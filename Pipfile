[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
django = "==3.2.3"
mysqlclient = "*"
djangorestframework = "==3.12.0"
markdown = "==3.3.4"
django-filter = "==2.4.0"
django-oauth-toolkit = "==1.4.1"
drf-yasg = "==1.20.0"
pyotp = "==2.6.0"
pillow = "==8.1.2"
django-countries = "==7.2.1"
django-cors-headers = "*"
sendgrid = "*"
django-environ = "==0.4.5"
django-redis = "==4.12.1"
num2words = "*"
xlwt = "==1.3.0"
celery = "==5.0.5"
redis = "*"
django-celery-beat = "==2.2.0"
xlsxwriter = "*"
qrcode = {extras = ["pil"], version = "*"}
fpdf = "*"
boto3 = "*"
django-crispy-forms = "*"
crispy-bootstrap4 = "*"
paystack = "*"
django-rest-paystack = "*"
django-storages = "*"
cryptography = "*"
django-extensions = "*"

[dev-packages]

[requires]
python_version = "3.9"
