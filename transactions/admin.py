from django.contrib import admin

from .models import AirtimeData, BillPayment, InternationalSendMoney, SendMoney, Collection

# Register your models here.
@admin.register(SendMoney)
class SendMoneyAdmin(admin.ModelAdmin):

    search_fields = [
        "user_ref",
        "transaction_ref",
        "response_code",
        "core_banking_ref",
        "nam_payment_reference",
        "sender__customer__username",
        "sender__customer__first_name",
        "sender__customer__last_name",
        "sender_account__account_number"
    ]
    list_filter = ["response_code", "status"]
    list_display = ("user_ref", "response_code", "status", "date_created")


@admin.register(AirtimeData)
class AirtimeDataAdmin(admin.ModelAdmin):

    search_fields = [
        "user_ref",
        "transaction_ref",
        "response_code",
        "sender__customer__username",
        "sender__customer__first_name",
        "sender__customer__last_name",
        "sender_account__account_number"
    ]
    list_filter = ["response_code", "status"]
    list_display = ("user_ref", "response_code", "status", "date_created")


@admin.register(BillPayment)
class BillPaymentAdmin(admin.ModelAdmin):

    search_fields = [
        "user_ref",
        "transaction_ref",
        "response_code",
        "sender__customer__username",
        "sender__customer__first_name",
        "sender__customer__last_name",
        "sender_account__account_number"
    ]
    list_filter = ["response_code", "status"]
    list_display = ("user_ref", "response_code", "status", "date_created")


@admin.register(InternationalSendMoney)
class InternationalSendMoneyAdmin(admin.ModelAdmin):

    search_fields = ["user_ref", "transaction_ref", "response_code"]
    list_filter = ["response_code", "status"]
    list_display = ("user_ref", "response_code", "status", "date_created")
    

@admin.register(Collection)
class CollectionAdmin(admin.ModelAdmin):

    search_fields = [
        "user_ref",
        "transaction_ref",
        "response_code",
        "core_banking_ref",
        "nam_payment_reference",
        "agent__user__username",
        "agent__user__first_name",
        "agent__user__last_name"
    ]
    list_filter = ["response_code", "status"]
    list_display = ("user_ref", "response_code", "status", "date_created")
