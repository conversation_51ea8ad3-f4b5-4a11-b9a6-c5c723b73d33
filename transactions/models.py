from dfa.models import Agent, AgentAccount
from bill_payment.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from common.functions import update_beneficiary
from common.validators import validate_phone_number
from customer.models import CustomerAccount, CustomerProfile
from user_profile.models import BankAdmin
from bank.models import Bank
from mfs.models import Countries, MobileWalletNetworks
import uuid
import logging

from django.db import models
from django.contrib.contenttypes.fields import GenericForeign<PERSON><PERSON>, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.db.models.signals import post_save
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Create your models here.

from common.models import CurrencyType, TransactionType


class Transaction(models.Model):
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    date_created = models.DateTimeField()

    class Meta:
        ordering = ['-date_created']
    def __str__(self):
        return "{0} - {1}".format(self.content_object.transaction_ref,
                                  self.date_created.date())


class SendMoney(models.Model):

    def __str__(self):
        return self.user_ref

    REVERSED = "R"
    FAILED = "F"
    PENDING = "P"
    COMPLETED = "S"

    TRANS_STATUS_CHOICES = (
        (COMPLETED, "Successful"),
        (REVERSED, "Reversed"),
        (FAILED, "Failed"),
        (PENDING, "Pending"),
    )

    user_ref = models.CharField(max_length=30, unique=True)
    transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    sender = models.ForeignKey(CustomerProfile, on_delete=models.CASCADE, blank=True, null=True)
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, blank=True, null=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    transaction_type = models.ForeignKey(TransactionType, on_delete=models.CASCADE, to_field='code', default='SEND_MONEY')
    channel = models.CharField(max_length=50, blank=True, null=True)
    currency = models.ForeignKey(CurrencyType, on_delete=models.CASCADE, null=True)
    sender_account_third_party = models.CharField(max_length=20, null=True, blank=True)
    # Field added to allow third party accounts as strings and not FK.
    sender_account = models.ForeignKey(CustomerAccount, on_delete=models.CASCADE, blank=True, null=True, to_field='account_number')
    agent_account = models.ForeignKey(AgentAccount, on_delete=models.CASCADE, blank=True, null=True, to_field='account_number')
    destination_account_number = models.CharField(max_length=20)
    destination_institution_code = models.CharField(max_length=20)
    destination_account_name = models.CharField(max_length=100)
    core_banking_ref = models.CharField(max_length=25, blank=True, null=True, db_index=True)
    response_code = models.CharField(max_length=5, blank=True)
    response_description = models.CharField(max_length=250, blank=True)
    remark = models.CharField(max_length=250, blank=True, null=True)
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True)
    nam_payment_reference = models.CharField(max_length=30, blank=True, null=True)

    fee = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    is_resolved = models.BooleanField(default=True)
    bill_ref = models.CharField(max_length=25, blank=True, null=True)
    is_transaction_chargable =models.BooleanField(default=False)
    
    status = models.CharField(
        max_length=2,
        choices=TRANS_STATUS_CHOICES,
        default=PENDING,
    )
    transact_relation = GenericRelation(Transaction,related_query_name='send_money')

    class Meta:
        db_table = 'omni_send_money'
        verbose_name = "Send Money "
        verbose_name_plural = "Send Money"
    

    def save(self, *args, **kwargs):
        # if transactions is successful update the beneficiary list
        if self.status == 'S':
            try:
                # Failure of this should not affect saving of this transaction
                update_beneficiary(
                    account_name=self.destination_account_name,
                    account_number=self.destination_account_number,
                    user=self.sender.customer,
                    beneficiary_bank_code=self.destination_institution_code
                )
            except Exception as e:
                log = logging.getLogger('django')
                log.info(f'Unable to update beneficiary list -> {e}')
        super().save(*args, **kwargs)


class AirtimeData(models.Model):
    def __str__(self):
        return self.user_ref

    REVERSED = "R"
    FAILED = "F"
    PENDING = "P"
    COMPLETED = "S"

    TRANS_STATUS_CHOICES = (
        (COMPLETED, "Successful"),
        (REVERSED, "Reversed"),
        (FAILED, "Failed"),
        (PENDING, "Pending"),
    )

    user_ref = models.CharField(max_length=30, unique=True)
    transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    sender = models.ForeignKey(
        CustomerProfile, on_delete=models.CASCADE, blank=True, null=True
    )
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE, blank=True, null=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default="0.00")
    transaction_type = models.ForeignKey(TransactionType, on_delete=models.CASCADE)
    channel = models.CharField(max_length=50, blank=True, null=True)
    currency = models.ForeignKey(CurrencyType, on_delete=models.CASCADE, null=True)
    phone_number = models.CharField(max_length=16, validators=[validate_phone_number])
    sender_account = models.ForeignKey(
        CustomerAccount, on_delete=models.CASCADE, blank=True, null=True
    )
    agent_account = models.ForeignKey(
        AgentAccount, on_delete=models.CASCADE, blank=True, null=True
    )
    biller = models.ForeignKey(AirtimeBiller, on_delete=models.CASCADE)
    payment_code = models.CharField(max_length=10, blank=True, null=True)
    core_banking_ref = models.CharField(
        max_length=25, blank=True, null=True, db_index=True
    )
    response_code = models.CharField(max_length=5, blank=True)
    response_description = models.CharField(max_length=250, blank=True)
    remark = models.CharField(max_length=250, blank=True, null=True)
    date_created = models.DateTimeField("date published", auto_now_add=True, blank=True)
    nam_payment_reference = models.CharField(max_length=30, blank=True, null=True)
    switch_ref = models.CharField(max_length=30, blank=True, null=True)

    fee = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    is_resolved = models.BooleanField(default=True)
    bill_ref = models.CharField(max_length=25, blank=True, null=True)
    is_transaction_chargable = models.BooleanField(default=False)

    status = models.CharField(
        max_length=2,
        choices=TRANS_STATUS_CHOICES,
        default=PENDING,
    )
    transact_relation = GenericRelation(Transaction, related_query_name="airtime_data")

    class Meta:
        db_table = "omni_airtime_data"
        verbose_name = "Airtime/Data"
        verbose_name_plural = "Airtime/Data"
    


class Utility(models.Model):

    def __str__(self):
        return self.user_ref

    REVERSED = 'R'
    DELETED = 'D'
    PENDING = 'P'
    COMPLETED = 'S'

    TRANS_STATUS_CHOICES = (
        (COMPLETED, 'Successful'),
        (REVERSED, 'Reversed'),
        (DELETED, 'Delete'),
        (PENDING, 'Pending'),
    )

    user_ref = models.CharField(max_length=30, unique=True)
    transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    sender = models.ForeignKey(CustomerProfile, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    fee = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    transaction_type = models.ForeignKey(TransactionType, on_delete=models.CASCADE)
    channel = models.CharField(max_length=50, blank=True, null=True)
    currency = models.ForeignKey(CurrencyType, on_delete=models.CASCADE, null=True)
    sender_account = models.ForeignKey(CustomerAccount, on_delete=models.CASCADE)
    biller_category =models.ForeignKey(BillerCategory, on_delete=models.CASCADE)
    biller_item = models.ForeignKey(Biller, on_delete=models.CASCADE)
    customer_filed_1 = models.CharField(max_length=200, blank=True, null=True)
    customer_filed_2 = models.CharField(max_length=200, blank=True, null=True)
    response_code = models.CharField(max_length=5, blank=True)
    response_description = models.CharField(max_length=250, blank=True)
    remark = models.CharField(max_length=250, blank=True, null=True)
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True)
    status = models.CharField(
        max_length=2,
        choices=TRANS_STATUS_CHOICES,
        default=PENDING,
    )
    transact_relation = GenericRelation(Transaction,related_query_name='utility')

    class Meta:
        db_table = 'omni_utility'
        verbose_name = "Utility"
        verbose_name_plural = "Utility"


class BillPayment(models.Model):

    def __str__(self):
        return self.user_ref

    REVERSED = "R"
    FAILED = "F"
    PENDING = "P"
    COMPLETED = "S"

    TRANS_STATUS_CHOICES = (
        (COMPLETED, "Successful"),
        (REVERSED, "Reversed"),
        (FAILED, "Failed"),
        (PENDING, "Pending"),
    )

    user_ref = models.CharField(max_length=30, unique=True)
    transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    sender = models.ForeignKey(CustomerProfile, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    transaction_type = models.ForeignKey(TransactionType, on_delete=models.CASCADE)
    channel = models.CharField(max_length=50, blank=True, null=True)
    currency = models.ForeignKey(CurrencyType, on_delete=models.CASCADE, null=True)
    customer_field_1 = models.CharField(max_length=200, blank=True, null=True)
    customer_field_2 = models.CharField(max_length=200, blank=True, null=True)
    sender_account = models.ForeignKey(CustomerAccount, on_delete=models.CASCADE)
    biller_category =models.ForeignKey(BillerCategory, on_delete=models.CASCADE)
    biller_item = models.ForeignKey(Biller, on_delete=models.CASCADE)
    payment_code = models.CharField(max_length=10, blank=True, null=True)
    core_banking_ref = models.CharField(max_length=25, blank=True, null=True, db_index=True)
    recharge_pin = models.CharField(max_length=50, blank=True)
    response_code = models.CharField(max_length=5, blank=True)
    response_description = models.CharField(max_length=250, blank=True)
    remark = models.CharField(max_length=250, blank=True, null=True)
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True)
    nam_payment_reference = models.CharField(max_length=30, blank=True, null=True)
    switch_ref = models.CharField(max_length=30, blank=True, null=True)

    fee = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    is_resolved = models.BooleanField(default=True)
    bill_ref = models.CharField(max_length=25, blank=True, null=True)
    is_transaction_chargable =models.BooleanField(default=False)
    
    status = models.CharField(
        max_length=2,
        choices=TRANS_STATUS_CHOICES,
        default=PENDING,
    )
    transact_relation = GenericRelation(Transaction,related_query_name='bill_payment')

    class Meta:
        db_table = 'omni_bill_payment'
        verbose_name = "Bill Payment"
        verbose_name_plural = "Bill Payments"
     

class Collection(models.Model):

    def __str__(self):
        return self.user_ref

    REVERSED = "R"
    FAILED = "F"
    PENDING = "P"
    COMPLETED = "S"
    
    AGENT_CREDIT = "AC"
    CUSTOMER_CREDIT = "CC"

    TRANS_STATUS_CHOICES = (
        (COMPLETED, "Successful"),
        (REVERSED, "Reversed"),
        (FAILED, "Failed"),
        (PENDING, "Pending"),
    )
    
    COLLECTION_TRANS_TYPE = (
        (AGENT_CREDIT, "Bank Admin Credit to Agent"), 
        (CUSTOMER_CREDIT, "Agent Credit to Customer")
    )

    user_ref = models.CharField(max_length=30, unique=True)
    transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE)
    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    transaction_type = models.ForeignKey(TransactionType, on_delete=models.CASCADE, to_field='code', default="COLLECTION")
    channel = models.CharField(max_length=50, blank=True, null=True)
    currency = models.ForeignKey(CurrencyType, on_delete=models.CASCADE, null=True)
    agent_account = models.ForeignKey(AgentAccount, on_delete=models.CASCADE, to_field='account_number')
    customer_account_number = models.CharField(max_length=20, null=True, blank=True)
    customer_account_name = models.CharField(max_length=100, null=True, blank=True)
    core_banking_ref = models.CharField(max_length=25, blank=True, null=True, db_index=True)
    response_code = models.CharField(max_length=5, blank=True)
    response_description = models.CharField(max_length=250, blank=True)
    remark = models.CharField(max_length=250, blank=True, null=True)
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True)
    nam_payment_reference = models.CharField(max_length=30, blank=True, null=True)
 
    status = models.CharField(
        max_length=2,
        choices=TRANS_STATUS_CHOICES,
        default=PENDING,
    )
    
    transact_relation = GenericRelation(Transaction, related_query_name='collection')

    class Meta:
        verbose_name = "Collection"
        verbose_name_plural = "Collections"
    

class InternationalSendMoney(models.Model):
    def __str__(self):
        return self.user_ref

    REVERSED = "R"
    FAILED = "F"
    PENDING = "P"
    COMPLETED = "S"

    TRANS_STATUS_CHOICES = (
        (COMPLETED, "Successful"),
        (REVERSED, "Reversed"),
        (FAILED, "Failed"),
        (PENDING, "Pending"),
    )

    MOBILE_WALLET = "mobile_wallet"
    BANK_ACCOUNT = "bank_account"
    CASH_PICKUP = "cash_pickup"

    PAYOUT_TYPE_CHOICES = (
        (MOBILE_WALLET, "Mobile Wallet"),
        (BANK_ACCOUNT, "Bank Account"),
        (CASH_PICKUP, "Cash Pickup"),
    )

    user_ref = models.CharField(max_length=30, unique=True)
    transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    sender = models.ForeignKey(CustomerProfile, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default="0.00")

    reciever_country = models.ForeignKey(Countries, on_delete=models.CASCADE)
    omni_reciever_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default="0.00"
    )
    real_reciever_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default="0.00"
    )
    exchange_rate = models.JSONField(default=dict, blank=True, null=True)
    exchange_rate_add_on = models.DecimalField(
        max_digits=10, decimal_places=2, default="0.00"
    )
    payout_type = models.CharField(max_length=15, choices=PAYOUT_TYPE_CHOICES)

    transaction_type = models.ForeignKey(TransactionType, on_delete=models.CASCADE)
    channel = models.CharField(max_length=50, blank=True, null=True)
    currency = models.ForeignKey(CurrencyType, on_delete=models.CASCADE, null=True)
    sender_account = models.ForeignKey(
        CustomerAccount, on_delete=models.CASCADE, to_field="account_number"
    )

    reciever_account_number = models.CharField(max_length=25, blank=True)
    reciever_account_name = models.CharField(max_length=100, blank=True)
    reciever_mfs_institution_code = models.CharField(max_length=10, blank=True)
    reciever_mobile_number = models.CharField(max_length=20, blank=True)
    reciever_mfs_mobile_wallet = models.ForeignKey(
        MobileWalletNetworks, on_delete=models.CASCADE, blank=True, null=True
    )
    reciever_name = models.CharField(max_length=100, blank=True)
    reciever_address = models.TextField(blank=True)
    reciever_email = models.EmailField(blank=True)

    core_banking_ref = models.CharField(
        max_length=25, blank=True, null=True, db_index=True
    )
    response_code = models.CharField(max_length=5, blank=True)
    response_description = models.CharField(max_length=250, blank=True)
    remark = models.CharField(max_length=250, blank=True, null=True)
    date_created = models.DateTimeField("date published", auto_now_add=True, blank=True)
    nam_payment_reference = models.CharField(max_length=30, blank=True, null=True)
    mfs_reference = models.CharField(max_length=30, blank=True, null=True)

    fee = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    is_resolved = models.BooleanField(default=True)
    bill_ref = models.CharField(max_length=25, blank=True, null=True)
    is_transaction_chargable = models.BooleanField(default=True)

    status = models.CharField(
        max_length=2,
        choices=TRANS_STATUS_CHOICES,
        default=PENDING,
    )
    transact_relation = GenericRelation(
        Transaction, related_query_name="international_send_money"
    )

    def clean_fields(self, exclude=None) -> None:
        super().clean_fields(exclude=exclude)
        if self.payout_type == "bank_account":
            # for payout option type bank account the following fields should be filled
            bank_dependencies = (
                (
                    "reciever_account_number",
                    self.reciever_account_number,
                    "Reciever Account Number",
                ),
                (
                    "reciever_account_name",
                    self.reciever_account_name,
                    "Reciever Account Name",
                ),
                (
                    "reciever_mfs_institution_code",
                    self.reciever_mfs_institution_code,
                    "Reciever MFS Institution Code",
                ),
            )
            for key, value, description in bank_dependencies:
                if not value:
                    raise ValidationError(
                        {
                            key: _(
                                f"{description} should be set for pay out type Bank Account"
                            )
                        }
                    )
        elif self.payout_type == "wallet":
            # for payout option type wallet the following fields should be filled
            wallet_dependencies = (
                (
                    "reciever_mobile_number",
                    self.reciever_mobile_number,
                    "Reciever Mobile Number",
                ),
            )
            for key, value, description in wallet_dependencies:
                if not value:
                    raise ValidationError(
                        {
                            key: _(
                                f"{description} should be set for pay out type Mobile Wallet"
                            )
                        }
                    )
        elif self.payout_type == "cash_pickup":
            # for payout option type cash pick up the following fields should be filled
            pick_up_dependencies = (
                ("reciever_name", self.reciever_name, "Reciever Name"),
                ("reciever_address", self.reciever_address, "Reciever Address"),
            )
            for key, value, description in pick_up_dependencies:
                if not value:
                    raise ValidationError(
                        {
                            key: _(
                                f"{description} should be set for pay out type Cash Pick Up"
                            )
                        }
                    )

    class Meta:
        db_table = "omni_international_send_money"
        verbose_name = "Send Money International"
        verbose_name_plural = "Send Money International"


class CoreBankingTransactions(models.Model):

    sender_account = models.ForeignKey(CustomerAccount, on_delete=models.CASCADE, blank=True, null=True)
    type = models.CharField(max_length=25, blank=True, null=True)
    payment_ref = models.CharField(max_length=30, blank=True, null=True)
    transaction_ref = models.CharField(max_length=30, blank=True, null=True)
    description = models.CharField(max_length=30, blank=True, null=True)
    amount = models.CharField(max_length=25, blank=True, null=True)
    narration = models.CharField(max_length=250, blank=True, null=True)
    core_banking_date = models.DateTimeField(blank=True, null=True)
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True)

    omni_channel_generated_transaction = models.ForeignKey(Transaction, on_delete=models.CASCADE, blank=True, null=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['sender_account', 'type', 'payment_ref', 'transaction_ref', 'description', 'amount', 'narration', 'core_banking_date'], name='unique_core_banking_transaction')
        ]
        
    # transact_relation = GenericRelation(Transaction,related_query_name='core_banking_transactions')


def create_transaction(sender, instance, created, **kwargs):
    """
    Post save handler to create/update transaction instances when 
    created or updated.
    """
    content_type = ContentType.objects.get_for_model(instance)
    try:
        transaction = Transaction.objects.get(content_type=content_type,
                             object_id=instance.id)
    except Transaction.DoesNotExist:
        transaction = Transaction(content_type=content_type, object_id=instance.id)
    transaction.date_created = instance.date_created
    transaction.save()


def update_customer_transaction_history(sender, instance, created, **kwargs):
    """
    for transaction with status not successful, connect the transaction to the
    core banking transaction. This is useful in generating transaction histroy
    for customers. if transaction status changes to SUCCESS and a copy of the
    transaction already exist on the core banking end, we delete that transaction
    and allow the core banking generate the transaction itself
    """
    if isinstance(instance.content_object, Collection):
        # TEMPORARY RESOLVE 
        return
    if (
        isinstance(instance.content_object, AirtimeData)
        or isinstance(instance.content_object, BillPayment)
        or isinstance(instance.content_object, InternationalSendMoney)
    ) and instance.content_object.status == "P":
        # edge case for this case, pending in either airtime data or bill payment means the
        # fee settlement account has been credited. in this situation the update core banking
        # transaction will take care of this so we need not do anything
        return
    if instance.content_object.status not in ("S", "R"):
        CoreBankingTransactions.objects.get_or_create(
            sender_account=instance.content_object.sender_account,
            omni_channel_generated_transaction=instance,
            transaction_ref="****************",
            amount=str(instance.content_object.amount),
        )
    else:
        try:
            CoreBankingTransactions.objects.get(
                sender_account=instance.content_object.sender_account,
                omni_channel_generated_transaction=instance,
                transaction_ref="****************",
                amount=str(instance.content_object.amount),
            ).delete()
        except Exception:
            pass


post_save.connect(create_transaction, sender=AirtimeData)
post_save.connect(create_transaction, sender=SendMoney)
post_save.connect(create_transaction, sender=BillPayment)
post_save.connect(create_transaction, sender=Collection)
post_save.connect(create_transaction, sender=InternationalSendMoney)
post_save.connect(update_customer_transaction_history, sender=Transaction)
# post_save.connect(create_transaction, sender=CoreBankingTransactions)