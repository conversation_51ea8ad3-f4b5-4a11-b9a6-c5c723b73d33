# Generated by Django 3.2.3 on 2024-04-10 22:51

import common.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bank', '0001_initial'),
        ('dfa', '0001_initial'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('common', '0001_initial'),
        ('mfs', '0001_initial'),
        ('bill_payment', '0001_initial'),
        ('customer', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Utility',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ref', models.CharField(max_length=30, unique=True)),
                ('transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('fee', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('channel', models.CharField(blank=True, max_length=50, null=True)),
                ('customer_filed_1', models.CharField(blank=True, max_length=200, null=True)),
                ('customer_filed_2', models.CharField(blank=True, max_length=200, null=True)),
                ('response_code', models.CharField(blank=True, max_length=5)),
                ('response_description', models.CharField(blank=True, max_length=250)),
                ('remark', models.CharField(blank=True, max_length=250, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='date published')),
                ('status', models.CharField(choices=[('S', 'Successful'), ('R', 'Reversed'), ('D', 'Delete'), ('P', 'Pending')], default='P', max_length=2)),
                ('biller_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bill_payment.billercategory')),
                ('biller_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bill_payment.biller')),
                ('currency', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='common.currencytype')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customerprofile')),
                ('sender_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customeraccount')),
                ('transaction_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.transactiontype')),
            ],
            options={
                'verbose_name': 'Utility',
                'verbose_name_plural': 'Utility',
                'db_table': 'omni_utility',
            },
        ),
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField()),
                ('date_created', models.DateTimeField()),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
            options={
                'ordering': ['-date_created'],
            },
        ),
        migrations.CreateModel(
            name='SendMoney',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ref', models.CharField(max_length=30, unique=True)),
                ('transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('channel', models.CharField(blank=True, max_length=50, null=True)),
                ('destination_account_number', models.CharField(max_length=20)),
                ('destination_institution_code', models.CharField(max_length=10)),
                ('destination_account_name', models.CharField(max_length=100)),
                ('core_banking_ref', models.CharField(blank=True, db_index=True, max_length=25, null=True)),
                ('response_code', models.CharField(blank=True, max_length=5)),
                ('response_description', models.CharField(blank=True, max_length=250)),
                ('remark', models.CharField(blank=True, max_length=250, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='date published')),
                ('nam_payment_reference', models.CharField(blank=True, max_length=30, null=True)),
                ('fee', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('is_resolved', models.BooleanField(default=True)),
                ('bill_ref', models.CharField(blank=True, max_length=25, null=True)),
                ('is_transaction_chargable', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('S', 'Successful'), ('R', 'Reversed'), ('F', 'Failed'), ('P', 'Pending')], default='P', max_length=2)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dfa.agent')),
                ('agent_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dfa.agentaccount', to_field='account_number')),
                ('currency', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='common.currencytype')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.customerprofile')),
                ('sender_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.customeraccount', to_field='account_number')),
                ('transaction_type', models.ForeignKey(default='SEND_MONEY', on_delete=django.db.models.deletion.CASCADE, to='common.transactiontype', to_field='code')),
            ],
            options={
                'verbose_name': 'Send Money ',
                'verbose_name_plural': 'Send Money',
                'db_table': 'omni_send_money',
            },
        ),
        migrations.CreateModel(
            name='InternationalSendMoney',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ref', models.CharField(max_length=30, unique=True)),
                ('transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('omni_reciever_amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('real_reciever_amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('exchange_rate', models.JSONField(blank=True, default=dict, null=True)),
                ('exchange_rate_add_on', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('payout_type', models.CharField(choices=[('mobile_wallet', 'Mobile Wallet'), ('bank_account', 'Bank Account'), ('cash_pickup', 'Cash Pickup')], max_length=15)),
                ('channel', models.CharField(blank=True, max_length=50, null=True)),
                ('reciever_account_number', models.CharField(blank=True, max_length=25)),
                ('reciever_account_name', models.CharField(blank=True, max_length=100)),
                ('reciever_mfs_institution_code', models.CharField(blank=True, max_length=10)),
                ('reciever_mobile_number', models.CharField(blank=True, max_length=20)),
                ('reciever_name', models.CharField(blank=True, max_length=100)),
                ('reciever_address', models.TextField(blank=True)),
                ('reciever_email', models.EmailField(blank=True, max_length=254)),
                ('core_banking_ref', models.CharField(blank=True, db_index=True, max_length=25, null=True)),
                ('response_code', models.CharField(blank=True, max_length=5)),
                ('response_description', models.CharField(blank=True, max_length=250)),
                ('remark', models.CharField(blank=True, max_length=250, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='date published')),
                ('nam_payment_reference', models.CharField(blank=True, max_length=30, null=True)),
                ('mfs_reference', models.CharField(blank=True, max_length=30, null=True)),
                ('fee', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('is_resolved', models.BooleanField(default=True)),
                ('bill_ref', models.CharField(blank=True, max_length=25, null=True)),
                ('is_transaction_chargable', models.BooleanField(default=True)),
                ('status', models.CharField(choices=[('S', 'Successful'), ('R', 'Reversed'), ('F', 'Failed'), ('P', 'Pending')], default='P', max_length=2)),
                ('currency', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='common.currencytype')),
                ('reciever_country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mfs.countries')),
                ('reciever_mfs_mobile_wallet', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='mfs.mobilewalletnetworks')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customerprofile')),
                ('sender_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customeraccount', to_field='account_number')),
                ('transaction_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.transactiontype')),
            ],
            options={
                'verbose_name': 'Send Money International',
                'verbose_name_plural': 'Send Money International',
                'db_table': 'omni_international_send_money',
            },
        ),
        migrations.CreateModel(
            name='CoreBankingTransactions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(blank=True, max_length=25, null=True)),
                ('payment_ref', models.CharField(blank=True, max_length=30, null=True)),
                ('transaction_ref', models.CharField(blank=True, max_length=30, null=True)),
                ('description', models.CharField(blank=True, max_length=30, null=True)),
                ('amount', models.CharField(blank=True, max_length=25, null=True)),
                ('narration', models.CharField(blank=True, max_length=250, null=True)),
                ('core_banking_date', models.DateTimeField(blank=True, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='date published')),
                ('omni_channel_generated_transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='transactions.transaction')),
                ('sender_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.customeraccount')),
            ],
        ),
        migrations.CreateModel(
            name='Collection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ref', models.CharField(max_length=30, unique=True)),
                ('transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('channel', models.CharField(blank=True, max_length=50, null=True)),
                ('customer_account_number', models.CharField(blank=True, max_length=20, null=True)),
                ('customer_account_name', models.CharField(blank=True, max_length=100, null=True)),
                ('core_banking_ref', models.CharField(blank=True, db_index=True, max_length=25, null=True)),
                ('response_code', models.CharField(blank=True, max_length=5)),
                ('response_description', models.CharField(blank=True, max_length=250)),
                ('remark', models.CharField(blank=True, max_length=250, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='date published')),
                ('nam_payment_reference', models.CharField(blank=True, max_length=30, null=True)),
                ('status', models.CharField(choices=[('S', 'Successful'), ('R', 'Reversed'), ('F', 'Failed'), ('P', 'Pending')], default='P', max_length=2)),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dfa.agent')),
                ('agent_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dfa.agentaccount', to_field='account_number')),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bank')),
                ('currency', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='common.currencytype')),
                ('transaction_type', models.ForeignKey(default='COLLECTION', on_delete=django.db.models.deletion.CASCADE, to='common.transactiontype', to_field='code')),
            ],
            options={
                'verbose_name': 'Collection',
                'verbose_name_plural': 'Collections',
            },
        ),
        migrations.CreateModel(
            name='BillPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ref', models.CharField(max_length=30, unique=True)),
                ('transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('channel', models.CharField(blank=True, max_length=50, null=True)),
                ('customer_field_1', models.CharField(blank=True, max_length=200, null=True)),
                ('customer_field_2', models.CharField(blank=True, max_length=200, null=True)),
                ('payment_code', models.CharField(blank=True, max_length=10, null=True)),
                ('core_banking_ref', models.CharField(blank=True, db_index=True, max_length=25, null=True)),
                ('recharge_pin', models.CharField(blank=True, max_length=50)),
                ('response_code', models.CharField(blank=True, max_length=5)),
                ('response_description', models.CharField(blank=True, max_length=250)),
                ('remark', models.CharField(blank=True, max_length=250, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='date published')),
                ('nam_payment_reference', models.CharField(blank=True, max_length=30, null=True)),
                ('switch_ref', models.CharField(blank=True, max_length=30, null=True)),
                ('fee', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('is_resolved', models.BooleanField(default=True)),
                ('bill_ref', models.CharField(blank=True, max_length=25, null=True)),
                ('is_transaction_chargable', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('S', 'Successful'), ('R', 'Reversed'), ('F', 'Failed'), ('P', 'Pending')], default='P', max_length=2)),
                ('biller_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bill_payment.billercategory')),
                ('biller_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bill_payment.biller')),
                ('currency', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='common.currencytype')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customerprofile')),
                ('sender_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customeraccount')),
                ('transaction_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.transactiontype')),
            ],
            options={
                'verbose_name': 'Bill Payment',
                'verbose_name_plural': 'Bill Payments',
                'db_table': 'omni_bill_payment',
            },
        ),
        migrations.CreateModel(
            name='AirtimeData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ref', models.CharField(max_length=30, unique=True)),
                ('transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('channel', models.CharField(blank=True, max_length=50, null=True)),
                ('phone_number', models.CharField(max_length=16, validators=[common.validators.validate_phone_number])),
                ('payment_code', models.CharField(blank=True, max_length=10, null=True)),
                ('core_banking_ref', models.CharField(blank=True, db_index=True, max_length=25, null=True)),
                ('response_code', models.CharField(blank=True, max_length=5)),
                ('response_description', models.CharField(blank=True, max_length=250)),
                ('remark', models.CharField(blank=True, max_length=250, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='date published')),
                ('nam_payment_reference', models.CharField(blank=True, max_length=30, null=True)),
                ('switch_ref', models.CharField(blank=True, max_length=30, null=True)),
                ('fee', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('is_resolved', models.BooleanField(default=True)),
                ('bill_ref', models.CharField(blank=True, max_length=25, null=True)),
                ('is_transaction_chargable', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('S', 'Successful'), ('R', 'Reversed'), ('F', 'Failed'), ('P', 'Pending')], default='P', max_length=2)),
                ('agent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dfa.agent')),
                ('agent_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dfa.agentaccount')),
                ('biller', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bill_payment.airtimebiller')),
                ('currency', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='common.currencytype')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.customerprofile')),
                ('sender_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='customer.customeraccount')),
                ('transaction_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.transactiontype')),
            ],
            options={
                'verbose_name': 'Airtime/Data',
                'verbose_name_plural': 'Airtime/Data',
                'db_table': 'omni_airtime_data',
            },
        ),
        migrations.AddConstraint(
            model_name='corebankingtransactions',
            constraint=models.UniqueConstraint(fields=('sender_account', 'type', 'payment_ref', 'transaction_ref', 'description', 'amount', 'narration', 'core_banking_date'), name='unique_core_banking_transaction'),
        ),
    ]
