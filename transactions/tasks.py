import logging

from logic_omnichannel.celery import app
from django.utils import timezone
from datetime import timedelta

from transactions.models import AirtimeData, BillPayment, InternationalSendMoney
from common.functions import account_settlement_reversal, update_daily_cummulative
from common.models import SettlementAccount
from common.services import (
    get_mfs_transaction_status,
    send_money,
    bill_payment_advice_get_tsq,
)
# from logic_omnichannel import settings
from django.conf import settings


@app.task(name="resolve_pending_airtime_settlement_reversals")
def airtime_settlement_reversals():
    """
    checks for pending reversals pending for more than an hour for airtime transactions checks
    for their transaction status and tries to reverse them if indeed they failed and if they
    were successful, it updates the status as successful.
    """
    log = logging.getLogger("django")
    log.info("FROM CELERY -> Running airtime settlement reversals")

    pending = AirtimeData.objects.filter(status="P")
    now = timezone.now()
    log.info(f"FROM CELERY -> {len(pending)} pending airtime reversals")
    for transaction in pending:
        if (
            transaction.date_created + timedelta(hours=1)
        ) <= now and transaction.date_created > (now - timedelta(days=30)):
            # Process transactions in the last 30 days and also 1 hour from the current time
            try:
                response_code, _ = bill_payment_advice_get_tsq(
                    transaction.core_banking_ref, "airtime_data"
                )
                if response_code == settings.SUCCESS_RESPONSE:
                    transaction.response_code = settings.SUCCESS_RESPONSE
                    transaction.response_description = "AIRTIME/DATA SUCCESSFUL"
                    transaction.status = "S"
                    transaction.is_resolved = False
                    transaction.is_transaction_chargable = True
                    transaction.save()

                    update_daily_cummulative(
                        transaction.amount, transaction.sender_account
                    )
                    log.info(
                        f"FROM CELERY -> Transaction with user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} has been successfully resolved to SUCCESS"
                    )
                elif (
                    response_code != None
                    and response_code != ""
                    and response_code not in ["21", "29", "57", "61", "17"]
                ):
                    # at this point we are getting a response code but it not successfull but it is a valid response code
                    sender_account = transaction.sender_account
                    settlement_account = SettlementAccount.objects.get(
                        transaction_type__code="AIRTIME_DATA", bank=sender_account.bank
                    )
                    amount = str(transaction.amount)
                    app_code = sender_account.profile.customer.app_code
                    account_settlement_reversal(
                        sender_account,
                        amount,
                        settlement_account,
                        app_code,
                        transaction,
                        send_money,
                        narration=f"AIRTIME REVERSAL {transaction.amount}",
                    )
                    log.info(
                        f"FROM CELERY -> Transaction with user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} has been successfully resolved to FAILED"
                    )
                else:
                    log.info(
                        f"FROM CELERY -> Transaction with user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} has been left unresolved and LEFT as PENDING"
                    )
                    # in future consider having a notification scheme for when at least a certain number of transactions are pending to notify the admin
            except Exception as e:
                log.info(
                    f"FROM CELERY -> Unable to complete reversals for user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} ERROR -> {e}"
                )


@app.task(name="resolve_pending_bill_payment_settlement_reversals")
def bill_payment_settlement_reversals():
    """
    checks for pending reversals pending for more than an hour for bill payment transactions checks
    for their transaction status and tries to reverse them if indeed they failed and if they were
    successful, it updates the status as successful.
    """
    log = logging.getLogger("django")
    log.info("FROM CELERY -> Running bill payment settlement reversals")

    pending = BillPayment.objects.filter(status="P")
    now = timezone.now()
    log.info(f"FROM CELERY -> {len(pending)} pending bill payment reversals")
    for transaction in pending:
        if (
            transaction.date_created + timedelta(hours=1)
        ) <= now and transaction.date_created > (now - timedelta(days=30)):
            # Process transactions in the last 30 days and also 1 hour from the current time
            try:
                response_code, recharge_pin = bill_payment_advice_get_tsq(
                    transaction.core_banking_ref, "bill_payment"
                )
                if response_code == settings.SUCCESS_RESPONSE:
                    transaction.response_code = settings.SUCCESS_RESPONSE
                    transaction.response_description = "BILL PAYMENT SUCCESSFUL"
                    transaction.status = "S"
                    transaction.is_resolved = False
                    transaction.is_transaction_chargable = True
                    transaction.recharge_pin = recharge_pin if recharge_pin else ""
                    transaction.save()

                    update_daily_cummulative(
                        transaction.amount, transaction.sender_account
                    )
                    log.info(
                        f"FROM CELERY -> Transaction with user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} has been successfully resolved to SUCCESS"
                    )

                elif (
                    response_code != None
                    and response_code != ""
                    and response_code not in ["21", "29", "57", "61", "17"]
                ):
                    # at this point we are getting a response code but it not successfull but it is a valid response code
                    sender_account = transaction.sender_account
                    settlement_account = SettlementAccount.objects.get(
                        transaction_type__code="BILL_PAYMENT", bank=sender_account.bank
                    )
                    amount = str(transaction.amount + transaction.fee)
                    app_code = sender_account.profile.customer.app_code
                    account_settlement_reversal(
                        sender_account,
                        amount,
                        settlement_account,
                        app_code,
                        transaction,
                        send_money,
                        narration=f"BILLPAYMENT REVERSAL {transaction.amount}",
                    )
                    log.info(
                        f"FROM CELERY -> Transaction with user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} has been successfully resolved to FAILED"
                    )
                else:
                    log.info(
                        f"FROM CELERY -> Transaction with user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} has been left unresolved and LEFT as PENDING"
                    )
                    # in future consider having a notification scheme for when at least a certain number of transactions are pending to notify the admin
            except Exception as e:
                log.info(
                    f"FROM CELERY -> Unable to complete reversals for user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} ERROR -> {e}"
                )


@app.task(name="resolve_international_send_money_transactions")
def resolve_international_send_money_transactions():
    """
    checks for pending international send money transactions and verify their state from
    the get transaction status, if it is still pending, do nothing, but if it is successful
    update the transaction status and if it were failed, reverse customer monies
    """
    log = logging.getLogger("django")
    log.info("FROM CELERY -> Running International Send Money Transactions Resolution")

    pending_transactions = InternationalSendMoney.objects.filter(status="P")
    log.info(
        f"FROM CELERY -> {len(pending_transactions)} pending International Send Money Transactions"
    )
    for transaction in pending_transactions:
        try:
            if (transaction.date_created + timedelta(minutes=10)) < timezone.now():
                if transaction.reciever_mfs_mobile_wallet:
                    # this is a mobile money transaction type
                    status = get_mfs_transaction_status(
                        transaction.mfs_reference, check_level=1
                    )
                else:
                    # this is a regular bank account transaction type
                    status = get_mfs_transaction_status(transaction.mfs_reference)

                if status == "success":
                    transaction.status = "S"
                    transaction.response_code = settings.SUCCESS_RESPONSE
                    transaction.response_description = "SEND MONEY SUCCESSFUL"
                    transaction.save()

                    update_daily_cummulative(
                        transaction.amount, transaction.sender_account
                    )

                    log.info(
                        f"FROM CELERY -> Transaction with user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} has been successfully resolved to SUCCESS"
                    )
                elif status == "failed":
                    sender_account = transaction.sender_account
                    settlement_account = SettlementAccount.objects.get(
                        transaction_type__code="international_send_money",
                        bank=sender_account.bank,
                    )
                    total_amount = (
                        transaction.amount
                        + transaction.exchange_rate_add_on
                        + transaction.fee
                    )
                    app_code = sender_account.profile.customer.app_code
                    account_settlement_reversal(
                        sender_account=sender_account,
                        amount=total_amount,
                        settlement_account=settlement_account,
                        app_code=app_code,
                        send_money=send_money,
                        narration=f"I_SEND_MONEY REVERSAL {transaction.amount}",
                        transaction=transaction,
                    )
                    log.info(
                        f"FROM CELERY -> Transaction with user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} has been successfully resolved to FAILED"
                    )
        except Exception as e:
            log.info(
                f"FROM CELERY -> Unable to resolve International Send Money for user ref {transaction.user_ref} and transaction ref {transaction.transaction_ref} ERROR -> {e}"
            )
