import logging

from django.contrib.contenttypes.models import ContentType
from transactions.models import Transaction
from customer.serializers import GetAccountTransactionSerializer


def get_transaction_details(instance):
    '''
    instance is either send money, airtime-data, bill payment or international transfers
    we parse it into the get transactions details serializer to retrieve the data for the transaction
    and we return it.
    '''
    log = logging.getLogger('django')

    try:
        content_type = ContentType.objects.get_for_model(instance)
        transaction = Transaction.objects.get(content_type=content_type, object_id=instance.id)
        serializer = GetAccountTransactionSerializer(transaction)
        return serializer.data
    except Transaction.DoesNotExist:
        # do not break the code
        log.info(f'From Get Transaction Details -> Transaction does not exist for {instance}')
        return {}
    except Exception as e:
        log.info(f'Error while retrieving transaction details {e}')
        # do not break the code
        return {}
