from common.functions import get_amount_in_words, get_bank_name_by_code
from mfs.models import Countries, MobileWalletNetworks
from bill_payment.models import <PERSON>timeB<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from customer.models import CustomerAccount
from common.models import CurrencyType, TransactionType
from dfa.models import Agent, AgentAccount
from bank.models import Bank
from common.validators import validate_numbers, validate_phone_number
from rest_framework import serializers

import logging

from .models import AirtimeData, BillPayment, InternationalSendMoney, SendMoney, Utility, Collection


class SendMoneySerializer(serializers.ModelSerializer):
    
    pin = serializers.CharField(
        min_length=4, write_only=True, allow_blank=True, required=False
    )
    sender_institution_code = serializers.CharField(min_length=1, write_only=True)
    
    class Meta:
        model = SendMoney
        fields = ['pin','user_ref', 'amount', 'currency', 'sender_account', 'agent_account', 'sender_institution_code', 
        'destination_account_number', 'destination_institution_code', 'destination_account_name', 'remark']
    
    def create(self, validated_data):
        '''
            Remove the pin and sender_institution_code field before running model create
        '''
        _validated_data = validated_data
        _validated_data.pop('pin', None)
        _validated_data.pop('sender_institution_code')
        return super().create(_validated_data)


class AirtimeDataSerializer(serializers.ModelSerializer):
    pin = serializers.CharField(min_length=4, write_only=True, validators=[validate_numbers])
    sender_institution_code = serializers.CharField(min_length=1, write_only=True)
    biller_code = serializers.CharField(min_length=1)
    payment_code = serializers.CharField(min_length=1)
    currency = serializers.IntegerField()
    sender_account = serializers.CharField(min_length=5, max_length=15, validators=[validate_numbers])
    

    class Meta:
        model = AirtimeData
        fields = ['pin','user_ref', 'amount', 'currency', 'sender_account', 'sender_institution_code',  'phone_number',
        'biller_code', 'payment_code', 'remark']
    
    def validate(self, attrs):
        """
        resolve the foriegn key dependecies at this point because for some wierd reason mysql threw error while
        mapping to desired model field which is supposed to self validate
        """
        validated_data = super().validate(attrs)
        currency = validated_data.get("currency")
        biller_code = validated_data.get("biller_code")
        try:
            currency = CurrencyType.objects.get(id=currency)
            biller = AirtimeBiller.objects.get(biller_code=biller_code)
            _transaction_type = TransactionType.objects.get(code="AIRTIME_DATA")
        except CurrencyType.DoesNotExist:
            raise serializers.ValidationError(
                {"currency": "Currency Type Does Not Exist"}
            )
        except AirtimeBiller.DoesNotExist:
            raise serializers.ValidationError(
                {"biller_code": "Airtime/Data Biller Does Not Exist"}
            )

        validated_data["currency"] = currency
        validated_data["biller"] = biller
        validated_data["transaction_type"] = _transaction_type

        return validated_data
        
    
    def create(self, validated_data):
        '''
            Pop every extra field that is not required for running model create
        '''
        _validated_data = validated_data
        _validated_data.pop('pin', None)
        _validated_data.pop('sender_institution_code')
        _validated_data.pop('biller_code')
        return super().create(_validated_data)
    

class BillPaymentSerializer(serializers.ModelSerializer):

    pin = serializers.CharField(min_length=4, write_only=True, validators=[validate_numbers])
    sender_institution_code = serializers.CharField(min_length=1, write_only=True)
    biller_category_code = serializers.CharField(min_length=1)
    biller_code = serializers.CharField(min_length=1)
    payment_code = serializers.CharField(min_length=1)
    currency = serializers.IntegerField()
    sender_account = serializers.CharField(min_length=5, max_length=15, validators=[validate_numbers])

    class Meta:
        model = BillPayment
        fields = ['pin','user_ref', 'amount', 'currency', 'sender_account', 'sender_institution_code', 'customer_field_1',
        'customer_field_2', 'biller_category_code', 'biller_code', 'payment_code','remark']
        
    def validate(self, attrs):
        '''
            resolve the foriegn key dependecies at this point because for some wierd reason mysql threw error while
            mapping to desired model field which is supposed to self validate
        '''
        validated_data = super().validate(attrs)
        currency = validated_data.get('currency')
        sender_account = validated_data.get('sender_account')
        biller_category_code = validated_data.get('biller_category_code')
        biller_code = validated_data.get('biller_code')
        try:
            currency = CurrencyType.objects.get(id=currency)
            sender_account = CustomerAccount.objects.get(account_number=sender_account)
            biller_category = BillerCategory.objects.get(code=biller_category_code)
            biller_item = Biller.objects.get(biller_code=biller_code)
            _transaction_type = TransactionType.objects.get(code='BILL_PAYMENT')
        except CurrencyType.DoesNotExist:
            raise serializers.ValidationError({
                'currency': 'Currency Type Does Not Exist'
            })
        except CustomerAccount.DoesNotExist:
            raise serializers.ValidationError({
                'sender_account': 'Account Number Does Not Exist'
            })
        except BillerCategory.DoesNotExist:
            raise serializers.ValidationError({
                'biller_category_code' : 'Biller Category Does Not Exist'
            })
        except Biller.DoesNotExist:
            raise serializers.ValidationError({
                'biller_item_code' : 'Biller Item Does Not Exist'
            })
        
        validated_data['currency'] = currency
        validated_data['sender_account'] = sender_account
        validated_data['biller_category'] = biller_category
        validated_data['biller_item'] = biller_item
        validated_data['transaction_type'] = _transaction_type

        return validated_data
        
    
    def create(self, validated_data):
        '''
            Pop every extra field that is not required for running model create
        '''
        _validated_data = validated_data
        _validated_data.pop('pin', None)
        _validated_data.pop('sender_institution_code')
        _validated_data.pop('biller_category_code')
        _validated_data.pop('biller_code')
        return super().create(_validated_data)
    

class CollectionSerializer(serializers.ModelSerializer):
    
    institution_code = serializers.CharField(min_length=1, write_only=True)
    pin = serializers.CharField(
        min_length=4, write_only=True, allow_blank=True, required=False
    )
    
    class Meta:
        model = Collection
        fields = [
            'pin', 'user_ref', 'amount', 'agent_account', 'currency',
            'institution_code', 'remark', 'customer_account_number'
        ]
        
    def create(self, validated_data):
        '''
           Runs the creation of the model instance.
        '''
        _validated_data = validated_data
        _validated_data.pop('pin', None)
        _validated_data.pop('institution_code')
        return super().create(_validated_data)



class InternationalBankAccountSendMoneySerializer(serializers.ModelSerializer):

    pin = serializers.CharField(
        min_length=4, write_only=True, validators=[validate_numbers]
    )
    currency = serializers.IntegerField()

    sender_institution_code = serializers.CharField(min_length=1, write_only=True)

    reciever_country_code = serializers.CharField(min_length=2)
    reciever_first_name = serializers.CharField(min_length=1)
    reciever_last_name = serializers.CharField(min_length=1)

    class Meta:
        model = InternationalSendMoney
        fields = [
            "pin",
            "user_ref",
            "amount",
            "currency",
            "sender_account",
            "sender_institution_code",
            "reciever_country_code",
            "reciever_account_number",
            "reciever_mfs_institution_code",
            "reciever_first_name",
            "reciever_last_name",
            "reciever_address",
            "reciever_mobile_number",
        ]

    def validate(self, attrs):
        """
        resolve the foriegn key dependecies at this point because for some wierd reason mysql threw error while
        mapping to desired model field which is supposed to self validate
        """
        validated_data = super().validate(attrs)
        currency = validated_data.get("currency")
        sender_account = validated_data.get("sender_account")
        reciever_country_code = validated_data.get("reciever_country_code")
        try:
            currency = CurrencyType.objects.get(id=currency)
            sender_account = CustomerAccount.objects.get(account_number=sender_account)
            reciever_country = Countries.objects.get(country_code=reciever_country_code)
            _transaction_type = TransactionType.objects.get(
                code="international_send_money"
            )
        except CurrencyType.DoesNotExist:
            raise serializers.ValidationError(
                {"currency": "Currency Type Does Not Exist"}
            )
        except CustomerAccount.DoesNotExist:
            raise serializers.ValidationError(
                {"sender_account": "Account Number Does Not Exist"}
            )
        except Countries.DoesNotExist:
            raise serializers.ValidationError(
                {
                    "reciever_country_code": f"Country with code {reciever_country_code} Does Not Exist"
                }
            )

        validated_data["currency"] = currency
        validated_data["sender_account"] = sender_account
        validated_data["reciever_country"] = reciever_country
        validated_data["transaction_type"] = _transaction_type
        validated_data[
            "reciever_account_name"
        ] = f'{validated_data["reciever_first_name"]} {validated_data["reciever_last_name"]}'
        validated_data["reciever_name"] = validated_data["reciever_account_name"]

        return validated_data

    def create(self, validated_data):
        """
        Remove the pin and sender_institution_code field before running model create
        """
        _validated_data = validated_data

        _validated_data.pop("pin", None)
        _validated_data.pop("sender_institution_code")
        _validated_data.pop("reciever_country_code")
        _validated_data.pop("reciever_first_name")
        _validated_data.pop("reciever_last_name")

        log = logging.getLogger("django")

        log.info(f"{_validated_data} for saving into international send money")

        return super().create(_validated_data)


class InternationalMobileMoneySendMoneySerializer(serializers.ModelSerializer):

    pin = serializers.CharField(
        min_length=4, write_only=True, validators=[validate_numbers]
    )
    sender_institution_code = serializers.CharField(min_length=1, write_only=True)
    reciever_country_code = serializers.CharField(min_length=2)
    reciever_mfs_mobile_wallet_code = serializers.CharField(min_length=1)
    currency = serializers.IntegerField()
    reciever_first_name = serializers.CharField(min_length=1)
    reciever_last_name = serializers.CharField(min_length=1)

    class Meta:
        model = InternationalSendMoney
        fields = fields = [
            "pin",
            "user_ref",
            "amount",
            "currency",
            "sender_account",
            "sender_institution_code",
            "reciever_country_code",
            "exchange_rate",
            "reciever_mobile_number",
            "reciever_mfs_mobile_wallet_code",
            "remark",
            "reciever_first_name",
            "reciever_last_name",
        ]

    def validate(self, attrs):
        """
        resolve the foriegn key dependecies at this point because for some wierd reason mysql threw error while
        mapping to desired model field which is supposed to self validate
        """
        validated_data = super().validate(attrs)
        currency = validated_data.get("currency")
        sender_account = validated_data.get("sender_account")
        reciever_country_code = validated_data.get("reciever_country_code")
        reciever_mfs_mobile_wallet_code = validated_data.get(
            "reciever_mfs_mobile_wallet_code"
        )
        try:
            currency = CurrencyType.objects.get(id=currency)
            sender_account = CustomerAccount.objects.get(account_number=sender_account)
            reciever_country = Countries.objects.get(country_code=reciever_country_code)
            reciever_mfs_mobile_wallet = MobileWalletNetworks.objects.get(
                country_network_code=reciever_mfs_mobile_wallet_code
            )
            _transaction_type = TransactionType.objects.get(
                code="INTERNATIONAL_SEND_MONEY"
            )
        except CurrencyType.DoesNotExist:
            raise serializers.ValidationError(
                {"currency": "Currency Type Does Not Exist"}
            )
        except CustomerAccount.DoesNotExist:
            raise serializers.ValidationError(
                {"sender_account": "Account Number Does Not Exist"}
            )
        except Countries.DoesNotExist:
            raise serializers.ValidationError(
                {
                    "reciever_country_code": f"Country with code {reciever_country_code} Does Not Exist"
                }
            )
        except MobileWalletNetworks.DoesNotExist:
            raise serializers.ValidationError(
                {
                    "reciever_mfs_mobile_wallet_code": f"MFS mobile wallet code {reciever_mfs_mobile_wallet_code} Does Not Exist"
                }
            )

        if not validated_data["reciever_mobile_number"]:
            raise serializers.ValidationError(
                {"reciever_mobile_number": "reciever_mobile_number is required"}
            )

        validated_data["currency"] = currency
        validated_data["sender_account"] = sender_account
        validated_data["reciever_country"] = reciever_country
        validated_data["transaction_type"] = _transaction_type
        validated_data["reciever_mfs_mobile_wallet"] = reciever_mfs_mobile_wallet
        validated_data[
            "reciever_account_name"
        ] = f'{validated_data["reciever_first_name"]} {validated_data["reciever_last_name"]}'
        validated_data["reciever_name"] = validated_data["reciever_account_name"]
        return validated_data

    def create(self, validated_data):
        """
        Remove the pin and sender_institution_code field before running model create
        """
        _validated_data = validated_data
        _validated_data.pop("pin", None)
        _validated_data.pop("sender_institution_code")
        _validated_data.pop("reciever_country_code")
        _validated_data.pop("reciever_mfs_mobile_wallet_code")
        _validated_data.pop("reciever_first_name")
        _validated_data.pop("reciever_last_name")
        return super().create(_validated_data)


class TransactionSummarySerializer(serializers.Serializer):

    transaction_details = serializers.SerializerMethodField("get_transaction_details")
    beneficiary_details = serializers.SerializerMethodField("get_beneficiary_details")
    sender_details = serializers.SerializerMethodField("get_sender_details")

    class Meta:
        fields = ["transaction_details", "beneficiary_details", "sender_details"]

    def get_transaction_details(self, obj):
        # transaction details for object with content object which could be send money, airtime or bill payment or international send money
        transaction = obj.content_object
        return {
            "transaction_date": transaction.date_created,
            "transaction_class": "DEBIT",
            "transaction_type": transaction.transaction_type.name,
            "tsq_type": transaction.transaction_type.code,
            "transaction_status": transaction.status,
            "amount": transaction.amount,
            "amount_in_words": get_amount_in_words(f"{transaction.amount}"),
            "transaction_description": transaction.response_description,
            "transaction_narration": transaction.remark,
            "customer_reference": transaction.user_ref,
            "channel": transaction.channel,
        }

    def get_beneficiary_details(self, obj):
        # beneficiary details for object with content object which could be send money, airtime or bill payment
        transaction = obj.content_object
        if isinstance(transaction, SendMoney):
            return {
                "beneficiary_account_number": transaction.destination_account_number,
                "beneficiary_name": transaction.destination_account_name,
                "beneficiary_bank": get_bank_name_by_code(
                    transaction.destination_institution_code
                ),
            }
        elif isinstance(transaction, AirtimeData):
            return {
                "biller": transaction.biller.biller_name,
                "credited_phone_number": transaction.phone_number,
            }
        elif isinstance(transaction, BillPayment):
            return {
                "biller": transaction.biller_item.biller_name,
                "category": transaction.biller_category.name,
                "beneficiary_unique_number": transaction.customer_field_1,
                "beneficiary_name": transaction.customer_field_2,
                "recharge_pin": transaction.recharge_pin,
            }
        elif isinstance(transaction, InternationalSendMoney):
            details = {}
            if transaction.payout_type == "bank_account":
                details = {
                    "payout_type": transaction.payout_type,
                    "beneficiary_account_number": transaction.reciever_account_number,
                    "beneficiary_name": transaction.reciever_account_name,
                    "beneficiary_bank": transaction.reciever_mfs_institution_code,
                    "beneficiary_country": transaction.reciever_country.name,
                    "recieved_amount_in_beneficiary_currency": transaction.omni_reciever_amount,
                }
            return details

    def get_sender_details(self, obj):
        # sender details for object with content object which could be send money, airtime or bill payment
        transaction = obj.content_object
        if isinstance(transaction, SendMoney):
            if transaction.channel == "DFA OAuth":
                return {
                    "agent_account": transaction.agent_account.account_number,
                    "agent_name": transaction.agent.get_full_name(),
                    "agent_bank": transaction.agent.bank.name,
                }
            elif transaction.channel.upper().startswith("3RD-PARTY"):
                return {
                    "sender_account_number": transaction.sender_account_third_party,
                    "sender_bank":  get_bank_name_by_code(
                        transaction.destination_institution_code),
                }
            else:
                return {
                        "sender_account_number": transaction.sender_account.account_number,
                        "sender_name": transaction.sender_account.profile.customer.get_full_name(),
                        "sender_bank": transaction.sender_account.bank.name,
                    }
            
        else:
            return {
                "sender_account_number": transaction.sender_account.account_number,
                "sender_name": transaction.sender_account.profile.customer.get_full_name(),
                "sender_bank": transaction.sender_account.bank.name,
            }
