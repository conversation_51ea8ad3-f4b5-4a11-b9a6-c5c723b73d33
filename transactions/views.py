from decimal import Decimal
import random
from dfa.models import AgentAccount, AgentMappingTable
import logging
from threading import Thread

from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q
from oauth2_provider.contrib.rest_framework import permissions
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError
from common.permissions import DFAAccessPermission
from rest_framework import serializers
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from common.functions import (
    account_has_platform_access,
    account_settlement_reversal,
    fund_settlement_account,
    get_fee,
    get_international_send_money_transaction_params,
    get_reference,
    get_transact_query_type,
    get_transaction_status,
    is_pin_valid,
    is_sender_verifed,
    set_response_data,
    get_platform,
    update_daily_cummulative,
    within_daily_limit,
    reciever_details_available,
    retrieve_reciever_details,
)
from common.models import (
    BillsPaymentAdviceClass,
    InternationalSendMoneyClass,
    SendMoneyClass,
    SettlementAccount,
    TransactionType,
)
from common.services import (
    bill_payment_advice,
    bill_payment_advice_get_tsq,
    get_name_enquiry,
    mfs_bank_send_money,
    mfs_mobile_money_send_money,
    retrieve_exchange_rate,
    send_money,
    update_balance_enquiry,
)
from bank.models import Bank
from customer.models import CustomerAccount
from transactions.serializers import (
    AirtimeDataSerializer,
    BillPaymentSerializer,
    InternationalBankAccountSendMoneySerializer,
    InternationalMobileMoneySendMoneySerializer,
    SendMoneySerializer,
    CollectionSerializer,
    TransactionSummarySerializer,
)
from ledger_manager.functions import debit_account, credit_account
from ledger_manager.models import AccountHistory, Account
from bank.models import Bank
from transactions.models import Transaction
# from logic_omnichannel import settings
from django.conf import settings
from transactions.utils import get_transaction_details

# Create your views here.

class SendMoneyAPI(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = SendMoneySerializer

    def post(self, request):

        log = logging.getLogger('omni_send_money')
        _request_data = {**request.data}
        _request_data.pop('pin')
        log.info(f'entering SEND MONEY API with: {_request_data}')

        try:
            '''
                Validate request payload and get the required arguments to call the send money gateway
            '''
            serializer = SendMoneySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            sender = request.auth.user
            token = request.auth.token

            # PRODUCTION
            app_code = sender.app_code 
            if app_code == None or app_code == "0000":
                app_code = sender.agent.bank.alias

            # DEVELOPMENT
            # app_code = None
            
            channel = get_platform(token=request.auth.token)
            if not hasattr(sender, 'customerprofile') and not hasattr(sender, 'agent'):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description='USER DOES NOT HAVE VALID PROFILE',
                    detail='User does not have a valid profile, contact admin'
                )
                log.info(f'exiting SEND MONEY API with: {data}')
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            if not channel.upper().startswith("DFA"):
                sender = sender.customerprofile
                if not is_sender_verifed(sender):
                # check only for customers, as agents email and phone number does not get verified.
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description='Customer is not VERIFIED',
                        detail='Customer is not VERIFIED. Confirm Email and Phone Number Verification'
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                sender = sender.agent
            
            pin = serializer.validated_data['pin']
            if not channel.startswith("Mobile Application") and not is_pin_valid(
                pin, sender
            ):
                # every other channel should require pin check except mobile app
                data = set_response_data(
                    code=settings.INVALID_PIN_RESPONSE,
                    description='INVALID PIN',
                    detail='User has inputed incorrect pin, kindly check and confirm'
                )
                log.info(f'exiting SEND MONEY API with: {data}')
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            sender_account = serializer.validated_data.get('sender_account', None)
            if sender_account is None:
                sender_account = serializer.validated_data.get('agent_account', None)
            if sender_account is None:
                raise Exception("Account number not supplied.")

            sender_institution_code = serializer.validated_data['sender_institution_code']
            destination_institution_code = serializer.validated_data['destination_institution_code']
            destination_account = serializer.validated_data['destination_account_number']
            amount = serializer.validated_data['amount']
            destination_account_name = serializer.validated_data["destination_account_name"]

            if not account_has_platform_access(channel, sender_account) or sender_account.status == 'I':
                # block account from making transaction on blocked channels
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description='Account Is Blocked On This Channel, Contact Admin',
                    detail=f'Account -> {sender_account.account_number} mapped to Bank -> {sender_account.bank} is blocked on Channel -> {channel}'
                )
                log.info(f'exiting SEND MONEY API with: {data}')
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            sender_bank = sender_account.bank
            destination_bank = Bank.objects.filter(Q(nip_code=destination_institution_code) | Q(code=destination_institution_code))
            if destination_bank:
                destination_bank = destination_bank[0]
            else:
                raise Exception('Destination Bank Does Not Exist')
            
            if sender_bank.code != sender_institution_code:
                # at this point it is possible that a customer has two accounts with seperate banks but same account
                # number. this seems wierd but we are handling it here
                try:
                    if channel.upper().startswith("DFA"):
                        sender_account = AgentAccount.objects.get(agent=sender, bank__code=sender_institution_code)
                    else:
                        sender_account = CustomerAccount.objects.get(profile=sender, bank__code=sender_institution_code)
                except ObjectDoesNotExist:
                    raise Exception('Institution code not matching account')

            if channel.upper().startswith("DFA"):
                if sender_account not in AgentAccount.objects.filter(agent=sender):
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description='INVALID AGENT ACCOUNT',
                        detail='This account is not valid for agent.'
                    )
                    log.info(f'exiting SEND MONEY API with: {data}')
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                if sender_account not in CustomerAccount.objects.filter(profile=sender):
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description='INVALID USER ACCOUNT',
                        detail='This account is not valid for user.'
                    )
                    log.info(f'exiting SEND MONEY API with: {data}')
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if not within_daily_limit(amount, sender_account):
                data = set_response_data(
                    code=settings.DAILY_LIMIT_RESPONSE,
                    description='Account Daily Limit Reached',
                    detail='Daily limit would be surpassed by this transaction'
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            send_money_type = get_transact_query_type(sender_bank, destination_bank)
            # if send_money_type == 'INTRA':
            #     destination_institution_code = sender_institution_code
            if send_money_type == 'INTER':
                destination_institution_code = destination_bank.nip_code
                # if not reciever_details_available(sender_account.account_number) or retrieve_reciever_details(sender_account.account_number).get('reciever_account') != destination_account:
                    # check if reciever details is not stored locally or if the currently stored details is invalid
                    # we run name enquiry again to populate our local dynamic memory
                    # NOTE: RUN THIS ASYNCRONOUSLY in future
                name_enq = get_name_enquiry(
                    sender_bank=sender_account.bank,
                    account_number=destination_account, 
                    institution_code=destination_bank.nip_code, 
                    sender_account_number=sender_account.account_number, 
                    sender_institution_code=sender_institution_code, 
                    type='INTER',
                    app_code=app_code
                )

                session_id = name_enq.get('response_description', []).get('nip_session_id')

                # for INTER bank transactions we calculate the fee for omni channel
                if channel.upper().startswith("DFA"):
                    fee = get_fee(amount=Decimal(amount), transaction_type='dfa_send_money', partner_bank=sender_bank)
                else:
                    fee = get_fee(amount=Decimal(amount), transaction_type='send_money', partner_bank=sender_bank)

            else:
                session_id = ''.join([str(random.randint(0, 9)) for _ in range(35)])

            
            # create send money payload class
            string_reference = get_reference()
            payload_class = SendMoneyClass()
            payload_class.send_money_type = send_money_type
            payload_class.sender_account = sender_account.account_number
            payload_class.sender_institution_code = sender_bank.nip_code
            payload_class.destination_account = destination_account
            payload_class.destination_institution_code = destination_bank.nip_code
            payload_class.amount = amount
            payload_class.narration = f'FT{sender_account.account_number} {destination_account}'
            payload_class.transaction_ref = str(string_reference)[:12]
            payload_class.bank_name = sender_account.bank.name
            payload_class.sender_account_name = sender_account.profile.customer.get_full_name()
            payload_class.destination_account_name = destination_account_name
            payload_class.sender_email = sender.customer.email
            payload_class.session_id = session_id

            # Call the send money service
            resp = send_money(sender_account.bank, payload_class, app_code)
            if resp.get('response_code') == settings.SUCCESS_RESPONSE:
                response_detail = resp.get('response_detail', {})
                transaction = serializer.save(
                    channel=channel,
                    response_code=settings.SUCCESS_RESPONSE,
                    response_description='SEND MONEY SUCCESSFUL' if not channel.upper().startswith("DFA") else 'DFA SEND MONEY SUCCESSFUL',
                    status='S',
                    nam_payment_reference=response_detail.get('cba_reference'),
                    core_banking_ref=payload_class.transaction_ref
                )
                log.info(f'checking SEND MONEY API for CBA REFERENCE: {response_detail.get("cba_reference")}')

                if channel.upper().startswith("DFA"):
                    tsq_type = TransactionType.objects.get(code="DFA_SEND_MONEY")
                    transaction = serializer.save(agent=sender, transaction_type=tsq_type)
                else:
                    transaction = serializer.save(sender=sender)

                if send_money_type == 'INTER':
                    # For Inter bank transactions there is fee attached and it is also chargeable
                    transaction = serializer.save(
                        fee=fee,
                        is_resolved=False,
                        is_transaction_chargable=True
                    )
                Thread(target=update_daily_cummulative, args=(amount, sender_account)).start()
                update_balance_enquiry(
                    token=token,
                    account_number=sender_account.account_number,
                    retry=True,
                )
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='SEND MONEY REQUEST SUCCESSFUL',
                    detail={
                        'transaction_reference': transaction.transaction_ref,
                        'user_ref': transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                        "details": get_transaction_details(transaction)
                    }
                )
                log.info(f'exiting SEND MONEY API with: {data}')
                return Response(data, status=status.HTTP_200_OK)
            else:
                transaction = serializer.save(
                    channel=channel,
                    response_code=resp.get("response_code") or settings.FAILED_RESPONSE,
                    response_description=resp.get("response_description")
                    or "Unable to Complete Transaction",
                    status="P" if resp.get("response_code") == "02" else "F",
                    fee=fee if send_money_type == "INTER" else Decimal("0.00"),
                    core_banking_ref=payload_class.transaction_ref,
                )

                if channel.upper().startswith("DFA"):
                    tsq_type = TransactionType.objects.get(code="DFA_SEND_MONEY")
                    transaction = serializer.save(agent=sender, transaction_type=tsq_type)
                else:
                    transaction = serializer.save(sender=sender)

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get("response_description")
                    or "Unable to Complete Transaction",
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code
                    },
                )
                log.info(f'exiting SEND MONEY API with: {data}')
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='INVALID REQEUST PAYLOAD',
                detail=serializer.errors
            )
            log.info(f'exiting SEND MONEY API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='ERROR SENDING MONEY',
                detail=str(e)
            )
            log.info(f'exiting SEND MONEY API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class AirtimeDataAPI(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = AirtimeDataSerializer

    def post(self, request):

        log = logging.getLogger("omni_airtime_data")
        _request_data = {**request.data}
        _request_data.pop("pin")
        log.info(f"entering Airtime-Data API with: {_request_data}")

        try:
            """
            Validate request payload and get the required arguments to call the Airtime Data gateway
            """
            serializer = AirtimeDataSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            sender = request.auth.user
            token = request.auth.token
            app_code = sender.app_code
            channel = get_platform(token=request.auth.token)
            pin = serializer.validated_data.get("pin")
            sender_account = serializer.validated_data.get("sender_account")
            if channel.upper().startswith("DFA"):
                # Transaction request is from the AGENT APP
                if not hasattr(sender, "agent"):
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="USER DOES NOT HAVE VALID PROFILE",
                        detail="User does not have a valid profile, contact admin",
                    )
                    log.info(f"exiting Airtime-Data API with: {data}")
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                sender = sender.agent
                if sender.status == "I":
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="You Have Been Suspended, Please Contact Admin",
                        detail="You Have Been Suspended, Please Contact Admin",
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                sender_account = AgentAccount.objects.get(account_number=sender_account)
            else:
                if not hasattr(sender, "customerprofile"):
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="USER DOES NOT HAVE VALID PROFILE",
                        detail="User does not have a valid profile, contact admin",
                    )
                    log.info(f"exiting Airtime-Data API with: {data}")
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                sender = sender.customerprofile
                if not is_sender_verifed(sender):
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="Customer is not VERIFIED",
                        detail="Customer is not VERIFIED. Confirm Email and Phone Number Verification",
                    )
                    log.info(f"exiting Airtime-Data API with: {data}")
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                sender_account = CustomerAccount.objects.get(
                    account_number=sender_account
                )

            if not is_pin_valid(pin, sender):
                data = set_response_data(
                    code=settings.INVALID_PIN_RESPONSE,
                    description="INVALID PIN",
                    detail="User has inputed incorrect pin, kindly check and confirm",
                )
                log.info(f"exiting Airtime-Data API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            sender_institution_code = serializer.validated_data[
                "sender_institution_code"
            ]
            payment_code = serializer.validated_data["payment_code"]
            phone_number = serializer.validated_data["phone_number"]
            amount = serializer.validated_data["amount"]
            transaction_type = serializer.validated_data["transaction_type"]
            bank = sender_account.bank
            if (
                not account_has_platform_access(channel, sender_account)
                or sender_account.status == "I"
            ):
                # block account from making transaction on blocked channels
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Account Is Blocked On This Channel, Contact Admin",
                    detail=f"Account -> {sender_account.account_number} mapped to Bank -> {sender_account.bank} is blocked on Channel -> {channel}",
                )
                log.info(f"exiting Airtime-Data API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # if sender_account.bank.code != sender_institution_code:
            #     # at this point it is possible that a customer has two accounts with seperate banks but same account
            #     # number. this seems wierd but we are handling it here
            #     try:
            #         sender_account = CustomerAccount.objects.get(
            #             profile=sender, bank__code=sender_institution_code
            #         )
            #     except ObjectDoesNotExist:
            #         raise Exception("Institution code not matching account")

            # if sender_account not in CustomerAccount.objects.filter(profile=sender):
            #     data = set_response_data(
            #         code=settings.FAILED_RESPONSE,
            #         description="INVALID USER ACCOUNT",
            #         detail="This account is not valid for user.",
            #     )
            #     log.info(f"exiting Airtime-Data API with: {data}")
            #     return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if not within_daily_limit(amount, sender_account):
                data = set_response_data(
                    code=settings.DAILY_LIMIT_RESPONSE,
                    description="Account Daily Limit Reached",
                    detail="Daily limit would be surpassed by this transaction",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            fee = get_fee(
                amount=Decimal(amount),
                transaction_type="airtime_data",
                partner_bank=bank,
            )
            #------------------------------------------------- #
            # Bank's wallet account is debited and saved to history, if not sufficient funds in 
            # wallet, it will raise an error
            wallet_account = Account.objects.get(account_number=bank.utility_account)
            debit_account(wallet_account, amount)
            
            wallet_account.save()
                    
            AccountHistory.objects.create(
                account = wallet_account,
                posting_type = "DEBIT",
                amount = amount,
                balance = wallet_account.actual_balance,
                narration = f"AD/Trans/{sender_account}/S"
            )
            # ------------------------------------------------- #
            settlement_account = SettlementAccount.objects.get(
                transaction_type=transaction_type, bank=bank
            )
            resp = fund_settlement_account(
                sender_account, amount, settlement_account, app_code, send_money
            )
            # REFUND WALLET IF FAILED.
            if resp.get("response_code") == settings.FAILED_RESPONSE:
                # possibly the user does not have enough money in his account or there is issue with the core banking
                transaction = serializer.save(
                    sender=sender,
                    sender_account=sender_account,
                    channel=channel,
                    status="F",
                    response_code=settings.FAILED_RESPONSE,
                    response_description="UNABLE TO FUND SETTLEMENT ACCOUNT",
                    core_banking_ref=resp.get("core_banking_ref"),
                )
                
                #---------------------------------------------------------#
                # If interswitch call fails wallet account debit is reversed
                credit_account(wallet_account, amount)
        
                wallet_account.save() 
                        
                AccountHistory.objects.create(
                    account = wallet_account,
                    posting_type = "CREDIT",
                    amount = amount,
                    balance = wallet_account.actual_balance,
                    narration = f"AD/Trans/{sender_account}/R"
                )
                #---------------------------------------------------------#

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail=resp.get(
                        "response_detail", "Unable To Complete Transactions"
                    ),
                )
                log.info(f"exiting Airtime-Data API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # at this point we'd probably want to save the state of the transaction
            if channel.upper().startswith("DFA"):
                transaction = serializer.save(
                    agent=sender,
                    agent_account=sender_account,
                    channel=channel,
                    status="P",
                    response_code="02",  # 02 here represents this unique state
                    response_description="SETTLEMENT ACCOUNT FUNDED SUCCESSFULLY",
                    core_banking_ref=resp.get("core_banking_ref"),
                    nam_payment_reference=resp.get("nam_payment_ref"),
                )
            else:
                transaction = serializer.save(
                    sender=sender,
                    sender_account=sender_account,
                    channel=channel,
                    status="P",
                    response_code="02",  # 02 here represents this unique state
                    response_description="SETTLEMENT ACCOUNT FUNDED SUCCESSFULLY",
                    core_banking_ref=resp.get("core_banking_ref"),
                    nam_payment_reference=resp.get("nam_payment_ref"),
                )

            payload_class = BillsPaymentAdviceClass()
            payload_class.amount = amount
            payload_class.sender_email = (
                sender.customer.email
                if not channel.upper().startswith("DFA")
                else sender.user.email
            )
            payload_class.sender_mobile = sender.phone_number
            payload_class.hash_value = "************"
            payload_class.institution_code = "000001"
            payload_class.payment_code = payment_code
            payload_class.request_id = resp.get("core_banking_ref")
            payload_class.billing_unique_number = phone_number
            payload_class.bank_name = sender_account.bank.name

            resp = bill_payment_advice(payload_class, "airtime_data")
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                # Airtime-Data Transaction is successful, at this point we save the success state to the db
                transaction.response_code = settings.SUCCESS_RESPONSE
                transaction.response_description = "AIRTIME/DATA SUCCESSFUL"
                transaction.status = "S"
                transaction.fee = fee
                transaction.is_resolved = False
                transaction.is_transaction_chargable = True
                transaction.switch_ref = resp.get("response_detail", {}).get("switch_ref", "")
                transaction.save()
                Thread(
                    target=update_daily_cummulative, args=(amount, sender_account)
                ).start()
                update_balance_enquiry(
                    token=token,
                    account_number=sender_account.account_number,
                    retry=True,
                )
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="AIRTIME/DATA PAYMENT REQUEST SUCCESSFUL",
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                        "details": get_transaction_details(transaction)
                    },
                )
                log.info(f"exiting Airtime-Data API with: {data}")
                return Response(data, status=status.HTTP_200_OK)
            else:
                transaction.response_description = "POSSIBLE AIRTIME/DATA FAILURE, PENDING REVERSAL"
                transaction.fee = fee
                transaction.switch_ref = resp.get("response_detail", {}).get("switch_ref", "")

                if resp.get("response_code") != "02":
                    """
                    From the switch response_code==02 means timeout which means it may or may not have gone through this very
                    vague tho but only at this point we do not do reversal every other point we do a reversal
                    at this point transaction has failed so we return the debited money back to the customer account
                    this must run so me may need to make sure this always run if it every gets to this point
                    """
                    log.info(
                        "FROM AIRTIME DATA API -> Calling For Transaction Reversal"
                    )
                    transaction.response_code = settings.FAILED_RESPONSE
                    #---------------------------------------------------------#
                    # If interswitch call fails wallet account debit is reversed
                    credit_account(wallet_account, amount)
            
                    wallet_account.save()
                            
                    AccountHistory.objects.create(
                        account = wallet_account,
                        posting_type = "CREDIT",
                        amount = amount,
                        balance = wallet_account.actual_balance,
                        narration = f"AD/Trans/{sender_account}/R"
                    )
                    #---------------------------------------------------------#
                    Thread(
                        target=account_settlement_reversal,
                        args=(
                            sender_account,
                            amount,
                            settlement_account,
                            app_code,
                            transaction,
                            send_money,
                        ),
                    ).start()

                transaction.save()
                
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                    },
                )
                log.info(f"exiting Airtime-Data API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="INVALID REQEUST PAYLOAD",
                detail=serializer.errors,
            )
            log.info(f"exiting Airtime-Data API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except AgentAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Account Does not Exist for Agent, Contact Admin",
                detail="Account Does not Exist for Agent, Contact Admin",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Account Does Not Exist for Customer",
                detail="Account Does Not Exist for Customer",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except SettlementAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="SETTLEMENT ACCOUNT NOT SET, CONTACT ADMIN",
                detail="SETTLEMENT ACCOUNT NOT SET, CONTACT ADMIN",
            )
            log.info(f"exiting Airtime-Data API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="ERROR PROCESSING AIRTIME/DATA REQUEST",
                detail=str(e),
            )
            log.info(f"exiting Airtime-Data API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class BillPaymentAPI(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = BillPaymentSerializer

    def post(self, request):

        log = logging.getLogger("omni_bill_payment")
        _request_data = {**request.data}
        _request_data.pop("pin")
        log.info(f"entering Bill Payment API with: {_request_data}")

        try:
            """
            Validate request payload and get the required arguments to call the Bill Payment Gateway
            """
            serializer = BillPaymentSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            sender = request.auth.user
            token = request.auth.token
            app_code = sender.app_code
            channel = get_platform(token=request.auth.token)
            if not hasattr(sender, "customerprofile"):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="USER DOES NOT HAVE VALID PROFILE",
                    detail="User does not have a valid profile, contact admin",
                )
                log.info(f"exiting Bill Payment API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            sender = sender.customerprofile
            if not is_sender_verifed(sender):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Customer is not VERIFIED",
                    detail="Customer is not VERIFIED. Confirm Email and Phone Number Verification",
                )
                log.info(f"exiting Bill Payment API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            pin = serializer.validated_data["pin"]
            if not is_pin_valid(pin, sender):
                data = set_response_data(
                    code=settings.INVALID_PIN_RESPONSE,
                    description="INVALID PIN",
                    detail="User has inputed incorrect pin, kindly check and confirm",
                )
                log.info(f"exiting Bill Payment API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            sender_account = serializer.validated_data["sender_account"]
            sender_institution_code = serializer.validated_data[
                "sender_institution_code"
            ]
            payment_code = serializer.validated_data["payment_code"]
            customer_field_1 = serializer.validated_data["customer_field_1"]
            amount = serializer.validated_data["amount"]
            transaction_type = serializer.validated_data["transaction_type"]

            if (
                not account_has_platform_access(channel, sender_account)
                or sender_account.status == "I"
            ):
                # block account from making transaction on blocked channels
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Account Is Blocked On This Channel, Contact Admin",
                    detail=f"Account -> {sender_account.account_number} mapped to Bank -> {sender_account.bank} is blocked on Channel -> {channel}",
                )
                log.info(f"exiting Bill Payment API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if sender_account.bank.code != sender_institution_code:
                # at this point it is possible that a customer has two accounts with seperate banks but same account
                # number. this seems wierd but we are handling it here
                try:
                    sender_account = CustomerAccount.objects.get(
                        profile=sender, bank__code=sender_institution_code
                    )
                except ObjectDoesNotExist:
                    raise Exception("Institution code not matching account")

            if sender_account not in CustomerAccount.objects.filter(profile=sender):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="INVALID USER ACCOUNT",
                    detail="This account is not valid for user.",
                )
                log.info(f"exiting Bill Payment API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if not within_daily_limit(amount, sender_account):
                data = set_response_data(
                    code=settings.DAILY_LIMIT_RESPONSE,
                    description="Account Daily Limit Reached",
                    detail="Daily limit would be surpassed by this transaction",
                )
                log.info(f"exiting Bill Payment API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            fee = get_fee(
                amount=Decimal(amount),
                transaction_type="bill_payment",
                partner_bank=sender_account.bank,
            )
            #-----------------------------------------------------------#
            bank = sender_account.bank
            wallet_account = Account.objects.get(account_number=bank.utility_account)
            debit_account(wallet_account, amount)
            
            wallet_account.save()
                    
            AccountHistory.objects.create(
                account = wallet_account,
                posting_type = "DEBIT",
                amount = amount,
                balance = wallet_account.actual_balance,
                narration = f"BP/Trans/{sender_account}/S"
            )
            #-----------------------------------------------------------#
            settlement_account = SettlementAccount.objects.get(
                transaction_type=transaction_type, bank=sender_account.bank
            )
            settlement_amount = str(Decimal(amount) + fee)
            resp = fund_settlement_account(
                sender_account,
                settlement_amount,
                settlement_account,
                app_code,
                send_money,
            )
            if resp.get("response_code") == settings.FAILED_RESPONSE:
                # possibly the user does not have enough money in his account or there is issue with the core banking
                transaction = serializer.save(
                    sender=sender,
                    sender_account=sender_account,
                    channel=channel,
                    status="F",
                    response_code=settings.FAILED_RESPONSE,
                    response_description="UNABLE TO FUND SETTLEMENT ACCOUNT",
                    core_banking_ref=resp.get("core_banking_ref"),
                )
                
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail=resp.get(
                        "response_detail", "Unable To Complete Transactions"
                    ),
                )
                log.info(f"exiting Bill Payment API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # at this point we'd probably want to save the state of the transaction
            transaction = serializer.save(
                sender=sender,
                channel=channel,
                status="P",
                response_code="02",  # 02 here represents this unique state
                response_description="SETTLEMENT ACCOUNT FUNDED SUCCESSFULLY",
                core_banking_ref=resp.get("core_banking_ref"),
                nam_payment_reference=resp.get("nam_payment_ref"),
            )

            payload_class = BillsPaymentAdviceClass()
            payload_class.amount = amount
            payload_class.sender_email = sender.customer.email
            payload_class.sender_mobile = sender.phone_number
            payload_class.hash_value = "************"
            payload_class.institution_code = "000001"
            payload_class.payment_code = payment_code
            payload_class.request_id = resp.get("core_banking_ref")
            payload_class.billing_unique_number = customer_field_1
            payload_class.bank_name = sender_account.bank.name

            resp = bill_payment_advice(payload_class, "bill_payment")
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                # Bill Payment Transaction is successful, at this point we save the success state to the db
                recharge_pin = resp.get("response_detail", {}).get("recharge_pin", "")
                transaction = serializer.save(
                    amount=Decimal(settlement_amount),
                    response_code=settings.SUCCESS_RESPONSE,
                    response_description="BILL PAYEMENT SUCCESSFUL",
                    status="S",
                    fee=fee,
                    is_resolved=False,
                    is_transaction_chargable=True,
                    recharge_pin=recharge_pin,
                    switch_ref=resp.get("response_detail", {}).get("switch_ref", ""),
                )
                Thread(
                    target=update_daily_cummulative,
                    args=(settlement_amount, sender_account),
                ).start()
                update_balance_enquiry(
                    token=token,
                    account_number=sender_account.account_number,
                    retry=True,
                )
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="BILL PAYMENT PAYMENT REQUEST SUCCESSFUL",
                    detail={
                        "recharge_pin": recharge_pin,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                        "details": get_transaction_details(transaction)
                    },
                )
                log.info(f"exiting Bill Payment API with: {data}")
                return Response(data, status=status.HTTP_200_OK)
            else:
                transaction = serializer.save(
                    response_description="POSSIBLE BILL PAYMENT FAILURE, PENDING REVERSAL",
                    fee=fee,
                    switch_ref=resp.get("response_detail", {}).get("switch_ref", ""),
                )

                if resp.get("response_code") != "02":
                    """
                    From the switch response_code==02 means timeout which means it may or may not have gone through this very
                    vague tho but only at this point we do not do reversal every other point we do a reversal
                    at this point transaction has failed so we return the debited money back to the customer account
                    this must run so me may need to make sure this always run if it every gets to this point
                    """
                    log.info(
                        "FROM BILLS PAYMENT API -> Calling For Transaction Reversal"
                    )
                    transaction = serializer.save(
                        response_code=settings.FAILED_RESPONSE,
                    )
                    #-------------------------------------------------------#
                    wallet_account = Account.objects.get(account_number=bank.utility_account)
                    credit_account(wallet_account, amount)
                    
                    wallet_account.save()
                            
                    AccountHistory.objects.create(
                        account = wallet_account,
                        posting_type = "CREDIT",
                        amount = amount,
                        balance = wallet_account.actual_balance,
                        narration = f"BP/Trans/{sender_account}/R"
                    )
                    #-----------------------------------------------------------#
                    Thread(
                        target=account_settlement_reversal,
                        args=(
                            sender_account,
                            settlement_amount,
                            settlement_account,
                            app_code,
                            transaction,
                            send_money,
                        ),
                    ).start()

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                    },
                )
                log.info(f"exiting Bill Payment API with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="INVALID REQEUST PAYLOAD",
                detail=serializer.errors,
            )
            log.info(f"exiting Bill Payment API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="ERROR PROCESSING UTILITY REQUEST",
                detail=str(e),
            )
            log.info(f"exiting Bill Payment API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class InternationalBankAccountSendMoneyAPI(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = InternationalBankAccountSendMoneySerializer

    def post(self, request):

        log = logging.getLogger("omni_international_send_money")
        _request_data = {**request.data}
        _request_data.pop("pin")
        log.info(
            f"entering International Send Money via Bank Account API with: {_request_data}"
        )

        try:
            """
            Validate request payload and get the required arguments to call the MFS Service Gateway
            """
            serializer = InternationalBankAccountSendMoneySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            sender = request.auth.user
            token = request.auth.token
            app_code = sender.app_code
            channel = get_platform(token=request.auth.token)
            if not hasattr(sender, "customerprofile"):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="USER DOES NOT HAVE VALID PROFILE",
                    detail="User does not have a valid profile, contact admin",
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            sender = sender.customerprofile
            if not is_sender_verifed(sender):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Customer is not VERIFIED",
                    detail="Customer is not VERIFIED. Confirm Email and Phone Number Verification",
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            pin = serializer.validated_data["pin"]
            if not is_pin_valid(pin, sender):
                data = set_response_data(
                    code=settings.INVALID_PIN_RESPONSE,
                    description="INVALID PIN",
                    detail="User has inputed incorrect pin, kindly check and confirm",
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            if sender.kyc_verification_status != "complete":
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"Customer KYC Documentation is currently {sender.kyc_verification_status}",
                    detail=f"Customer KYC Documentation is currently {sender.kyc_verification_status}",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            sender_account = serializer.validated_data["sender_account"]
            sender_institution_code = serializer.validated_data[
                "sender_institution_code"
            ]

            amount = serializer.validated_data["amount"]

            reciever_account_number = serializer.validated_data[
                "reciever_account_number"
            ]
            reciever_mfs_institution_code = serializer.validated_data[
                "reciever_mfs_institution_code"
            ]
            reciever_first_name = serializer.validated_data["reciever_first_name"]
            reciever_last_name = serializer.validated_data["reciever_last_name"]
            reciever_address = serializer.validated_data["reciever_address"]
            reciever_phone_number = serializer.validated_data["reciever_mobile_number"]
            transaction_type = serializer.validated_data["transaction_type"]
            remark = f"{reciever_first_name} TRANSF"

            reciever_country = serializer.validated_data["reciever_country"]
            reciever_country_code = reciever_country.country_code
            reciever_currency_code = reciever_country.currency_code

            sender_bank = sender_account.bank

            if (
                not account_has_platform_access(channel, sender_account)
                or sender_account.status == "I"
            ):
                # block account from making transaction on blocked channels
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Account Is Blocked On This Channel, Contact Admin",
                    detail=f"Account -> {sender_account.account_number} mapped to Bank -> {sender_account.bank} is blocked on Channel -> {channel}",
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if sender_account.bank.code != sender_institution_code:
                # at this point it is possible that a customer has two accounts with seperate banks but same account
                # number. this seems wierd but we are handling it here
                try:
                    sender_account = CustomerAccount.objects.get(
                        profile=sender, bank__code=sender_institution_code
                    )
                except ObjectDoesNotExist:
                    raise Exception("Institution code not matching account")

            if sender_account not in CustomerAccount.objects.filter(profile=sender):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="INVALID USER ACCOUNT",
                    detail="This account is not valid for user.",
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if not within_daily_limit(amount, sender_account):
                data = set_response_data(
                    code=settings.DAILY_LIMIT_RESPONSE,
                    description="Account Daily Limit Reached",
                    detail="Daily limit would be surpassed by this transaction",
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            exchange_rate = retrieve_exchange_rate(reciever_country_code)   
            
            if exchange_rate:
                transaction_params = get_international_send_money_transaction_params(
                    amount, exchange_rate
                )
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable to Retrieve Exchange Rate",
                    detail="Unable to Retrieve Exchange Rate from Cache",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            fee = get_fee(
                amount=Decimal(amount),
                transaction_type="i_send_money",
                partner_bank=sender_bank,
            )

            omni_reciever_amount = transaction_params.get("omni_reciever_amount")
            real_reciever_amount = transaction_params.get("real_reciever_amount")
            exchange_rate_add_on = transaction_params.get("exchange_rate_add_on")

            settlement_account = SettlementAccount.objects.get(
                transaction_type=transaction_type, bank=sender_account.bank
            )

            total_amount = str(Decimal(amount) + fee)
            resp = fund_settlement_account(
                sender_account, total_amount, settlement_account, app_code, send_money
            )
            if resp.get("response_code") == settings.FAILED_RESPONSE:
                # possibly the user does not have enough money in his account or there is issue with the core banking
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail=resp.get(
                        "response_detail", "Unable To Complete Transactions"
                    ),
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # at this point we'd probably want to save the state of the transaction
            transaction = serializer.save(
                sender=sender,
                channel=channel,
                status="P",
                amount=(amount - exchange_rate_add_on).quantize(Decimal("1.00")),
                exchange_rate_add_on=exchange_rate_add_on.quantize(Decimal("1.00")),
                fee=fee,
                # exchange_rate=exchange_rate,
                omni_reciever_amount=omni_reciever_amount.quantize(Decimal("1.00")),
                real_reciever_amount=real_reciever_amount.quantize(Decimal("1.00")),
                response_code="02",  # 02 here represents this unique state
                response_description="SETTLEMENT ACCOUNT FUNDED SUCCESSFULLY",
                core_banking_ref=resp.get("core_banking_ref"),
                nam_payment_reference=resp.get("nam_payment_ref"),
            )

            payload_class = InternationalSendMoneyClass()
            payload_class.reciever_account_number = reciever_account_number
            payload_class.reciever_amount = real_reciever_amount
            payload_class.reciever_currency_code = reciever_currency_code
            payload_class.reciever_mfs_bank_code = reciever_mfs_institution_code
            payload_class.narration = remark
            payload_class.reciever_country_code = reciever_country_code
            payload_class.reciever_first_name = reciever_first_name
            payload_class.reciever_last_name = reciever_last_name
            payload_class.reciever_phone_number = reciever_phone_number
            payload_class.reciever_address = reciever_address
            payload_class.request_id = str(transaction.transaction_ref)
            payload_class.sender_country_code = "NG"
            payload_class.sender_first_name = sender.customer.first_name
            payload_class.sender_last_name = sender.customer.last_name
            payload_class.sender_phone_number = sender.phone_number
            payload_class.sender_address = "Nigeria"
            payload_class.sender_dob = sender.dob
            payload_class.sender_document_id = sender.kycdocs.id_number

            resp = mfs_bank_send_money(payload_class)

            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                # International Send Money Transaction is successful, at this point we save the success state to the db
                transaction = serializer.save(
                    response_code=settings.PENDING_RESPONSE,
                    response_description="SEND MONEY PENDING",
                    status="P",
                    is_resolved=False,
                    mfs_reference=resp.get("response_detail", {}).get("mfs_reference"),
                )
                update_balance_enquiry(
                    token=token,
                    account_number=sender_account.account_number,
                    retry=True,
                )
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="INTERNATIONAL SEND MONEY REQUEST PENDING",
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                        "details": get_transaction_details(transaction)
                    },
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                transaction = serializer.save(
                    response_code=settings.FAILED_RESPONSE,
                    response_description="SEND MONEY FAILED PENDING REVERSAL",
                    mfs_reference=resp.get("response_detail", {}).get("mfs_reference"),
                )
                log.info(
                    "FROM INTERNATIONAL SEND MONEY API -> Calling For Transaction Reversal"
                )
                Thread(
                    target=account_settlement_reversal,
                    args=(
                        sender_account,
                        total_amount,
                        settlement_account,
                        app_code,
                        transaction,
                        send_money,
                    ),
                ).start()

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                    },
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="ERROR PROCESSING INTERNATIONAL SEND MONEY REQUEST",
                detail=str(e),
            )
            log.info(f"exiting International Bank Account Send Money API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class CollectionAPI(generics.GenericAPIView):
    # create a log handler for this
    permission_classes = [DFAAccessPermission]
    serializer_class = CollectionSerializer
    
    def post(self, request):
        log = logging.getLogger('omni_send_money')
        
        try:
            serializer = CollectionSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            user = request.auth.user
            agent = user.agent
            bank = agent.bank
            token = request.auth.token
            app_code = bank.alias
            
            channel = get_platform(token=token)
            
            pin = serializer.validated_data["pin"]
            amount = serializer.validated_data['amount']
            institution_code = serializer.validated_data["institution_code"]
            agent_account = serializer.validated_data["agent_account"]
            customer_account_number = serializer.validated_data["customer_account_number"]

            try:
                agent_account = AgentAccount.objects.get(account_number=agent_account, bank=bank)
            except AgentAccount.DoesNotExist:
                raise Exception("Agent account does not exist")
                
            institution_code = Bank.objects.filter(Q(nip_code=institution_code) | Q(code=institution_code), id=bank.id)
            if institution_code:
                institution_code = institution_code[0]
            else:
                raise Exception('Agent Bank not bank with institution code.')
                
            if agent_account.agent != agent:
                    # Some layer of extra security, ensuring users of the API do not enter account number of choice for 
                    # debit, if token retrieved is not owner of account number entered, the forbidden error will be returned
                    data = set_response_data(
                            code=settings.FAILED_RESPONSE,
                            description="Forbidden Action. Account number supplied not for this Agent",
                            detail="Forbidden Action. Account number supplied not for this Agent"
                        )
                    return Response(data, status=status.HTTP_403_FORBIDDEN)
                
            if agent_account.status == "I":
                data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description='Inactive Agent account',
                        detail='Agent account is not active. Please contact admin'
                    )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            is_pin_validated = is_pin_valid(pin, agent)
            if not is_pin_validated:
                data = set_response_data(
                    code=settings.INVALID_PIN_RESPONSE,
                    description='INVALID PIN',
                    detail='Agent has inputted incorrect pin, kindly check and confirm'
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            
            mapping = AgentMappingTable.objects.filter(
                agent__agent=agent, customer_account_number=customer_account_number
            )
            if mapping:
                mapping = mapping[0]
                customer_account = mapping.customer_account_number
                customer_name = mapping.customer_account_name
            else:
                name_enq = get_name_enquiry(
                    sender_bank=institution_code,
                    account_number=customer_account_number, 
                    institution_code=institution_code.code,
                    sender_account_number=customer_account_number,
                    sender_institution_code=institution_code.code,
                    type="INTRA",
                    app_code=app_code
                )
                log.info(f"COLLECTION API: Name Enquiry >> {name_enq}")
                if name_enq.get("response_code") == settings.SUCCESS_RESPONSE:
                    customer_name = name_enq.get("response_description")["account_name"]
                    customer_account = customer_account_number
                else:
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="Customer account not found",
                        detail="Customer account not found"
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            if not within_daily_limit(amount, agent_account):
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description='Account Daily Limit Reached',
                        detail='Daily limit would be surpassed by this transaction'
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            payload_class = SendMoneyClass()
            payload_class.send_money_type = "INTRA"
            payload_class.sender_account = agent_account.account_number
            payload_class.sender_institution_code = institution_code.code
            payload_class.destination_account = customer_account
            payload_class.destination_institution_code = institution_code.code
            payload_class.amount = amount
            payload_class.narration = f'COLLECTIONS: {agent_account.account_number} {customer_account}'
            payload_class.transaction_ref = get_reference()
            payload_class.bank_name = agent_account.bank.name
            payload_class.sender_account_name = agent_account.agent.get_full_name()
            payload_class.sender_email = agent_account.agent.user.email
            payload_class.destination_account_name = customer_name
            
            resp = send_money(institution_code, payload_class, app_code)
            log.info(f"COLLECTION API: Send Money >> {resp}")
            if resp.get('response_code') == settings.SUCCESS_RESPONSE:
                response_detail = resp.get('response_detail', {})
                transaction = serializer.save(
                    bank=bank,
                    agent=agent,
                    agent_account=agent_account,
                    channel=channel,
                    response_code=settings.SUCCESS_RESPONSE,
                    response_description='COLLECTION TRANSACTION SUCCESSFUL',
                    status='S',
                    customer_account_number=customer_account,
                    customer_account_name = customer_name,
                    nam_payment_reference=response_detail.get('nam_payment_reference'),
                    core_banking_ref=payload_class.transaction_ref
                )
                
                Thread(target=update_daily_cummulative, args=(amount, agent_account)).start()
                update_balance_enquiry(
                        token=token,
                        account_number=agent_account.account_number,
                        retry=True,
                    )
                data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description='COLLECTION REQUEST SUCCESSFUL',
                        detail={
                            'transaction_reference': transaction.transaction_ref,
                            'user_ref': transaction.user_ref,
                            "tsq_type": transaction.transaction_type.code,
                            "details": get_transaction_details(transaction)
                        }
                    )
                return Response(data, status=status.HTTP_200_OK)
            else:
                    transaction = serializer.save(
                        bank=bank,
                        agent=agent,
                        agent_account=agent_account,
                        channel=channel,
                        customer_account_number=customer_account,
                        customer_account_name=customer_name,
                        response_code=resp.get("response_code") or settings.FAILED_RESPONSE,
                        response_description=resp.get("response_description")
                        or "Unable to Complete Transaction",
                        status="P" if resp.get("response_code") == "02" else "F",
                        core_banking_ref=payload_class.transaction_ref,
                    )
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description=resp.get("response_description")
                        or "Unable to Complete Transaction",
                        detail={
                            "transaction_reference": transaction.transaction_ref,
                            "user_ref": transaction.user_ref,
                            "tsq_type": transaction.transaction_type.code
                        },
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='INVALID REQEUST PAYLOAD',
                detail=serializer.errors
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='UNABLE TO PROCESS COLLECTION TRANSACTION',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            
class InternationalMobileMoneySendMoneyAPI(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = InternationalMobileMoneySendMoneySerializer

    def post(self, request):

        log = logging.getLogger("omni_international_send_money")
        _request_data = {**request.data}
        _request_data.pop("pin")
        log.info(
            f"entering International Send Money via Mobile Wallet API with: {_request_data}"
        )

        try:
            """
            Validate request payload and get the required arguments to call the MFS Service Gateway
            """
            serializer = InternationalMobileMoneySendMoneySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            sender = request.auth.user
            token = request.auth.token
            app_code = sender.app_code
            channel = get_platform(token=request.auth.token)
            if not hasattr(sender, "customerprofile"):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="USER DOES NOT HAVE VALID PROFILE",
                    detail="User does not have a valid profile, contact admin",
                )
                log.info(
                    f"exiting International Mobile Money Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            sender = sender.customerprofile
            if not is_sender_verifed(sender):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Customer is not VERIFIED",
                    detail="Customer is not VERIFIED. Confirm Email and Phone Number Verification",
                )
                log.info(
                    f"exiting International Mobile Money Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            pin = serializer.validated_data["pin"]
            if not is_pin_valid(pin, sender):
                data = set_response_data(
                    code=settings.INVALID_PIN_RESPONSE,
                    description="INVALID PIN",
                    detail="User has inputed incorrect pin, kindly check and confirm",
                )
                log.info(
                    f"exiting International Mobile Money Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            if sender.kyc_verification_status != "complete":
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"Customer KYC Documentation is currently {sender.kyc_verification_status}",
                    detail=f"Customer KYC Documentation is currently {sender.kyc_verification_status}",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)    

            sender_account = serializer.validated_data["sender_account"]
            sender_institution_code = serializer.validated_data[
                "sender_institution_code"
            ]

            amount = serializer.validated_data["amount"]
            reciever_mobile_number = serializer.validated_data["reciever_mobile_number"]
            reciever_mfs_mobile_wallet_code = serializer.validated_data["reciever_mfs_mobile_wallet_code"]
            reciever_first_name = serializer.validated_data["reciever_first_name"]
            reciever_last_name = serializer.validated_data["reciever_last_name"]
            transaction_type = serializer.validated_data["transaction_type"]
            remark = f"{reciever_first_name} TRANSF"
            reciever_country = serializer.validated_data["reciever_country"]
            reciever_country_code = reciever_country.country_code
            reciever_currency_code = reciever_country.currency_code

            sender_bank = sender_account.bank

            if (
                not account_has_platform_access(channel, sender_account)
                or sender_account.status == "I"
            ):
                # block account from making transaction on blocked channels
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Account Is Blocked On This Channel, Contact Admin",
                    detail=f"Account -> {sender_account.account_number} mapped to Bank -> {sender_account.bank} is blocked on Channel -> {channel}",
                )
                log.info(
                    f"exiting International Mobile Money Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if sender_account.bank.code != sender_institution_code:
                # at this point it is possible that a customer has two accounts with seperate banks but same account
                # number. this seems wierd but we are handling it here
                try:
                    sender_account = CustomerAccount.objects.get(
                        profile=sender, bank__code=sender_institution_code
                    )
                except ObjectDoesNotExist:
                    raise Exception("Institution code not matching account")

            if sender_account not in CustomerAccount.objects.filter(profile=sender):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="INVALID USER ACCOUNT",
                    detail="This account is not valid for user.",
                )
                log.info(
                    f"exiting International Mobile Money Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if not within_daily_limit(amount, sender_account):
                data = set_response_data(
                    code=settings.DAILY_LIMIT_RESPONSE,
                    description="Account Daily Limit Reached",
                    detail="Daily limit would be surpassed by this transaction",
                )
                log.info(
                    f"exiting International Mobile Money Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            exchange_rate = retrieve_exchange_rate(reciever_country_code)   
            
            if exchange_rate:
                transaction_params = get_international_send_money_transaction_params(
                    amount, exchange_rate
                )
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable to Retrieve Exchange Rate",
                    detail="Unable to Retrieve Exchange Rate from Cache",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            fee = get_fee(
                amount=Decimal(amount),
                transaction_type="i_send_money",
                partner_bank=sender_bank,
            )

            omni_reciever_amount = transaction_params.get("omni_reciever_amount")
            real_reciever_amount = transaction_params.get("real_reciever_amount")
            exchange_rate_add_on = transaction_params.get("exchange_rate_add_on")

            settlement_account = SettlementAccount.objects.get(
                transaction_type=transaction_type, bank=sender_account.bank
            )

            total_amount = str(Decimal(amount) + fee)
            resp = fund_settlement_account(
                sender_account, total_amount, settlement_account, app_code, send_money
            )
            if resp.get("response_code") == settings.FAILED_RESPONSE:
                # possibly the user does not have enough money in his account or there is issue with the core banking
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail=resp.get(
                        "response_detail", "Unable To Complete Transactions"
                    ),
                )
                log.info(
                    f"exiting International Mobile Money Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # at this point we'd probably want to save the state of the transaction
            transaction = serializer.save(
                sender=sender,
                channel=channel,
                status="P",
                amount=(amount - exchange_rate_add_on).quantize(Decimal("1.00")),
                exchange_rate_add_on=exchange_rate_add_on.quantize(Decimal("1.00")),
                fee=fee,
                # exchange_rate=exchange_rate,
                omni_reciever_amount=omni_reciever_amount.quantize(Decimal("1.00")),
                real_reciever_amount=real_reciever_amount.quantize(Decimal("1.00")),
                response_code="02",  # 02 here represents this unique state
                response_description="SETTLEMENT ACCOUNT FUNDED SUCCESSFULLY",
                core_banking_ref=resp.get("core_banking_ref"),
                nam_payment_reference=resp.get("nam_payment_ref"),
            )

            payload_class = InternationalSendMoneyClass()
            payload_class.reciever_amount = real_reciever_amount
            payload_class.reciever_currency_code = reciever_currency_code
            payload_class.reciever_mfs_mobile_wallet_code = reciever_mfs_mobile_wallet_code
            payload_class.narration = remark
            payload_class.reciever_country_code = reciever_country_code
            payload_class.reciever_first_name = reciever_first_name
            payload_class.reciever_last_name = reciever_last_name
            payload_class.reciever_phone_number = reciever_mobile_number
            payload_class.request_id = str(transaction.transaction_ref)
            payload_class.sender_country_code = "NG"
            payload_class.sender_first_name = sender.customer.first_name
            payload_class.sender_last_name = sender.customer.last_name
            payload_class.sender_phone_number = sender.phone_number
            payload_class.sender_address = "Nigeria"
            payload_class.sender_dob = sender.dob
            payload_class.sender_document_id = sender.kycdocs.id_number

            resp = mfs_mobile_money_send_money(payload_class)

            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                # International Send Money Transaction is successful, at this point we save the success state to the db
                transaction = serializer.save(
                    response_code=settings.PENDING_RESPONSE,
                    response_description="SEND MONEY PENDING",
                    status="P",

                    is_resolved=False,
                    mfs_reference=resp.get("response_detail", {}).get("mfs_reference"),
                )
                
                update_balance_enquiry(
                    token=token,
                    account_number=sender_account.account_number,
                    retry=True,
                )
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="INTERNATIONAL SEND MONEY REQUEST PENDING",
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                        "details": get_transaction_details(transaction)
                    },
                )
                log.info(
                    f"exiting International Bank Account Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                transaction = serializer.save(
                    response_code=settings.FAILED_RESPONSE,
                    response_description="SEND MONEY FAILED PENDING REVERSAL",
                    mfs_reference=resp.get("response_detail", {}).get("mfs_reference"),
                )

                
                log.info(
                    "FROM INTERNATIONAL SEND MONEY API -> Calling For Transaction Reversal"
                )
                Thread(
                    target=account_settlement_reversal,
                    args=(
                        sender_account,
                        total_amount,
                        settlement_account,
                        app_code,
                        transaction,
                        send_money,
                    ),
                ).start()

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                     detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                    },

                )
                log.info(
                    f"exiting International Mobile Money Send Money API with: {data}"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="ERROR PROCESSING INTERNATIONAL SEND MONEY REQUEST",
                detail=str(e),
            )
            log.info(f"exiting International Bank Account Send Money API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetTransactionStatus(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, user_ref: str, tsq_type: str):
        try:
            """
            Retrieves transactions for this particular transaction based on the user
            reference and transaction type, run its own transaction status query for the
            transaction. Based on the response from the tsq, it updates the transaction
            on the db and return the status of the transaction
            """
            sender = request.auth.user
            transaction_type = tsq_type.lower()

            channel = get_platform(token=request.auth.token)
            if channel.upper().startswith("DFA"):
                sender = sender.agent
            else:
                sender = sender.customerprofile
            

            if transaction_type == "send_money":
                transaction = Transaction.objects.get(
                    send_money__sender=sender, send_money__user_ref=user_ref
                )
            elif transaction_type == "dfa_send_money":
                transaction = Transaction.objects.get(
                    send_money__agent=sender, send_money__user_ref=user_ref
                )
            elif transaction_type == "airtime_data":
                transaction = Transaction.objects.get(
                    airtime_data__sender=sender,
                    airtime_data__user_ref=user_ref,
                )
            elif transaction_type == "bill_payment":
                transaction = Transaction.objects.get(
                    bill_payment__sender=sender,
                    bill_payment__user_ref=user_ref,
                )
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"Invalid Transaction Type {transaction_type}",
                    detail=f"Invalid Transaction Type {transaction_type}",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            transaction_status = get_transaction_status(
                transaction, transaction_type, send_money, bill_payment_advice_get_tsq
            )
            if transaction_status:
                code, status_desc = settings.TRANSACTION_STATUS_CODE.get(
                    transaction_status, (None, None)
                )
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Get Transaction Status Request Successful",
                    detail={
                        "status": status_desc,
                        "transaction_summary": TransactionSummarySerializer(
                            transaction
                        ).data,
                    },
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable to retrieve transaction status at the moment, Please try later",
                    detail="Unable to retrieve transaction status at the moment, Please try later",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Transaction.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Transaction with user_ref {user_ref} Does Not Exist for this user",
                detail=f"Transaction with user_ref {user_ref} Does Not Exist for this user",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Getting Transaction Status",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        


class GetTransactionFee(generics.GenericAPIView):

    trans_type = openapi.Parameter(
        "trans_type",
        openapi.IN_QUERY,
        description="Transaction's Type.",
        type=openapi.TYPE_STRING,
        required=True
    )

    amount = openapi.Parameter(
        "amount",
        openapi.IN_QUERY,
        description="Transaction Amount.",
        type=openapi.TYPE_STRING,
        required=True
    )

    @swagger_auto_schema(manual_parameters=[trans_type, amount])
    def get(self, request):
        
        try:
            """
            Retrieves the amount and transaction type from Query Param,
            then calculates and returns fee for the retrieved config.
            """
            sender = request.auth.user
            app_code = sender.app_code 
            if app_code == None or app_code == "0000":
                app_code = sender.agent.bank.alias

            amount = request.query_params.get("amount", None)
            trans_type = request.query_params.get("trans_type", None)

            partner_bank = Bank.objects.get(alias=app_code)
            fee = get_fee(
                amount=Decimal(amount),
                transaction_type=trans_type,
                partner_bank=partner_bank
            )

            fee_data = {
                'fee': fee,
                'amount': Decimal(amount),
            }

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="GET TRANSACTION FEE SUCCESSFUL.",
                detail=fee_data
            )
            return Response(data, status=status.HTTP_200_OK)

        except Bank.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Partner Bank Not Found',
                detail='Partner Bank Not Found'
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='UNABLE TO GET FEE.',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
