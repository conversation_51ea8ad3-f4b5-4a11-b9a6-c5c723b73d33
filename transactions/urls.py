from transactions.views import (
    AirtimeDataAPI,
    BillPaymentAPI,
    GetTransactionStatus,
    InternationalBankAccountSendMoneyAPI,
    InternationalMobileMoneySendMoneyAPI,
    SendMoneyAPI,
    CollectionAPI,
    GetTransactionFee
)
from django.urls import path

urlpatterns = [
    path("send_money/", SendMoneyAPI.as_view(), name="send_money"),
    path("collection/", CollectionAPI.as_view(), name="collection"),
    path("airtime_data/", AirtimeDataAPI.as_view(), name="airtime_data"),
    path("bill_payment/", BillPaymentAPI.as_view(), name="bill_payment"),
    path(
        "international_bank_send_money/",
        InternationalBankAccountSendMoneyAPI.as_view(),
        name="international_bank_send_money",
    ),
    path(
        "international_mobile_money_send_money/",
        InternationalMobileMoneySendMoneyAPI.as_view(),
        name="international_mobile_money_send_money",
    ),
    path(
        "get_transaction_status/<str:user_ref>/<str:tsq_type>/",
        GetTransactionStatus.as_view(),
        name="get_transaction_status",
    ),
    path("get_transaction_fee", GetTransactionFee.as_view(), name="get_transaction_fee")
]
