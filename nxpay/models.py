from django.db import models
from django.core.validators import MinLengthValidator
from common.validators import validate_numbers, validate_phone_number
from customer.models import CustomerAccount

# Create your models here.


class Token(models.Model):
    EMAIL = "email"
    SMS = "sms"
    BOTH = "both"
    FAILED = "failed"
    PENDING = "pending"
    RESOLVED = "resolved"
    EXPIRED = "expired"

    CHANNEL_CHOICES = ((EMAIL, "Email"), (SMS, "SMS"), (BOTH, "Both"))
    STATUS_CHOICES = (
        (FAILED, "Failed"),
        (PENDING, "Pending"),
        (RESOLVED, "Success"),
        (EXPIRED, "Expired"),
    )
    account = models.ForeignKey(CustomerAccount, on_delete=models.CASCADE)
    token = models.CharField(max_length=200)
    retrieval_channel = models.CharField(max_length=5, choices=CHANNEL_CHOICES)
    email = models.EmailField(blank=True)
    phone_number = models.Char<PERSON>ield(
        max_length=16, blank=True, validators=[validate_phone_number]
    )
    amount = models.DecimalField(max_digits=16, decimal_places=2, default="0.00")
    retrieval_pin = models.CharField(
        max_length=6, validators=[validate_numbers, MinLengthValidator(6)]
    )
    status = models.CharField(max_length=8, choices=STATUS_CHOICES, default="pending")
    date_created = models.DateTimeField(auto_now_add=True)
