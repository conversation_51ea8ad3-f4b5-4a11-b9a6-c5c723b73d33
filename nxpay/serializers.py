from rest_framework import serializers

from .models import Token


class TokenSerializer(serializers.ModelSerializer):
    account_id = serializers.IntegerField(required=True)

    class Meta:
        model = Token
        fields = [
            "account_id",
            "amount",
            "email",
            "phone_number",
            "retrieval_channel",
            "retrieval_pin",
        ]

    def validate(self, attrs):
        validated_data = super().validate(attrs)
        channel = validated_data.get("retrieval_channel")
        phone_number = validated_data.get("phone_number")
        email = validated_data.get("email")
        if channel == Token.BOTH:
            # both phone number and email must exist
            if not phone_number or not email:
                raise serializers.ValidationError(
                    {"retrieval_channel": "Phone number and Email must exist"}
                )
        elif channel == Token.EMAIL:
            # email must exist
            if not email:
                raise serializers.ValidationError({"email": "Email must exist"})
        elif channel == Token.SMS:
            # phone number must exist
            if not phone_number:
                raise serializers.ValidationError(
                    {"phone_number": "Phone Number must exist"}
                )

        return validated_data

    def create(self, validated_data):
        validated_data.pop("account_id")
        return super().create(validated_data)


class RedeemTokenSerializer(serializers.Serializer):
    account_id = serializers.IntegerField(required=True)
    amount = serializers.DecimalField(max_digits=16, decimal_places=2, default="0.00")
    token = serializers.CharField(min_length=1)
    retrieval_pin = serializers.CharField(min_length=6, max_length=6)

    class Meta:
        fields = ["account_id", "amount", "token", "retrieval_pin"]
