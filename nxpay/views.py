from rest_framework import generics, viewsets, status
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from oauth2_provider.contrib.rest_framework import permissions
from threading import Thread
import logging
from datetime import timed<PERSON>ta
from bank.models import Bank

from common.services import (
    initiateNxPayToken,
    redeemNxPayToken,
    send_email,
    send_sms,
    send_sms_test,
    validateNxPayToken,
)
from customer.models import CustomerAccount
# from logic_omnichannel import settings
from django.conf import settings
from nxpay.models import Token
from nxpay.serializers import RedeemTokenSerializer, TokenSerializer
from common.functions import get_reference, get_serializer_key_error, set_response_data


# Create your views here.
class TokenView(viewsets.GenericViewSet):
    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = TokenSerializer

    def create(self, request):
        try:
            """
            validate request payload and then initiate token request, then send notification
            to the token recipient
            """
            log = logging.getLogger("django")
            user = request.auth.user
            profile = user.customerprofile
            serializer = TokenSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            account = CustomerAccount.objects.get(
                id=serializer.validated_data.get("account_id"), profile=profile
            )
            token = serializer.save(account=account)
            log.info("Calling Initiate Token Service")
            resp = initiateNxPayToken(token)
            log.info(f"Response from Initiate NxPay Token Service, resp -> {resp}")
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                token.token = resp.get("response_detail", {}).get("token")
                token.save()
                log.info(f"Inititating Thread to send notifications")
                Thread(target=self.send_notification, args=(token,)).start()
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Token Initiated and Sent to Beneficiary",
                    detail="Token Initiated and Sent to Beneficiary",
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                token.status = "failed"
                token.save()
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"Token Initiation Failed -> {resp.get('response_description', 'NxPay Service Error')}",
                    detail=f"Token Initiation Failed -> {resp.get('response_description', 'NxPay Service Error')}",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Customer Account Not Valid",
                detail="Customer Account Not Valid",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Initiating Token, Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_notification(self, token: Token):
        """
        sends token notifaction to the beneficiary on successful token initiation
        """
        if token.retrieval_channel == token.BOTH:
            self.send_sms_notification(token)
            self.send_email_notification(token)
        elif token.retrieval_channel == token.SMS:
            self.send_sms_notification(token)
        elif token.retrieval_channel == token.EMAIL:
            self.send_email_notification(token)

    def send_sms_notification(self, token: Token):
        """
        send sms to beneficiary
        """
        message = f"Token: {token.token}\nAmount: {token.amount}\nSender: {token.account.profile.customer.first_name} {token.account.profile.customer.last_name}\nExpires: {(token.date_created + timedelta(minutes=60)).strftime('%a %b %d %H:%M:%S WAT %Y')}\nPin: Shared by sender\nRedeem from any bank channels or agent"

        if settings.APP_ENV == "development":
            send_sms_test(token.phone_number, message)
            return

        if token.account.bank.message_sender_name == "OmoluabiMB":
            send_sms_test(token.phone_number, message)
        else:
            request_id = get_reference()
            send_sms(
                token.phone_number,
                message,
                institution_code=token.account.bank.code,
                message_sender_name=token.account.bank.message_sender_name,
                request_id=request_id,
            )

    def send_email_notification(self, token: Token):
        """
        send email to beneficiary
        """

        message = f"Kindly find your NxPay Credit token details below;\nToken: {token.token}\nAmount: {token.amount}\nSender: {token.account.profile.customer.first_name} {token.account.profile.customer.last_name}\nExpires: {(token.date_created + timedelta(minutes=60)).strftime('%a %b %d %H:%M:%S WAT %Y')}\nPin: Shared by sender\nRedeem from any bank channels or agent.\n \nPlease retrieve pin from sender."

        email_args = {
            "email_from": token.account.bank.email,
            "to": token.email,
            "subject": f"YOUR NXPAY CREDIT TOKEN DETAILS.",
            "message": message,
            "customer_first_name": "",
            "img_url": f"{token.account.bank.email_img_url}",
            "foreground": token.account.bank.email_foreground_color,
            "background": token.account.bank.email_background_color,
        }

        send_email(email_args, "ACCOUNT_OPENING_NOTIFICATION")


class RedeemToken(generics.GenericAPIView):
    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = RedeemTokenSerializer

    def post(self, request):
        try:
            """
            Validate request payload and try to process token redemption
            """
            user = request.auth.user
            profile = user.customerprofile

            serializer = RedeemTokenSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            account_id = serializer.validated_data.get("account_id")
            account = CustomerAccount.objects.get(id=account_id, profile=profile)
            resp = redeemNxPayToken(account, serializer.validated_data)
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="NxPay Token Redeemed Successfully",
                    detail="NxPay Token Redeemed Successfully",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable to Redeem NxPay Token"
                    ),
                    detail="Unable to Redeem NxPay Token",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Customer Account Not Valid",
                detail="Customer Account Not Valid",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Initiating Token, Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ValidateToken(generics.GenericAPIView):
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, account_id, token):
        try:
            """
            Validate NxPay Token and returns Token details
            """
            user = request.auth.user
            profile = user.customerprofile

            account = CustomerAccount.objects.get(id=account_id, profile=profile)

            resp = validateNxPayToken(account.bank.nip_code, token)
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                detail = resp.get("response_detail")
                try:
                    originating_bank = Bank.objects.get(
                        nip_code=detail.get("originating_institution_code")
                    ).name
                except Bank.DoesNotExist:
                    originating_bank = ""
                detail["originating_bank"] = originating_bank

                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Token Validated Successfully",
                    detail=detail,
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Error Validating Token"
                    ),
                    detail=resp.get(
                        "response_detail",
                        resp.get("response_description", "Error Validating Token"),
                    ),
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Customer Account Not Valid",
                detail="Customer Account Not Valid",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Validating Token, Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
