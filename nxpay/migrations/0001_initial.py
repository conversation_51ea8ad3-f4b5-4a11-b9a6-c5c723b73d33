# Generated by Django 3.2.3 on 2024-04-10 22:51

import common.validators
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customer', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Token',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=200)),
                ('retrieval_channel', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS'), ('both', 'Both')], max_length=5)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone_number', models.Char<PERSON>ield(blank=True, max_length=16, validators=[common.validators.validate_phone_number])),
                ('amount', models.Decimal<PERSON>ield(decimal_places=2, default='0.00', max_digits=16)),
                ('retrieval_pin', models.CharField(max_length=6, validators=[common.validators.validate_numbers, django.core.validators.MinLengthValidator(6)])),
                ('status', models.CharField(choices=[('failed', 'Failed'), ('pending', 'Pending'), ('resolved', 'Success'), ('expired', 'Expired')], default='pending', max_length=8)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customeraccount')),
            ],
        ),
    ]
