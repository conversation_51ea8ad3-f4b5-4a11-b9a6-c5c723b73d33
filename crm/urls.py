from crm.views import AddCommentView, GetRelatedService, GetTicketCategory, TicketViewSet
from django.urls import path
from rest_framework.routers import DefaultRouter


router = DefaultRouter()
router.register('tickets', TicketViewSet)

urlpatterns = [
    path('get_related_services/<str:category_code>/', GetRelatedService.as_view(), name='get_related_services'),
    path('get_ticket_categories/', GetTicketCategory.as_view(), name='get_ticket_categories'),
    path('add_comment/', AddCommentView.as_view(), name='add_comment'),
]

urlpatterns += router.urls