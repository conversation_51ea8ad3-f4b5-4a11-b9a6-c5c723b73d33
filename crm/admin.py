from django.contrib import admin

from .models import Category, Ticket, Comment
# Register your models here.


class CommentInline(admin.TabularInline):
    model = Comment

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    
    list_display = ['name', 'code']
    list_filter = ['name', 'code']
    search_fields = ['name', 'code']

@admin.register(Ticket)
class TicketAdmin(admin.ModelAdmin):
    
    list_display = ['created_by', 'title', 'category', 'related_service', 'date_created', 'status']
    list_filter = ['category', 'related_service', 'status']
    search_fields = ('created_by__username', 'created_by__first_name', 'created_by__last_name')

    inlines = [
        CommentInline,
    ]

    def title(self, obj):
        if subject:=obj.subject:
            if len(subject) > 25:
                subject = subject[:25]
                subject = subject.rstrip(' ')
                return f'{subject}...'
            return subject
        return '...'

@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    
    list_display = ['created_by', 'response', 'date_created', 'ticket']
    search_fields = ('created_by__username', 'created_by__first_name', 'created_by__last_name')

    def response(self, obj):
        if comment:=obj.comment:
            if len(comment) > 25:
                comment = comment[:25]
                comment = comment.rstrip(' ')
                return f'{comment}...'
            return comment
        return '...'
