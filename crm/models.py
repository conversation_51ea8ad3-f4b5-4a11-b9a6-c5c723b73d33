from django.db import models
from user_profile.models import User
from bank.models import Bank

# Create your models here.


class Category(models.Model):
    class Meta:
        verbose_name_plural = "Categories"
        verbose_name = "Category"

    name = models.CharField(max_length=25)
    code = models.CharField(max_length=15, unique=True)

    def __str__(self) -> str:
        return self.name


class Ticket(models.Model):

    OPEN = "open"
    CLOSED = "closed"
    STATUS_CHOICES = ((OPEN, "open"), (CLOSED, "closed"))

    subject = models.CharField(max_length=100)
    body = models.TextField()
    # attachments = models.ImageField()
    category = models.ForeignKey(Category, on_delete=models.CASCADE, to_field="code")
    user_ref = models.CharField(max_length=30, blank=True)
    related_service = models.Char<PERSON>ield(max_length=50)
    date_created = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.Char<PERSON><PERSON>(max_length=10, choices=STATUS_CHOICES, default=OPEN)
    img_base64 = models.TextField(blank=True)
    created_by = models.ForeignKey(
        User, on_delete=models.CASCADE, blank=True, null=True
    )
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, blank=True, null=True)
    is_edited = models.BooleanField(default=False)

    def __str__(self) -> str:
        if len(self.subject) > 25:
            subject = self.subject[:25]
            subject = subject.rstrip(" ")
            return f"{subject}..."
        return self.subject


class Comment(models.Model):

    ticket = models.ForeignKey(Ticket, on_delete=models.CASCADE)
    comment = models.TextField()
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    date_created = models.DateTimeField(auto_now_add=True)
    is_edited = models.BooleanField(default=False)
    updated_at = models.DateTimeField(auto_now=True)
    mentions = models.JSONField(default=dict, blank=True, null=True)
