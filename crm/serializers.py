from django.db.models import fields
from rest_framework import serializers

from .models import Category, Comment, Ticket


class TicketCategorySerializer(serializers.ModelSerializer):

    class Meta:
        model = Category
        fields = '__all__'


class TicketSerializer(serializers.ModelSerializer):

    created_by = serializers.SerializerMethodField('get_created_by')

    class Meta:
        model = Ticket
        fields = '__all__'

    def get_created_by(self, obj):
        return f'{obj.created_by.last_name} {obj.created_by.first_name}'


class AddCommentSerializer(serializers.ModelSerializer):

    ticket_id = serializers.IntegerField(required=True)
    mentions = serializers.ListField(
        child=serializers.EmailField(), allow_empty=True, required=False
    )

    class Meta:
        model = Comment
        fields = ["ticket_id", "comment", "mentions"]

    def validate(self, attrs):
        validated_data = super().validate(attrs)

        mentions = validated_data.get("mentions")

        if mentions:
            # convert mentions into a dict to be stored in the model
            validated_data["mentions"] = {"mentions": mentions}
        return validated_data

    def create(self, validated_data):
        ticket_id = validated_data.pop("ticket_id")
        validated_data["ticket"] = Ticket.objects.get(id=ticket_id)
        return super().create(validated_data)
