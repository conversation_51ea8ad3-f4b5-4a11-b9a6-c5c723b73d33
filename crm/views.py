from common.services import send_email
from bank.models import Bank
from threading import Thread

from rest_framework.exceptions import ValidationError
from crm.serializers import (
    AddCommentSerializer,
    TicketCategorySerializer,
    TicketSerializer,
)
from bill_payment.models import AirtimeBiller, BillerCategory
from .models import Category, Comment, Ticket
from common.functions import (
    CustomPageNumberPagination,
    send_ticket_creation_notification,
    set_response_data,
)
# from logic_omnichannel import settings
from django.conf import settings
from oauth2_provider.contrib.rest_framework import permissions
from rest_framework import generics, status, viewsets
from rest_framework.response import Response

# Create your views here.


class GetRelatedService(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, category_code):
        """
        Depending on the category of the ticket request, this endpoint returns a list of
        related services mapped to the particular category
        """
        try:
            # if not send money continue with the code by finding the desired category
            category = Category.objects.get(code=category_code)

            if category.code == "airtime_data":
                # call for airtime billers
                airtime_data_billers = AirtimeBiller.objects.all()
                related_services = [
                    biller.biller_name for biller in airtime_data_billers
                ]
                related_services.sort()
            elif category_code == "bill_payment":
                # call for bill payment services
                bill_payment_services = BillerCategory.objects.all()
                related_services = [service.name for service in bill_payment_services]
                related_services.sort()
            elif category_code == "send_money":
                # for category send_money we already know it is either INTER or INTRA so we handle it
                related_services = [
                    "INTER BANK TRANSFER",
                    "INTRA BANK TRANSFER",
                    "TRANSFER TO SELF",
                    "TRANSFER TO OTHER COUNTRIES",
                ]
            else:
                related_services = []

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Related Service Request Successful",
                detail={"related_services": related_services},
            )
            return Response(data, status=status.HTTP_200_OK)
        except Category.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable To Retrieve Related Services, Invalid Category Code",
                detail="Unable To Retrieve Related Services, Invalid Category Code",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Retrieve Related Services, {e}",
                detail=f"{e}",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetTicketCategory(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = TicketCategorySerializer

    def get_queryset(self):
        queryset = Category.objects.all()
        return queryset

    def get(self, request):
        """
        Returns a list of Ticket Category objects
        """
        try:
            queryset = self.get_queryset()
            serializer = TicketCategorySerializer(queryset, many=True)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Ticket Category Request Successful",
                detail={"categories": serializer.data},
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Retrieve Ticket Categories {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class TicketViewSet(viewsets.ModelViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    queryset = Ticket.objects.all().order_by("-date_created")
    serializer_class = TicketSerializer
    pagination_class = CustomPageNumberPagination

    def get_queryset(self, user=None, bank=None):
        queryset = super().get_queryset()
        if user:
            queryset = queryset.filter(created_by=user)
        elif bank:
            queryset = queryset.filter(bank=bank)
        ticket_id = self.request.query_params.get("ticket_id", None)
        status = self.request.query_params.get("status", "").lower()

        if ticket_id:
            queryset = queryset.filter(id=ticket_id)
        if status in ("open", "closed"):
            queryset = queryset.filter(status=status)
        return queryset

    def list(self, request, *args, **kwargs):

        try:
            user = request.auth.user
            if hasattr(user, "customerprofile"):
                # adjust query to accomodate query list of customer created tickets
                queryset = self.filter_queryset(self.get_queryset(user))
            elif hasattr(user, "bankadmin"):
                queryset = self.filter_queryset(
                    self.get_queryset(bank=user.bankadmin.bank)
                )
            else:
                queryset = self.filter_queryset(self.get_queryset())

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                serializer = self.get_paginated_response(serializer.data)
            else:
                serializer = self.get_serializer(queryset, many=True)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Tickets List Request Successful",
                detail=serializer.data,
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Retrieve Tickets List {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def create(self, request, *args, **kwargs):

        try:
            created_by = request.auth.user
            app_code = created_by.app_code
            if app_code != "0000":
                # ticket is created by bank customer
                partner_bank = Bank.objects.get(alias=app_code)
            else:
                # ticket is created by bank admin
                partner_bank = created_by.bankadmin.bank
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer.save(created_by=created_by, bank=partner_bank)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Ticket Successfully Created",
                detail="Ticket Successfully Created",
            )
            Thread(
                target=send_ticket_creation_notification,
                args=(partner_bank, serializer.data, send_email),
            ).start()
            return Response(data, status=status.HTTP_201_CREATED)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Create Ticket {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):

        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)

            # get comments attached to this ticket
            comments = Comment.objects.filter(ticket=instance)
            comments_list = []
            if comments:
                for comment in comments:
                    _comment = {
                        "comment": comment.comment,
                        "created_by": f"{comment.created_by.last_name} {comment.created_by.first_name}",
                        "date_created": comment.date_created,
                        "is_edited": comment.is_edited,
                        "updated_at": comment.updated_at,
                    }
                    comments_list.append(_comment)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Ticket Request Successful",
                detail={"ticket": {**serializer.data, "comments": comments_list}},
            )

            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Retrieve Ticket {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, "_prefetched_objects_cache", None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        data = set_response_data(
            code=settings.SUCCESS_RESPONSE,
            description="Ticket Updated Successfully",
            detail={
                "ticket_id": serializer.data.get("id"),
                "updated_at": serializer.data.get("updated_at"),
            },
        )
        return Response(data, status=status.HTTP_200_OK)

    def perform_update(self, serializer):
        serializer.save()

    def partial_update(self, request, *args, **kwargs):
        kwargs["partial"] = True
        return self.update(request, *args, **kwargs)


class AddCommentView(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = AddCommentSerializer

    def post(self, request):
        """
        Adds comment to CRM Tickets
        """
        try:
            serializer = AddCommentSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            created_by = request.auth.user
            serializer.save(created_by=created_by)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Comments Added Successfully",
                detail="Comments Added Successfully",
            )
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Request Payload",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Add Comment",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
