
from itertools import count
from django.core.cache import cache

def get_ussd_banklist():
    #print(list(cache.get('ussd_app_banks')))
    #load this from list of banks on main app
    return [
        { 'name': "ACCESS BANK", 'code': "044", "nip_code": "000014" },
        { 'name': "DIAMOND BANK", 'code': "063", "nip_code": "000005" },
        { 'name': "ECOBANK NIGERIA", 'code': "050" , "nip_code": "000010"},
        { 'name': "FIDELITY BANK", 'code': "070" , "nip_code": "000007"},
        { 'name': "FIRST BANK OF NIGERIA", 'code': "011" , "nip_code": "000016"},
        { 'name': "FIRST CITY MONUMENT BANK", 'code': "214" , "nip_code": "090409"},
        { 'name': "GUARANTY TRUST BANK", 'code': "058" , "nip_code": "000013"},
        { 'name': "HERITAGE BANK", 'code': "030" , "nip_code": "000020"},
        { 'name': "JAIZ BANK", 'code': "301" , "nip_code": "000006"},
        { 'name': "KEYSTONE BANK", 'code': "082" , "nip_code": "000002"},
        { 'name': "PROVIDUS BANK", 'code': "101" , "nip_code": "000023"},
        { 'name': "SKYE BANK", 'code': "076" , "nip_code": ""},
        { 'name': "STANBIC IBTC BANK", 'code': "221" , "nip_code": "000012"},
        { 'name': "STANDARD CHARTERED BANK", 'code': "068", "nip_code": "000021" },
        { 'name': "STERLING BANK", 'code': "232" , "nip_code": "000001"},
        { 'name': "SUNTRUST", 'code': "100" , "nip_code": "000022"},
        { 'name': "UNION BANK OF NIGERIA", 'code': "032" , "nip_code": "000018"},
        { 'name': "UNITED BANK FOR AFRICA", 'code': "033" , "nip_code": "000004"},
        { 'name': "UNITY BANK", 'code': "215" , "nip_code": "000011"},
        { 'name': "WEMA BANK", 'code': "035" , "nip_code": "000017"},
        { 'name': "ZENITH BANK", 'code': "057" , "nip_code": "000015"},

    ]

def get_match_bank(bank_name_or_code):
    banks = get_ussd_banklist()
    for bank in banks:
    
        if bank['name'] == bank_name_or_code or bank['name'] == bank_name_or_code or bank['nip_code'] == bank_name_or_code:
            return bank
        else:
            return None

def get_like_banks(accountNumber):
    
    banks = get_ussd_banklist()
    algo = [3, 7, 3, 3, 7, 3, 3, 7, 3, 3, 7, 3]
    
    account_list = list(accountNumber)
    interger_of_account = list(map(int, account_list))
    #print(interger_of_account[0:9])

    codelist = []
   
    for bank in banks:
        j = list(bank['code'])
        codelist.append(j)

    #print(codelist)
    dataset = []
    for i in codelist:
        datas = {}
        d = list(i)
        d = list(map(int, d))
        all_list = (d + interger_of_account[0:9])
        checksum = interger_of_account[-1]
        #print(all_list)
        total = 0
        ele = 0
        res_list = [algo[i] * all_list[i] for i in range(len(algo))]
        while(ele < len(res_list)):
            total = total + res_list[ele]
            ele += 1
    
        # printing total value
        # print(type(total))
        mod = total % 10
        # print(mod)
        check = 10 - mod
        # print(check)
        code = all_list[0:3]
        if check == checksum or check == 10:
            i[0:3] = [''.join(i[0:3])]
            datas['code'] = i[0]
            dataset.append(datas)
        # print(f'{i[0]}-{check}')
    # print(dataset)

    final_result = []
    for codes in dataset:
        valueData = {}
        for i in banks:
            if i['code'] != codes['code']:continue
            valueData['code'] = i['code']
            valueData['name'] = i['name']
            valueData['nip_code'] = i['nip_code']
            final_result.append(valueData)

    return final_result