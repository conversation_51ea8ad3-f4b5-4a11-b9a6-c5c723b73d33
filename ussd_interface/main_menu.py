import logging
from .util import (
    authenticate_ussd,
    get_company_details, 
    authenticate_ussd_v2,
    get_ussd_message,
    get_ussd_profile
)

def get_main_menu(string, msisdn, session_id):
    log = logging.getLogger("django")
    company_details = get_company_details(string, msisdn)

    try:
        
        # if authenticate_ussd(msisdn, company_details.alias, session_id) is None or company_details is None:
        if authenticate_ussd_v2(msisdn, company_details) is None or company_details is None:
            message = get_ussd_message('no_profile', company_details.name)
            next_action = "request"

        else:  
            message = get_ussd_message('main', company_details.name)
            next_action = "request"

        return {'message': message, 'next_action': next_action}
    except Exception:
        message = get_ussd_message('exception_message', company_details.name)
        next_action = "request"
        return {'message': message, 'next_action': next_action}


    


