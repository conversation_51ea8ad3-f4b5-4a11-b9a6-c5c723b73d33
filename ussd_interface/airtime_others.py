import threading
from datetime import datetime, timedelta
from threading import Thread
import logging
import re
from ussd_interface.util import (
    get_pin_entry, 
    get_account_number_menu,
    get_account_number_menu_v2,
    ussd_buy_aitirme_data, 
    ussd_buy_aitirme_data_v2,
    get_set_pin_menu,
    ussd_validate_pin,
    ussd_validate_pin_v2,
    get_amount_entry,
    get_airtime_biller_menu,
    get_company_details,
    authenticate_ussd,
    authenticate_ussd_v2,
    get_ussd_message,
)

def get_airtime_other_flow(ussd_input, user_input, msisdn, network, sessionid):
    log = logging.getLogger("django")
    log.info(">>> entering airtime other menu:" + sessionid)
    #improve this by loading main menu from configuration for easy changes
    
    main_entry_pattern = r"\*\d{4}\*\d{2,4}\*3"
    airtime_self_entry_pattern = main_entry_pattern + "$"
    select_account_pattern = main_entry_pattern + "\*\d{1,6}$"
    input_amount_pattern = main_entry_pattern + "\*\d{1,6}\*\d{2,6}$"
    select_network_pattern = main_entry_pattern + "\*\d{1,6}\*\d{2,6}\*\d{1,4}$"
    input_mobile_number_pattern = main_entry_pattern + "\*\d{1,6}\*\d{2,6}\*\d{1,4}\*\d{11,13}$"
    pin_pattern = main_entry_pattern + "\*\d{1,6}\*\d{2,6}\*\d{1,4}\*\d{11,13}\*\d{4,6}$"
    # direct short code  *7737*{bank_short}*3*{amount}*{benef_mobie}
    direct_pattern = main_entry_pattern + "\*\d{2,6}\*\d{11,13}$"
    direct_select_network_pattern = main_entry_pattern + "\*\d{2,6}\*\d{11,13}\*\d{1,4}$"
    direct_pin_pattern = main_entry_pattern + "\*\d{2,6}\*\d{11,13}\*\d{1,4}\*\d{4,6}$"
    
    if re.match(airtime_self_entry_pattern, ussd_input):
        log.info(">>> entering airtime other select account menu:" + sessionid)
        # get_account = get_account_number_menu(msisdn, sessionid)
        get_account = get_account_number_menu_v2(msisdn, ussd_input)
        message = get_account['message']
        next_action = get_account['next_action']

    elif re.match(select_account_pattern, ussd_input):
        log.info(">>> entering aitime other input amount menu:" + sessionid)
        message = get_amount_entry()
        next_action = "request"    
  
    elif re.match(input_amount_pattern, ussd_input) or re.match(direct_pattern, ussd_input):
        log.info(">>> entering aitime other input pin menu:" + sessionid)

        if re.match(direct_pattern, ussd_input):
            result = ussd_input.split("*")
            company_short_code = "*" + str(result[1]) + "*" + str(result[2])
            company_details = get_company_details(company_short_code, msisdn)
            
            # if authenticate_ussd(msisdn, company_details.alias, sessionid) is None or company_details is None:
            if authenticate_ussd_v2(msisdn, company_details.alias) is None or company_details is None:
                #load this from a single source 
                message = get_ussd_message('no_profile', company_details.name)
                next_action = "request"
                return {'message': message, 'next_action': next_action}


        get_airtime_biller = get_airtime_biller_menu()
        message = get_airtime_biller['message']
        next_action = get_airtime_biller['next_action']

    elif re.match(select_network_pattern, ussd_input):
        log.info(">>> entering aitime other input beneficiary mobile number menu:" + sessionid)
        message = "Kindly input Benefiary PHONE NUMBER:"
        next_action = "request"

    elif re.match(input_mobile_number_pattern, ussd_input) or re.match(direct_select_network_pattern, ussd_input):
        log.info(">>> entering aitime self input pin menu:" + sessionid)
        message = get_pin_entry()
        next_action = "request"            

    elif re.match(pin_pattern, ussd_input) or re.match(direct_pin_pattern, ussd_input):
        #split innput and get account selected
        #*7737*28*3*1000*************3*0609
        if re.match(direct_pin_pattern, ussd_input):
            result = ussd_input.split("*")
            selected_account = None
            selected_network = result[6]
            amount = str(result[4])
            benef_mobile = str(result[5])
            user_pin = user_input
        else:    
            result = ussd_input.split("*")
            selected_account = str(result[4])
            selected_network = result[6]
            amount = str(result[5])
            benef_mobile = str(result[7])
            user_pin = user_input
        # validate pin
        
        # if ussd_validate_pin(user_pin, msisdn, sessionid):
        if ussd_validate_pin_v2(user_pin, msisdn, ussd_input):

            log.info(">>> pin valid with and going for artime vending:" + sessionid)

            message = "Airtime recharge of " + amount + " naira to " +  benef_mobile + " is successful, thank you"
            next_action = "end"

            # Start a new thread to run the task
            # task_thread = threading.Thread(target=ussd_buy_aitirme_data, args=(
            #     msisdn, 
            #     selected_account, 
            #     sessionid, 
            #     amount, 
            #     user_pin, 
            #     selected_network,
            #     benef_mobile)) 
            task_thread = threading.Thread(target=ussd_buy_aitirme_data_v2, args=(
                msisdn, 
                ussd_input,
                selected_account,  
                amount, 
                user_pin, 
                selected_network,
                benef_mobile)) 
            task_thread.start()
        else: 
            set_pin_menu = get_set_pin_menu('request')
            message = set_pin_menu['message']
            next_action = set_pin_menu['next_action']   

    return {'message': message, 'next_action': next_action}    