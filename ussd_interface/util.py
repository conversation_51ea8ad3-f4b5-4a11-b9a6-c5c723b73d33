from itertools import count
from rest_framework.reverse import reverse
import random
from datetime import datetime, timedelta
from threading import Thread
from common.functions import ussd_authentication, get_reference
from common.services import send_sms, get_balance_enquiry,get_name_enquiry
import logging
import re
import random
import string
from django.core.cache import cache
import base64
import requests
import json
from customer.models import CustomerProfile
# from logic_omnichannel import settings
from django.conf import settings
from bank.models import Bank 
from django.contrib.auth.hashers import check_password
from common.functions import get_user_token, get_platform
from .getbanks import *

from user_profile.models import User

# def get_ussd_profile(msisdn):
#     phone_number = re.sub("^234", "0", msisdn)

#     ussd_profiles = cache.get("ussd_profiles")
   
#     ussd_user = next((profile[phone_number] for profile in ussd_profiles if phone_number in profile), None)
#     if ussd_user is not None:
#          return ussd_user
#     return None

def get_bank_ussd_code(ussd_code):
    parts = ussd_code.split('*')
    code = '*' +'*'.join(parts[1:3])
    return code  # parts[1] and parts[2] are the first two segments


def get_app_code_from_ussd_code(ussd_code):
    log = logging.getLogger("django")
    ussd_app_banks = cache.get('ussd_app_banks')

    if ussd_app_banks is not None:
        bank_ussd_code = get_bank_ussd_code(ussd_code)

        for ussd_app_bank in ussd_app_banks:
            ussd_app_bank = ussd_app_banks.get(ussd_code=bank_ussd_code)
            if ussd_app_bank is not None:
                app_code = ussd_app_bank.alias
                return app_code
            else:
                return None   
    else:
        log.info('>>> unable to read banks loaded in cache or bank object is empty')        

def get_ussd_profile(msisdn, ussd_code):
    # pass ussd_code as none and return ussd user if its none
    phone_number = re.sub("^234", "0", msisdn)
    ussd_profiles = cache.get("ussd_profiles")
    # if ussd_code is not None:
    bank_ussd_code = get_app_code_from_ussd_code(ussd_code)

    for profile in ussd_profiles:
        if phone_number in profile:
            ussd_user = profile[phone_number]
            app_code = ussd_user.get("app_code")
            if app_code == bank_ussd_code:
                print(f"USSD USER RETURNED ::    {ussd_user}")
                return ussd_user
    return None
    
    # else:
    #     for profile in ussd_profiles:
    #         if phone_number in profile:
    #             ussd_user = profile[phone_number]
    #             return ussd_user
    #     return None

def get_airtime_biller_menu():
    log = logging.getLogger("django")
    details = cache.get('airtime_data_biller')
    
    if details is not None:

        produced_string = ''
        ind = 1

        for detail in details:
            biller_name = detail.biller_name 
            produced_string = f'{produced_string}\n{ind}.{biller_name}'
            ind = ind + 1  
            
        message = "Kindly select from your Network(s) below: \n" + produced_string
        resp = {'message': message, 'next_action': 'requets'}
        
    else:
        message = "hoops!! Something seems wrong try the again"
        resp = {'message': message, 'next_action': 'end'}
    return resp

def get_pin_entry():

    message = "Kindly input your PIN:"
    
    return message

def get_amount_entry():

    message = "Kindly input AMOUNT:"
    
    return message


def get_account_number_menu(msisdn, sessionid):
    log = logging.getLogger("django")
    red_key = 'user_ussd_details:' + msisdn + sessionid
    user_details = cache.get(red_key)

    if user_details is not None:

        profile_accounts = user_details["response_detail"]["customer_bank_profiles"][0]['profile_account']

        produced_string = ''
        # pattern of string : '\n1.**********\n2.**********\n3.**********'
        ind = 1
        if profile_accounts is not None:
            for profile_account in profile_accounts:
                #account_id = profile_account['id']
                #account_name = profile_account['account_name']
                account_number = profile_account['account_number']
                produced_string = f'{produced_string}\n{ind}.{account_number}' 

                ind = ind + 1
                # log.info("PRODUCED >>>" + produced_string)
                
                message = "Kindly select from your account(s) below: \n" + produced_string
                resp = {'message': message, 'next_action': 'requets'}
        else:
                # test this senario
                message = "No account found on your online profile, Select menu below : \n\n7.Open Bank Account"
                resp = {'message': message, 'next_action': 'requets'}
    else:
        message = "hoops!! Something seems wrong try the again"
        resp = {'message': message, 'next_action': 'end'}
    return resp

def get_account_number_menu_v2(msisdn, ussd_code):
    log = logging.getLogger("django")
   
    ussd_user = get_ussd_profile(msisdn, ussd_code)
    if ussd_user is not None:

        accounts = ussd_user['accounts']
        produced_string = ''
        # pattern of string : '\n1.**********\n2.**********\n3.**********'
        ind = 1
        if accounts is not None:
            for account in accounts:
                produced_string = f'{produced_string}\n{ind}.{account}' 
                ind = ind + 1
                
                message = "Kindly select from your account(s) below: \n" + produced_string
                resp = {'message': message, 'next_action': 'requets'}
        else:
                # test this senario
                message = "No account found on your online profile, Select menu below : \n\n7.Open Bank Account"
                resp = {'message': message, 'next_action': 'requets'}
    else:
        message = "hoops!! Something seems wrong try the again"
        resp = {'message': message, 'next_action': 'end'}
    return resp

def authenticate_ussd(msisdn, app_code, session_id):
    log = logging.getLogger("django")
    client_id = settings.USSD_AUTH_ID
    client_secret = settings.USSD_AUTH_SEC

    # try to improve this by reversing since they are on the same application 
    #url = reverse('user_profile:user_login') 

    try:
        red_key = 'user_ussd_details:' + msisdn + session_id
        user_details = cache.get(red_key)
        
        if user_details is None:

            trim_phone = re.sub("^234", "0", msisdn)

            credential = f'{client_id}:{client_secret}'
            credential = base64.b64encode(credential.encode('utf-8'))
            credential = str(credential, 'utf-8')
            headers = {
            'accept': 'application/json',
            'authorization': f'Basic {credential}',
            'Content-Type': 'application/json',
            }
           
            url = str(settings.USSD_AUTH_BASE_URL)
            # url = 'http://127.0.0.1:8000/user_profile/login/'
        
            payload = json.dumps({
                "username": trim_phone,
                "password": trim_phone,
                "app_code": app_code
            })
            log.info("after setting var")
            response = requests.request('POST', url, headers=headers, data=payload)
            log.info("after post")
            response = response.json()

            # log.info("set cache >>>" + str(response))
        
            # Remeber to set ttl with varable timeout here 
            # log.info('CDOE:' + response.get('response_code') == settings.SUCCESS_RESPONSE )
            #json.loads(jsonString)
            ussd_account_access = get_ussd_default_account(response)
            #print("here f" + ussd_account_access)
            log.info("after setting ussd_accecss act")
            
            if response.get('response_code') == settings.SUCCESS_RESPONSE and ussd_account_access is not None:
                cache.set(red_key, response)
                return response
                
            else:
                return None
        else:
            log.info('>>> cannot retrive user details from chache')
            return  user_details  
            # log.info("HELLO GET CACHE >>>" + str(products))
    except Exception:
        log.info('>>> in exception')
        raise Exception('Unknown Validation Error')
    

def authenticate_ussd_v2(msisdn, company_details):
    log = logging.getLogger("django")

    ussd_user = get_ussd_profile(msisdn, company_details.ussd_code)
    if ussd_user is not None:
        app_code = ussd_user.get("app_code")
        if app_code == company_details.alias:
            return ussd_user
        log.info('>>> Invalid user')
        raise Exception("Invalid user")
    return None


def get_and_send_customer_balance(msisdn, user_input, session_id):
    log = logging.getLogger("django")
    log.info('>>> get and send balance enquiry task started in background')
    red_key = 'user_ussd_details:' + msisdn + session_id
    user_details = cache.get(red_key)

    if user_details is not None:

        profile_accounts = user_details["response_detail"]["customer_bank_profiles"][0]["profile_account"]
        institution_code = user_details["response_detail"]["customer_bank_profiles"][0]["bank"]["code"]
        bank_alias = user_details["response_detail"]["customer_bank_profiles"][0]["bank"]["alias"]

        ind = 1
        if profile_accounts is not None:
            for profile_account in profile_accounts:
                account_name = profile_account['account_name']
                account_number = profile_account['account_number']
                # log.info('account number >>' + account_number)
                if ind == int(user_input):
                    # log.info('index=' + str(ind))
                   
                    balance_info = get_balance_enquiry(account_number, institution_code, app_code=bank_alias)
                    
                    if balance_info.get('response_code') == settings.SUCCESS_RESPONSE:
                       # send balance notification by SMS or EMAIL
                       # log.info('BAL RESP:' + str(balance_info))
                       available_balance = balance_info['response_description']['available_balance']
                       book_balance = balance_info['response_description']['book_balance']
                       thank_you_message = ". Thanks for banking with us"
                       sms_text = "Dear "+ account_name + ", balances for " + account_number + " are Available Balance:" + available_balance + ", Book Balance:" + book_balance + thank_you_message
                       #bank = Bank.objects.get(alias=bank_alias)
                       ussd_app_banks = cache.get('ussd_app_banks')
                       bank = ussd_app_banks.get(alias=bank_alias)

                       message_sender_name = bank.message_sender_name

                       sms_resp = send_sms(
                            msisdn,
                            sms_text,
                            institution_code=institution_code,
                            message_sender_name=message_sender_name,
                            request_id=get_reference(),
                        )

                       log.info("sms response >>>" + str(sms_resp))

                       break
                        #return bal 
                    else:
                        log.info('>>> Response from balance equiry >>>' + str(balance_info))    
                else:
                    ind = ind + 1    
        else:
            # test this senario
            log.info('>>> unable to retrive any account for customer profile')
            return 
    else:
        log.info('>>> unable to retrive any user details from cache memory')
        return

def get_and_send_customer_balance_v2(msisdn, selected_account, ussd_input):
    log = logging.getLogger("django")
    ussd_user = get_ussd_profile(msisdn, ussd_input)

    if ussd_user is not None:

        profile_accounts = ussd_user["accounts"]
        institution_code = ussd_user["bank_code"]
        bank_alias = ussd_user["app_code"]

        ind = 1
        if profile_accounts is not None:
            for index, profile_account in enumerate(profile_accounts):
                account_name = ussd_user['name']
                account_number = profile_account
                # log.info('account number >>' + account_number)
                if ind == int(selected_account):
                    # log.info('index=' + str(ind))
                    print("GOT HERE :::")
                    balance_info = get_balance_enquiry(account_number, institution_code, app_code=bank_alias)
                    
                    if balance_info.get('response_code') == settings.SUCCESS_RESPONSE:
                       # send balance notification by SMS or EMAIL
                       # log.info('BAL RESP:' + str(balance_info))
                       available_balance = balance_info['response_description']['available_balance']
                       book_balance = balance_info['response_description']['book_balance']
                       thank_you_message = ". Thanks for banking with us"
                       sms_text = "Dear "+ account_name + ", balances for " + account_number + " are Available Balance:" + available_balance + ", Book Balance:" + book_balance + thank_you_message
                       #bank = Bank.objects.get(alias=bank_alias)
                       ussd_app_banks = cache.get('ussd_app_banks')
                       bank = ussd_app_banks.get(alias=bank_alias)

                       message_sender_name = bank.message_sender_name

                       sms_resp = send_sms(
                            msisdn,
                            sms_text,
                            institution_code=institution_code,
                            message_sender_name=message_sender_name,
                            request_id=get_reference(),
                        )

                       log.info("sms response >>>" + str(sms_resp))

                       break
                        #return bal 
                    else:
                        log.info('>>> Response from balance equiry >>>' + str(balance_info))    
                else:
                    ind = ind + 1    
        else:
            # test this senario
            log.info('>>> unable to retrive any account for customer profile')
            return 
    else:
        log.info('>>> unable to retrive any user details from cache memory')
        return

def get_company_details(string, msisdn=None):
    log = logging.getLogger("django")

    ussd_app_banks = cache.get('ussd_app_banks')
    #bank = Bank.objects.get(ussd_code=string)

    if ussd_app_banks is not None:

        for ussd_app_bank in ussd_app_banks:
            ussd_app_bank = ussd_app_banks.get(ussd_code=string)
            if ussd_app_bank is not None:
                return ussd_app_bank
            else:
                return None   
    else:
        log.info('>>> unable to read banks loaded in cache or bank object is empty')            
          

def get_set_pin_menu(context_action):
     message = "Your pin is not correct. Kindly retry with the right PIN or set your pin with option below: \n\n11.Set Pin"
     next_action = "request"
     return {'message': message, 'next_action': context_action}

def ussd_validate_pin(user_pin, msisdn , session_id):

        #customer = request.auth.user
        red_key = 'user_ussd_details:' + msisdn + session_id
        user_details = cache.get(red_key)
        user_id = user_details["response_detail"]["user_profile"]["id"]
        customer = CustomerProfile.objects.get(customer=user_id)

        return check_password(user_pin, customer.pin)
        return True

def ussd_validate_pin_v2(user_pin, msisdn, ussd_code):
        ussd_user = get_ussd_profile(msisdn, ussd_code)
        if ussd_user is not None:
            pin = ussd_user.get("pin")
            if pin == '':
                raise Exception("User Pin not set, set pin first.")
            return check_password(user_pin, pin)
        
        return None

def ussd_buy_aitirme_data_v2(msisdn, ussd_input, selected_account, amount, user_pin, selected_network, beneficiary_phone = None):
    log = logging.getLogger("django")
    log.info('>>> buy airtime data task started in background')
    client_id = settings.USSD_AUTH_ID
    client_secret = settings.USSD_AUTH_SEC

    ussd_user = get_ussd_profile(msisdn, ussd_input)
    if ussd_user is not None:
        app_code = ussd_user.get("app_code")
        trim_phone = re.sub("^234", "0", msisdn)
        credential = f'{client_id}:{client_secret}'
        credential = base64.b64encode(credential.encode('utf-8'))
        credential = str(credential, 'utf-8')
        headers = {
        'accept': 'application/json',
        'authorization': f'Basic {credential}',
        'Content-Type': 'application/json',
        }
        
        login_url = str(settings.USSD_AUTH_BASE_URL)
        # login_url = 'http://127.0.0.1:8000/user_profile/login/'

        payload = json.dumps({
            "username": trim_phone,
            "password": trim_phone,
            "app_code": app_code
        })
        login_response = requests.request('POST', login_url, headers=headers, data=payload)
        login_response = login_response.json()
        log.info(f"USSD LOGIN TO BUY AIRTIME RESPONSE :::  {login_response}")

        if login_response.get('response_code') == settings.SUCCESS_RESPONSE:
            access_token = login_response.get("response_detail")["token_parameters"]["access_token"]
        else:
            return "Cannot Authenticate User."
        if selected_account is None:
            customer_account = get_ussd_default_account(login_response)[0]
        else:    
            customer_account = get_selected_account(selected_account, login_response)[0]

        institution_code = login_response["response_detail"]["customer_bank_profiles"][0]["bank"]["code"]
        sel_bill = get_selected_airtime_biller(selected_network)
        # log.info('>>> selected accounts:  ' + customer_account)

        if  beneficiary_phone is None:
            beneficiary_phone = msisdn

        headers = {
            "Authorization": f'Bearer {access_token}',
            "Content-Type": "application/json",
        }
        log.info('selected_biller' + str())
        url = str(settings.USSD_AIRTIME_BASE_URL)
        # url = "http://127.0.0.1:8000/transactions/airtime_data/"

        payload = json.dumps({
            "phone_number":beneficiary_phone,
            "amount":amount,
            "pin":user_pin,
            "user_ref": institution_code + generate_random_ref(20),
            "remark":"ussd:" + msisdn + ":" + beneficiary_phone,
            "currency":1,
            "sender_account": customer_account,
            "sender_institution_code":institution_code,
            "biller_code":sel_bill.biller_code,
            "payment_code":sel_bill.payment_code
            })
        
        response = requests.request('POST', url, headers=headers, data=payload)
        response = response.json()
        log.info(f"USSD BUY AIRTIME RESPONSE :::  {response}")

        if response.get('response_code') == settings.SUCCESS_RESPONSE:
                #cache.set(red_key, response)
                return " airtime/data success " + str(response)
                
        else:
            return " airtime/data failure " + str(response)
    else:
        return "cannot find user details in memory"
    
def ussd_buy_aitirme_data(msisdn, selected_account, sessionid, amount, user_pin, selected_network, beneficiary_phone = None):
    log = logging.getLogger("django")
    log.info('>>> buy airtime data task started in background')

    red_key = 'user_ussd_details:' + msisdn + sessionid
    
    user_details = cache.get(red_key)
    
    access_token = user_details["response_detail"]["token_parameters"]["access_token"]
    
    if selected_account is None:
        customer_account = get_ussd_default_account(user_details)[0]
    else:    
        customer_account = get_selected_account(selected_account, user_details)[0]

    institution_code = user_details["response_detail"]["customer_bank_profiles"][0]["bank"]["code"]
    sel_bill = get_selected_airtime_biller(selected_network)
    # log.info('>>> selected accounts:  ' + customer_account)

    if  beneficiary_phone is None:
        beneficiary_phone = msisdn

    if user_details is not None:

        #url = reverse("get_agent_profile", request=request)

        headers = {
            "Authorization": f'Bearer {access_token}',
            "Content-Type": "application/json",
        }
        log.info('selected_biller' + str())
        url = str(settings.USSD_AIRTIME_BASE_URL)

        payload = json.dumps({
            "phone_number":beneficiary_phone,
            "amount":amount,
            "pin":user_pin,
            "user_ref": institution_code + generate_random_ref(20),
            "remark":"ussd:" + msisdn + ":" + beneficiary_phone,
            "currency":1,
            "sender_account": customer_account,
            "sender_institution_code":institution_code,
            "biller_code":sel_bill.biller_code,
            "payment_code":sel_bill.payment_code
            })
        
        response = requests.request('POST', url, headers=headers, data=payload)
        response = response.json()

        if response.get('response_code') == settings.SUCCESS_RESPONSE:
                #cache.set(red_key, response)
                return " airtime/data success " + str(response)
                
        else:
            return " airtime/data failure " + str(response)
    else:
        return "cannot find user details in memory"
    
def get_selected_account(user_input, user_details):
    #log = logging.getLogger("django")

    if user_details is not None:

        profile_accounts = user_details["response_detail"]["customer_bank_profiles"][0]["profile_account"]

        ind = 1
        if profile_accounts is not None:
            for profile_account in profile_accounts:
                account_name = profile_account['account_name']
                account_number = profile_account['account_number']
                # log.info('account number >>' + account_number)
                if ind == int(user_input):
                    # log.info('index=' + str(ind))
                    return [account_number, account_name]
                else:    
                    ind = ind + 1
        else:
            return None

def get_selected_account_v2(user_input, ussd_user):
    #log = logging.getLogger("django")

   if ussd_user is not None:
        accounts = ussd_user.get("accounts", [])
        account_name = ussd_user.get("name")

        ind = 1
        for account in accounts:
            if ind == int(user_input):
                # log.info('index=' + str(ind))
                return [account, account_name]  # Return the correct account
            ind += 1  # Increment index
        else:
            return None



def get_ussd_default_account(user_details):
    #log = logging.getLogger("django")

    if user_details is not None:

        profile_accounts = user_details["response_detail"]["customer_bank_profiles"][0]["profile_account"]

        if profile_accounts is not None:
            for profile_account in profile_accounts:
                account_name = profile_account['account_name']
                account_number = profile_account['account_number']
                # log.info('account number >>' + account_number)
                if profile_account['channel_access'] == 'ALLOW':
                    # log.info('index=' + str(ind))
                    return [account_number, account_name]
                
        return None 
    

def get_ussd_default_account_v2(ussd_user):
    #log = logging.getLogger("django")

    if ussd_user is not None:
        account_name = ussd_user["name"]
        account_number = ussd_user["accounts"][0]
        return [account_number, account_name]
                
    return None 
                   

def generate_random_ref(length):
    # Generate a string of random digits and lowercase letters
    chars = string.digits + string.ascii_lowercase
    ref = ''.join(random.choice(chars) for _ in range(length))
    return ref

def get_selected_airtime_biller(selected_biller):
    #log = logging.getLogger("django")
    details = cache.get('airtime_data_biller')
    
    if details is not None:

        ind = 1

        for detail in details:
            #if selected_biller.isdigit() :
            if selected_biller.isdigit() and ind == int(selected_biller):
                    return detail
            if str(selected_biller) in detail.biller_name:
                    return detail
            
            ind = ind + 1

def get_selected_other_bank(selected_bank, sessionID):
    #log = logging.getLogger("django")
    details = cache.get(f'banklist{sessionID}')
   
    if details is not None:

        ind = 1

        for detail in details:
            #if selected_biller.isdigit() :
            if ind == int(selected_bank):
                    return detail
            
            ind = ind + 1

    return None        

def doUSSDNameEnquiry(dest_account_number, msisdn, sessionid, type, dest_institution_code=None, selected_account_number=None):
        
        user_details = getUserInMemory(msisdn, sessionid)
     
        if selected_account_number is None:
            get_account = get_ussd_default_account(user_details)
        else:  
            get_account = get_selected_account(selected_account_number, user_details)    

        if get_account is None:
                return None

        sender_account_number =   get_account[0]
    
        sender_institution_code = user_details["response_detail"]["customer_bank_profiles"][0]["bank"]["code"]
        bank_alias = user_details["response_detail"]["customer_bank_profiles"][0]["bank"]["alias"]
       
        if dest_institution_code is None:
            dest_institution_code = sender_institution_code
        else:
            match_bank = get_selected_other_bank(dest_institution_code, sessionid)
            dest_institution_code = match_bank['nip_code']  

        name_enq_details = get_name_enquiry(
                dest_account_number,
                dest_institution_code,
                sender_account_number,
                sender_institution_code,
                type=type,
                app_code=bank_alias,
            )
        if name_enq_details['response_code'] == settings.SUCCESS_RESPONSE: 
            return name_enq_details
        else:
            return None

def doUSSDNameEnquiry_v2(dest_account_number, ussd_input, msisdn, sessionid, type, dest_institution_code=None, selected_account_number=None):
        
        ussd_user = get_ussd_profile(msisdn, ussd_input)
       
        if selected_account_number is None:
            get_account = get_ussd_default_account_v2(ussd_user)
        else:  
            get_account = get_selected_account_v2(selected_account_number, ussd_user)    
            # Look for how you handled get these accounts, or implement a way from cache

        if get_account is None:
                return None

        sender_account_number =   get_account[0]
    
        sender_institution_code = ussd_user["bank_code"]
        bank_alias = ussd_user["app_code"]
       
        if dest_institution_code is None:
            dest_institution_code = sender_institution_code
        else:
            match_bank = get_selected_other_bank(dest_institution_code, sessionid)
            dest_institution_code = match_bank['nip_code']  
            # Check if this cache loads or exists

        name_enq_details = get_name_enquiry(
                dest_account_number,
                dest_institution_code,
                sender_account_number,
                sender_institution_code,
                type=type,
                app_code=bank_alias,
            )
        # First log each stage to see if it fails before prod url call
        # Then call the test endpoint and see outcome.
        if name_enq_details['response_code'] == settings.SUCCESS_RESPONSE: 
            return name_enq_details
        else:
            return None


def getUserInMemory(msisdn, sessionid):
    red_key = 'user_ussd_details:' + msisdn + sessionid
    user_details = cache.get(red_key)
    
    if user_details is None:
        return None
    else:
        return user_details

def ussd_send_money(msisdn, selected_account, sessionid, amount, user_pin, beneficiary_account, url, benf_int_code):
    log = logging.getLogger("django")
    log.info(f'>>> send - money for: {sessionid} task started in background')

    user_details = getUserInMemory(msisdn, sessionid)

    if user_details is not None:
        access_token = user_details["response_detail"]["token_parameters"]["access_token"]

        if selected_account is None:
           customer_account = get_ussd_default_account(user_details)[0]
        else:    
            customer_account = get_selected_account(selected_account, user_details)[0]

        institution_code = user_details["response_detail"]["customer_bank_profiles"][0]["bank"]["code"]    

        if benf_int_code is None:
            dest_institution_code = institution_code
        else:
            match_bank = get_selected_other_bank(benf_int_code, sessionid)
            dest_institution_code = match_bank['nip_code'] 

        benef_name = cache.get(beneficiary_account)
        
        remark =  "USSD-Transfer|" + benef_name
        # strimmed_ramark = remark[:50]
        headers = {
            "Authorization": f'Bearer {access_token}',
            "Content-Type": "application/json",
        }

        payload = json.dumps(
        {
            "pin": user_pin,
            "user_ref": generate_random_ref(30),
            "amount": amount,
            "currency": "1",
            "sender_account": customer_account,
            "sender_institution_code": institution_code,
            "destination_account_number": beneficiary_account,
            "destination_institution_code": dest_institution_code,
            "destination_account_name": benef_name,
            "remark": remark
        })
        
        response = requests.request('POST', url, headers=headers, data=payload)
        response = response.json()

        if response.get('response_code') == settings.SUCCESS_RESPONSE:
                
                return " send-money success " + str(response)
                
        else:
            return " send-money failure " + str(response)
    else:
        return "cannot find user details in memory"

def get_ussd_message(message_type, company_name):

    if company_name == "":
        wel_msge = ""
    else:
        wel_msge =  "Welcome to " + company_name + ", "   

    if message_type == 'no_profile':
            return wel_msge + "You do not have account or online USSD profile, Select option below : \n\n7.Open Bank Account \n\n8.Create Online Profile"  
    elif  message_type == 'main': 
            return wel_msge + "Kindly select from the option below:"+ "\n\n1.Balance Equiry\n2.Airtime Self\n3.Airtime Other\n4.Trsf-My Bank\n5.Trsf-Other Bank" 
    if message_type == 'exception_message':
            return wel_msge + "Something is not right, try-again or  Select option below : \n\n7.Open Bank Account \n\n8.Create Online Profile"  
    else:
        return "Unknown Message"

def processed_bank_list(bank_lists, sessionID):
    log = logging.getLogger("django")
    memory_key = f'banklist{sessionID}'

    if bank_lists is not None:
        cache.set(memory_key, bank_lists) 
       
        ind = 1
        produced_string = ""
        for bank_list in bank_lists:
                
            bank_name = bank_list['name']
            produced_string = f'{produced_string}\n{ind}.{bank_name}' 

            ind = ind + 1
       
        if produced_string != "":
            message = "Kindly select from bank list below: \n" + produced_string
            return {'message': message, 'next_action': 'requets'}

    # test this senario
    message = "No bank found for this beneficiary account"
    return {'message': message, 'next_action': 'end'}
            

    
    

    
