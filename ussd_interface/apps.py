from django.apps import AppConfig



class UssdInterfaceConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'ussd_interface'

    def ready(self):
        from bank.models import Bank
        from bill_payment.models import AirtimeBiller 
        from django.core.cache import cache
        import logging
        log = logging.getLogger("django")
        #Your code here
        ussd_app_bank = Bank.objects.all()
        airtime_data_biller = AirtimeBiller.objects.filter(type='Airtime')
        cache.set('ussd_app_banks', ussd_app_bank, timeout=None)
        cache.set('airtime_data_biller', airtime_data_biller, timeout=None)
        log.info(">>> Banks are loaded in cache memory for USSD fast loading") 
        log.info(">>> Billers are loaded in cache memory for USSD fast loading") 
