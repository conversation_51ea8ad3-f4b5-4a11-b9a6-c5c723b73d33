import threading
from datetime import datetime, timedelta
from threading import Thread
import logging
import re
from ussd_interface.util import (
    get_pin_entry, 
    get_account_number_menu,
    get_account_number_menu_v2,
    ussd_validate_pin_v2,
    ussd_buy_aitirme_data,
    ussd_buy_aitirme_data_v2,
    get_set_pin_menu,
    ussd_validate_pin,
    get_amount_entry,
)

def get_airtime_self_flow(ussd_input, user_input, msisdn, network, sessionid):
    log = logging.getLogger("django")
    log.info(">>> entering airtime self menu:" + sessionid)
    #improve this by loading main menu from configuration for easy changes
    
    main_entry_pattern = r"\*\d{4}\*\d{2,4}\*2"
    airtime_self_entry_pattern = main_entry_pattern + "$"
    select_account_pattern = main_entry_pattern + "\*\d{1,6}$"
    input_amount_pattern = main_entry_pattern + "\*\d{1,6}\*\d{2,6}$"
    pin_pattern = main_entry_pattern + "\*\d{1,6}\*\d{2,6}\*\d{4,6}$"

    #log.info("pin_pattern:"+ str(pin_pattern))
    #log.info("select_account_pattern:"+ str(select_account_pattern))
    
    if re.match(airtime_self_entry_pattern, ussd_input):
        log.info(">>> entering airtime self select account menu:" + sessionid)
        get_account = get_account_number_menu_v2(msisdn, ussd_input)
        message = get_account['message']
        next_action = get_account['next_action']

    elif re.match(select_account_pattern, ussd_input):
        log.info(">>> entering aitime self input amount menu:" + sessionid)
        message = get_amount_entry()
        next_action = "request"    
  
    elif re.match(input_amount_pattern, ussd_input):
        log.info(">>> entering aitime self input pin menu:" + sessionid)
        message = get_pin_entry()
        next_action = "request"

    elif re.match(pin_pattern, ussd_input):
        #split innput and get account selected
        result = ussd_input.split("*")
        selected_account = str(result[4])
        selected_network = network
        amount = str(result[5])
        user_pin = user_input
        # validate pin
        

        if ussd_validate_pin_v2(user_pin, msisdn, ussd_input):

            log.info(">>> pin valid with and going for artime vending:" + sessionid)
            message = "Airtime recharge of " + amount +  " naira is successful, thank you"
            next_action = "end"

            # Start a new thread to run the task
            # task_thread = threading.Thread(target=ussd_buy_aitirme_data, args=(msisdn, selected_account, sessionid, amount, user_pin, selected_network)) 
            task_thread = threading.Thread(target=ussd_buy_aitirme_data_v2, args=(msisdn, ussd_input, selected_account, amount, user_pin, selected_network)) 
            task_thread.start()
        else: 
            set_pin_menu = get_set_pin_menu('request')
            message = set_pin_menu['message']
            next_action = set_pin_menu['next_action']   

    return {'message': message, 'next_action': next_action}    