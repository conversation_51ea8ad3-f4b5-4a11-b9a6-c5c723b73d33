from django.shortcuts import render
from rest_framework import generics, viewsets, status
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from oauth2_provider.contrib.rest_framework import permissions
from rest_framework.reverse import reverse

from threading import Thread
import logging
from datetime import timed<PERSON>ta
from bank.models import Bank
# from logic_omnichannel import settings
from django.conf import settings
from common.functions import set_response_data
import re

from ussd_interface.util import get_ussd_message
from .main_menu import get_main_menu
from .balance_enquiry import get_balance_enquiry_flow

from .aritime_self import get_airtime_self_flow
from .airtime_others import get_airtime_other_flow
from .transfer_my_bank import get_transfer_mybank_flow
from .transfer_others import get_transfer_others_flow

# Create your views here.

class ProcessCreditSwitch(generics.GenericAPIView):
    #permission_classes = [permissions.TokenHasReadWriteScope]
    permission_classes = ()
    #permission_classes = (IsThirdParty,)

    def get(self, request):
        log = logging.getLogger("django")
        send_money_url = reverse('send_money', request=request)
        try:
            """
            process creditswitch ussd input
            """
            msisdn = request.GET['msisdn']
            user_input = request.GET['input']
            sessionid = request.GET['sessionid']
            network = request.GET['network']
            action = request.GET['action']
            ussd_input = request.GET['allInput']
            #msisdn=**************&input=*444&sessionid=*********&network=MTN&action=begin&allinput=*444*1
            main_entry_pattern = r"\*\d{4}\*\d{2,4}$"
            balance_enquiry_pattern = r"^\*[0-9]{4}\*[0-9]{2,4}\*1"
            airtime_self_pattern = r"^\*\d{4}\*\d{2,4}\*2"
            airtime_other_pattern = r"^\*\d{4}\*\d{2,4}\*3"
            transfer_my_bank_pattern = r"^\*\d{4}\*\d{2,4}\*4"
            transfer_other_bank_pattern = r"^\*\d{4}\*\d{2,4}\*5"
           
            # determine what flow to use based on user input
            if re.match(main_entry_pattern, ussd_input):
                
                data = get_main_menu(ussd_input, msisdn, sessionid)
            
            elif re.match(balance_enquiry_pattern, ussd_input):

                data =  get_balance_enquiry_flow(ussd_input, user_input, msisdn, network, sessionid)
            
            elif re.match(airtime_self_pattern, ussd_input):
        
                data = get_airtime_self_flow (ussd_input, user_input, msisdn, network, sessionid)

            elif re.match(airtime_other_pattern, ussd_input):

                data = get_airtime_other_flow (ussd_input, user_input, msisdn, network, sessionid)

            elif re.match(transfer_my_bank_pattern, ussd_input):
                
                data = get_transfer_mybank_flow(ussd_input, user_input, msisdn, network, sessionid, send_money_url)

            elif re.match(transfer_other_bank_pattern, ussd_input):
               
                data = get_transfer_others_flow(ussd_input, user_input, msisdn, network, sessionid, send_money_url)

            else:  
                message = 'Unknown Service Code'
                next_action = 'end'
                data = {'message': message, 'next_action': next_action}

            returned_data = {'message': data['message'], 'action': data['next_action']}
            return Response(returned_data, status=status.HTTP_200_OK)

        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"ussd application error, Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)