import threading
from datetime import datetime, timedelta
from threading import Thread
import logging
import re
from django.core.cache import cache
from ussd_interface.util import (
    get_account_number_menu,
    get_account_number_menu_v2,
    get_set_pin_menu,
    ussd_validate_pin,
    ussd_validate_pin_v2,
    get_amount_entry,
    doUSSDNameEnquiry,
    doUSSDNameEnquiry_v2,
    ussd_send_money,
    get_company_details,
    authenticate_ussd,
    authenticate_ussd_v2,
    get_ussd_message,
)   

def get_transfer_mybank_flow(ussd_input, user_input, msisdn, network, sessionid, url=None):
    log = logging.getLogger("django")
    
    main_entry_pattern = r"\*\d{4}\*\d{2,4}\*4"
    transfer_my_bank_entry_pattern = main_entry_pattern + "$"
    select_account_pattern = main_entry_pattern + "\*\d{1,6}$"
    input_amount_pattern = main_entry_pattern + "\*\d{1,6}\*\d{2,9}$"
    input_benf_account_number_pattern = main_entry_pattern + "\*\d{1,6}\*\d{2,9}\*\d{10}$"
    pin_pattern = main_entry_pattern + "\*\d{1,6}\*\d{2,9}\*\d{10}\*\d{4,6}$"
    # direct short code *7737*{bank_short_code}*4*{amount}*{another_account}#
    direct_pattern = main_entry_pattern + "\*\d{2,9}\*\d{10}$"
    direct_pin_pattern = main_entry_pattern + "\*\d{2,9}\*\d{10}\*\d{4,6}$"


    #log.info("pin_pattern:"+ str(pin_pattern))
    
    if re.match(transfer_my_bank_entry_pattern, ussd_input):
        log.info(">>> entering transfer my-bank select account menu:" + sessionid)
        # get_account = get_account_number_menu(msisdn, sessionid)
        get_account = get_account_number_menu_v2(msisdn, ussd_input)
        message = get_account['message']
        next_action = get_account['next_action']

    elif re.match(select_account_pattern, ussd_input):
        log.info(">>> entering transfer my-bank input amount menu:" + sessionid)
        message = get_amount_entry()
        next_action = "request"    
  
    elif re.match(input_amount_pattern, ussd_input):
        log.info(">>> entering transfer my-bank input beneficiary account:" + sessionid)
        message = "Kindly input Benefiary Account NUMBER:"
        next_action = "request"

    elif re.match(input_benf_account_number_pattern, ussd_input) or re.match(direct_pattern, ussd_input):
        log.info(">>> entering transfer my-bank input pin menu:" + sessionid)
        
        if re.match(direct_pattern, ussd_input):
            result = ussd_input.split("*")
            benef_acount = str(result[5])
            selected_account = None
            company_short_code = "*" + str(result[1]) + "*" + str(result[2])
            company_details = get_company_details(company_short_code, msisdn)

            # if authenticate_ussd(msisdn, company_details.alias, sessionid) is None or company_details is None:
            if authenticate_ussd_v2(msisdn, company_details) is None or company_details is None:
                #load this from a single source 
                message = get_ussd_message('no_profile', company_details.name)
                next_action = "request"
                return {'message': message, 'next_action': next_action}

        else:
            result = ussd_input.split("*")
            selected_account = str(result[4])
            benef_acount = str(result[6])    
 
        # print(selected_account ,benef_acount )
        name_enq = doUSSDNameEnquiry_v2(benef_acount, ussd_input, msisdn, sessionid, "INTRA", None, selected_account)

        if name_enq is not None:
            benef_name  = name_enq['response_description']['account_name']
            #cache.set('benef_acount')
            cache.set(benef_acount, benef_name)
            message = "Your are about to transfer to " + benef_name + " charges apply, Kindly input your PIN:"
            next_action = "request"
            
        else:
            message = "Sorry, unable to validate beneficiary account number, try again"
            next_action = "end"                

    elif re.match(pin_pattern, ussd_input) or  re.match(direct_pin_pattern, ussd_input):
        #split innput and get account selected
        
        result = ussd_input.split("*")
        user_pin = user_input
       
        if re.match(direct_pin_pattern, ussd_input):
            selected_account = None
            amount = str(result[4])
            benef_acount = str(result[5])
        else:
            selected_account = str(result[4])
            amount = str(result[5])
            benef_acount = str(result[6])    
            
        # validate pin
        
        if ussd_validate_pin_v2(user_pin, msisdn, ussd_input):

            log.info(">>> pin valid going for transfer with my bank:" + sessionid)
            message = "Transfer of " + amount + " naira to " +  benef_acount + " is processing, thank you"
            next_action = "end"

            # Start a new thread to run the task
            task_thread = threading.Thread(target=ussd_send_money, args=(
                (msisdn, selected_account, sessionid, amount, user_pin, benef_acount, url, None))) 
            task_thread.start()
        else: 
            set_pin_menu = get_set_pin_menu('request')
            message = set_pin_menu['message']
            next_action = set_pin_menu['next_action']   
 
    return {'message': message, 'next_action': next_action}  