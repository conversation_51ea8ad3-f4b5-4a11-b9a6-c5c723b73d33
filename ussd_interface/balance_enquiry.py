import threading
from datetime import datetime, timedelta
from threading import Thread
import logging
import re
from ussd_interface.util import (
    get_pin_entry, 
    get_account_number_menu,
    get_account_number_menu_v2,
    get_and_send_customer_balance_v2,
    ussd_validate_pin_v2,
    get_and_send_customer_balance, 
    get_set_pin_menu,
    ussd_validate_pin,
)

def get_balance_enquiry_flow(ussd_input, user_input, msisdn, network, sessionid):
    log = logging.getLogger("django")
    log.info(">>> entering balance enquiry menu:" + sessionid)

    balance_entry_pattern = r"\*\d{4}\*\d{2,4}\*1$"
    account_selected_pattern = r"\*\d{4}\*\d{2,4}\*1\*\d{1,6}$"
    balance_enq_pin_pattern = r"\*\d{4}\*\d{2,4}\*1\*\d{1,6}\*\d{4,6}$"
    
    if re.match(balance_entry_pattern, ussd_input):
        log.info(">>> entering balance enquiry select account menu:" + sessionid)
        # get_account = get_account_number_menu(msisdn, sessionid)
        get_account = get_account_number_menu_v2(msisdn, ussd_input)
        message = get_account['message']
        next_action = get_account['next_action']
  
    elif re.match(account_selected_pattern, ussd_input):
        log.info(">>> entering balance enquiry input pin menu:" + sessionid)
        message = get_pin_entry()
        next_action = "request"

    elif re.match(balance_enq_pin_pattern, ussd_input):
        #split innput and get account selected
        result = ussd_input.split("*")
        selected_account = str(result[4])
        user_pin = user_input
        # validate pin
        

        # if ussd_validate_pin(user_pin, msisdn, sessionid):
        if ussd_validate_pin_v2(user_pin, msisdn, ussd_input):

            log.info(">>> balance completed:" + sessionid)
            message = "Thank you, your account balance will be sent as SMS or Email"
            next_action = "end"

            # Start a new thread to run the task
            task_thread = threading.Thread(target=get_and_send_customer_balance_v2, args=(msisdn, selected_account, ussd_input)) 
            task_thread.start()
        else: 
            set_pin_menu = get_set_pin_menu('request')
            message = set_pin_menu['message']
            next_action = set_pin_menu['next_action']   

    return {'message': message, 'next_action': next_action}    



