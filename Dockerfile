FROM python:3.9-slim

ENV PYTHONDONTWRITEBYTECODE 1

ENV PYTHONUNBUFFERED 1

RUN apt-get update && apt-get install -y \
    pkg-config \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

RUN mkdir -p /logicshift/omni_channel /logicshift/logs /logicshift/omni_channel/media_root

WORKDIR /logicshift/omni_channel

RUN pip install --upgrade pip

COPY requirements.txt /logicshift/omni_channel/

RUN pip install -r requirements.txt

COPY . /logicshift/omni_channel/

RUN rm -rf /logicshift/omni_channel/media_root/*

EXPOSE 8000

CMD python manage.py migrate bank --fake && python manage.py makemigrations && python manage.py migrate && python manage.py runserver 0.0.0.0:8000