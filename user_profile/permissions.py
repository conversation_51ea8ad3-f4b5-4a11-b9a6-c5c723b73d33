from rest_framework.permissions import BasePermission


class IsBankSuperAdmin(BasePermission):
    '''
        Global permision check for Bank Super Admin user
    '''

    def has_permission(self, request, view):
        user = request.auth.user
        if hasattr(user, 'bankadmin'):
            return user.bankadmin.is_bank_superadmin
        
class IsBankAdmin(BasePermission):
    '''
        Global permision check for Bank Admin user
    '''

    def has_permission(self, request, view):
        user = request.auth.user
        if hasattr(user, 'bankadmin'):
            return True


class IsOwner(BasePermission):
    '''
        Global permission check if you requesting user is owner of the object
        based on the parsed primary key (pk) from the request url
    '''

    def has_permission(self, request, view):
        user = request.auth.user
        pk=request.parser_context['kwargs']['pk']
        if user.id == pk:
            return True
        