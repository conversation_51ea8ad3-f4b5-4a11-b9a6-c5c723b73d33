from django.urls import path, include

from .views import (
    <PERSON><PERSON><PERSON><PERSON>,
    GetKYCRequirementList,
    GetVerificationStatus,
    Login,
    ActivateDeactivateBankAdmin,
    ChangePassword,
    CreateBankAdminUser,
    GetAllBankAdminUsers,
    Logout,
    OpenAccount,
    RequestPasswordReset,
    GetToken,
    UpdateKYCDocs,
    VerifyUserPhoneNumber,
    VerifyUserEmail,
    SendResetPasswordOTP,
    ResetPassword,
    SendUserSMSOTP,
    SendUserEmailVerification,
    UpdateBankAdminUser,
    UpdateUserPhoneNumber,
    DeleteAccount,
    OpenAccountV2
)

urlpatterns = [
    path("create_user/", CreateUser.as_view(), name="create_user"),
    path("login/", Login.as_view(), name="user_login"),
    path(
        "activate_deactivate_bank_admin/",
        ActivateDeactivateBankAdmin.as_view(),
        name="activate_deactivate_bank_admin",
    ),
    path("change_password/", ChangePassword.as_view(), name="change_password"),
    path("create_bank_admin/", CreateBankAdminUser.as_view(), name="create_bank_admin"),
    path(
        "get_all_bank_admin/", GetAllBankAdminUsers.as_view(), name="get_all_bank_admin"
    ),
    path("get_token/<str:refresh_token>/", GetToken.as_view(), name="get_token"),
    path("logout/", Logout.as_view(), name="logout"),
    path(
        "request_password_reset/",
        RequestPasswordReset.as_view(),
        name="request_password_reset",
    ),
    path(
        "verify_user_phone_number/",
        VerifyUserPhoneNumber.as_view(),
        name="verify_user_phone_number",
    ),
    path("verify_user_email/", VerifyUserEmail.as_view(), name="verify_user_email"),
    path(
        "send_reset_password_otp/",
        SendResetPasswordOTP.as_view(),
        name="send_reset_password_otp",
    ),
    path("reset_password/", ResetPassword.as_view(), name="reset_password"),
    path("send_user_sms_otp/", SendUserSMSOTP.as_view(), name="send_user_sms_otp"),
    path(
        "send_user_email_verification/",
        SendUserEmailVerification.as_view(),
        name="send_user_email_verification",
    ),
    path(
        "update_bank_admin/<int:pk>/",
        UpdateBankAdminUser.as_view(),
        name="update_bank_admin",
    ),
    path(
        "get_verification_status/",
        GetVerificationStatus.as_view(),
        name="get_verification_status",
    ),
    path(
        "get_kyc_requirements_list/",
        GetKYCRequirementList.as_view(),
        name="get_kyc_requirements_list",
    ),
    path("update_kyc_documents/", UpdateKYCDocs.as_view(), name="update_kyc_documents"),
    path("open_account/", OpenAccount.as_view(), name="open_account"),
    path("v2/open_account/", OpenAccountV2.as_view(), name="open_account_v2"),
    path("update_user_phone_number/", UpdateUserPhoneNumber.as_view(), name="update_user_phone_number"),
    path("delete_customer_user_profile/<str:app_code>/<int:bvn>/", DeleteAccount.as_view({"delete": "destroy"}), name="delete_account"),

]
