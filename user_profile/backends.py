from common.functions import ussd_authentication
from user_profile.views import get_app_code
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from rest_framework.exceptions import AuthenticationFailed

UserModel = get_user_model()


class CustomUserBackend(ModelBackend):
    def authenticate(
        self, request, username=None, password=None, app_code=None, **kwargs
    ):
        if username is None:
            username = kwargs.get(UserModel.USERNAME_FIELD)
        if username is None or password is None:
            return
        USSD = False
        INTERNAL = False
        if app_code == None:
            # possible token generation authentication, it comes in only with username and password,
            # we get back the app code attached to this user from the internal dynamic memory
            app_code, request_platform = get_app_code(username)
            if request_platform and request_platform.upper().startswith(
                "USSD APPLICATION"
            ):
                USSD = True
            if request_platform and request_platform.upper().startswith("INTERNAL"):
                INTERNAL = True
            if not app_code:
                # SET bank code to default omni channel bank code <0000>
                app_code = "0000"
        try:
            if USSD:
                # for ussd application we get username as phone_number
                user = ussd_authentication(username, app_code)
            else:
                user = UserModel.objects.get(username=username, app_code=app_code)
                if (
                    hasattr(user, "bankadmin")
                    and user.bankadmin.reset_password
                    and self.user_can_authenticate(user)
                ):
                    # unique case for password reset <bank admin> this state is temporary and will
                    # only last to generate a token
                    return user
        except UserModel.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user (#20760).
            # if kwargs.get('token'):
            #     UserModel().set_password(password)
            # else:
            raise AuthenticationFailed(
                "Invalid Credentials. Kindly provide the correct details and try again"
            )
        except Exception:
            raise AuthenticationFailed(
                "Invalid Credentials. Kindly provide the correct details and try again"
            )
        else:
            if INTERNAL and self.user_can_authenticate(user):
                return user
            elif USSD and self.user_can_authenticate(user):
                return user
            elif user.check_password(password) and self.user_can_authenticate(user):
                return user
            else:
                raise AuthenticationFailed("Unable to Authenticate User")
