from enum import unique
from django.conf import settings
from django.contrib import auth
from django.contrib.auth.validators import UnicodeUsernameValidator
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
from django.core.exceptions import ValidationError
from django.db.models import constraints
from django.utils import timezone
from django.db import models, IntegrityError
from django.utils.translation import gettext_lazy as _
from django.core.mail import send_mail

from bank.models import Bank


# Create your models here.

class UserManager(BaseUserManager):

    def _create_user(self, username, email, password, **extra_fields):
        """
        Create and save a user with the given username, email, and password.
        """
        if not username:
            raise ValueError('The given username must be set')
        email = self.normalize_email(email)
        username = self.model.normalize_username(username)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_user(self, username, email=None, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        return self._create_user(username, email, password, **extra_fields)
    
    def create_superuser(self, username, email=None, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self._create_user(username, email, password, **extra_fields)

    def with_perm(self, perm, is_active=True, include_superusers=True, backend=None, obj=None):
        if backend is None:
            backends = auth._get_backends(return_tuples=True)
            if len(backends) == 1:
                backend, _ = backends[0]
            else:
                raise ValueError(
                    'You have multiple authentication backends configured and '
                    'therefore must provide the `backend` argument.'
                )
        elif not isinstance(backend, str):
            raise TypeError(
                'backend must be a dotted import path string (got %r).'
                % backend
            )
        else:
            backend = auth.load_backend(backend)
        if hasattr(backend, 'with_perm'):
            return backend.with_perm(
                perm,
                is_active=is_active,
                include_superusers=include_superusers,
                obj=obj,
            )
        return self.none()
    

class User(AbstractBaseUser, PermissionsMixin):
    """
    An abstract base class implementing a fully featured User model with
    admin-compliant permissions.

    Username and password are required. Other fields are optional.
    """
    username_validator = UnicodeUsernameValidator()

    username = models.CharField(
        _('username'),
        max_length=150,
        help_text=_('Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.'),
        validators=[username_validator],
        error_messages={
            'unique': _("A user with that username already exists."),
        },
    )
    first_name = models.CharField(_('first name'), max_length=150, blank=True)
    last_name = models.CharField(_('last name'), max_length=150, blank=True)
    email = models.EmailField(_('email address'), blank=True)
    app_code = models.CharField(_('App code'), max_length=15, default='0000') #default 0000 describes general OMNI CHANNEL user
    is_staff = models.BooleanField(
        _('staff status'),
        default=False,
        help_text=_('Designates whether the user can log into this admin site.'),
    )
    is_active = models.BooleanField(
        _('active'),
        default=True,
        help_text=_(
            'Designates whether this user should be treated as active. '
            'Unselect this instead of deleting accounts.'
        ),
    )
    date_joined = models.DateTimeField(_('date joined'), default=timezone.now)

    objects = UserManager()

    EMAIL_FIELD = 'email'
    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['email']

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')
        constraints = [
            models.UniqueConstraint(fields=['username', 'app_code'], name='unique_user')
        ]
        

    def clean(self):
        super().clean()
        self.email = self.__class__.objects.normalize_email(self.email)

    def get_full_name(self):
        """
        Return the first_name plus the last_name, with a space in between.
        """
        full_name = '%s %s' % (self.first_name, self.last_name)
        return full_name.strip()

    def get_short_name(self):
        """Return the short name for the user."""
        return self.first_name

    def email_user(self, subject, message, from_email=None, **kwargs):
        """Send an email to this user."""
        send_mail(subject, message, from_email, [self.email], **kwargs)
    
    # def clean_fields(self, exclude=None) -> None:
    #     super().clean_fields(exclude=exclude)
    #     try:
    #         user_model = auth.get_user_model()
    #         user_model.objects.get(username=self.username, app_code=self.app_code)
    #         raise ValidationError({
    #             'username': _(f'Username already exists for this App Code: {self.app_code}')
    #         })
    #     except user_model.DoesNotExist:
    #         return
    
    def save(self, *args, **kwargs) -> None:
        try:
            self.username = self.username.lower()
            super(User, self).save(*args, **kwargs)
        except IntegrityError:
            raise ValidationError({
                'username': _(f'Username already exists for this App Code: {self.app_code}')
            })


class ClientApplication(models.Model):
    class Meta:
        db_table = 'omni_user_client_application'
    
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True)
    account_created_from = models.CharField(max_length=40)
    last_login_from = models.CharField(max_length=50, null=True, blank=True)

    def __str__(self) -> str:
        return ''


class BankAdmin(models.Model):

    ADMIN = "admin"
    SUPER_ADMIN = "superadmin"
    MARKETER = "marketer"

    ROLE_CHOICES = (
        (ADMIN, "Admin"),
        (SUPER_ADMIN, "Super Admin"),
        (MARKETER, "Marketer"),
    )

    class Meta:
        db_table = "omni_user_bank_admin"

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True
    )
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, null=True)
    is_bank_admin = models.BooleanField(null=True)
    is_bank_superadmin = models.BooleanField(null=True)
    reset_password = models.BooleanField(default=False)
    job_description = models.TextField(max_length=250, blank=True, null=True)
    role = models.CharField(max_length=15, choices=ROLE_CHOICES, default=ADMIN)
    organization_name = models.CharField(max_length=60, blank=True, null=True)

    def __str__(self) -> str:
        return self.user.username


