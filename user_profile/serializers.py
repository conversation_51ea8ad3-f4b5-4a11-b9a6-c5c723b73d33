from dfa.models import AgentOnboardedAccount
from django.core.exceptions import ValidationError
from customer.models import CustomerProfile
from bank.models import Bank
import logging

from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

from rest_framework import serializers

from common.validators import validate_name, validate_numbers, validate_phone_number


User = get_user_model()

class UserSerializer(serializers.ModelSerializer):

    username = serializers.EmailField(required=True)
    password = serializers.CharField(min_length=5, write_only=True)
    last_name = serializers.CharField(max_length=150)
    first_name = serializers.CharField(max_length=150)
    bvn = serializers.CharField(min_length=11, max_length=11, required=True, validators=[validate_numbers])
    phone_number = serializers.CharField(required=True, validators=[validate_phone_number])
    app_code = serializers.CharField(max_length=15, required=False, allow_null=True, allow_blank=True)

    class Meta:
        model = User
        fields = ['id','username', 'first_name', 'last_name', 'bvn', 'password', 'phone_number', 'app_code']
        read_only_fields = ('id',)
    
    def validate(self, attrs):
        '''
            Extra validation to ensure the app_code is valid and it is an alias to a partner bank
        '''
        resp = super().validate(attrs)
        app_code = attrs.get('app_code')
        if app_code:
            try:
                partner_bank = Bank.objects.get(alias=app_code)
            except Bank.DoesNotExist:
                raise serializers.ValidationError({
                    'app_code': _(f'Invalid app code verify app code from admin')
                })
        return resp

    def create(self, validated_data):
        log = logging.getLogger('omni_create_user')
        _validated_data = {**validated_data}
        _validated_data.pop('password')
        log.info(f'GOT HERE:{_validated_data}')
        try:
            app_code = validated_data.get('app_code', None)
            # Before creating the user verify that bvn does not exist for this same user within the calling bank app_code
            # else raise validation error
            error = ''
            cs_profile = CustomerProfile.objects.filter(bvn=validated_data['bvn'], customer__app_code=validated_data['app_code'])
            if cs_profile:
                error = 'bvn_error'
                raise Exception
            else:
                user = User.objects.create(
                    username=validated_data['username'],
                    email=validated_data['username'],
                    first_name=validated_data['first_name'],
                    last_name=validated_data['last_name'],
                    app_code = app_code if app_code else '0000'
                )
                log.info(f'User Created Successfully {user}')
        except Exception as e:
            log.info(f'FAILED HERE:{e}')
            if error == 'bvn_error':
                raise Exception(f'User already exists with this BVN . Kindly verify BVN')
            else:
                raise Exception(f'User with email {validated_data["username"]} already exist')

        user.set_password(validated_data['password'])
        user.save()

        return user

        
class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(min_length=1)
    password = serializers.CharField(min_length=5)
    app_code = serializers.CharField(max_length=15, required=False, allow_null=True, allow_blank=True)
    
    class Meta:
        fields = ['username', 'password', 'app_code']


class ActivateDeactivateBankAdminSerializer(serializers.Serializer):
    bank_admin_id = serializers.IntegerField(required=True)
    is_active = serializers.BooleanField(default=True)

    class Meta:
        fields = ['bank_admin_id', 'is_active']


class ChangePasswordSerializer(serializers.Serializer):

    password = serializers.CharField(min_length=5)
    new_password = serializers.CharField(min_length=5)

    class Meta:
        fields = ('password', 'new_password')

    
class PhoneNumberSerializer(serializers.Serializer):

    phone_number = serializers.CharField(required=True, validators=[validate_phone_number])
    customer_id = serializers.CharField(required=True)

    class Meta:
        fields = ("phone_number", "customer_id")


class UpdateUserPhoneNumberSerializer(serializers.Serializer):

    phone_number = serializers.CharField(required=True, validators=[validate_phone_number])
    customer_id = serializers.CharField(required=True)
    validate_otp_at = serializers.CharField(max_length=11, required=True)
    otp = serializers.CharField(max_length=7, required=True)

    class Meta:
        fields = ("phone_number", "customer_id", "otp", "verify_otp_at")



class BankAdminUserSerializer(serializers.ModelSerializer):

    password = serializers.CharField(min_length=5, write_only=True)
    last_name = serializers.CharField(max_length=20, validators=[validate_name])
    first_name = serializers.CharField(max_length=20, validators=[validate_name])
    is_bank_superadmin = serializers.BooleanField(default=False)
    job_description = serializers.CharField(max_length=250, allow_blank=True)

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "first_name",
            "last_name",
            "password",
            "is_bank_superadmin",
            "job_description",
        ]
        read_only_fields = ("id",)

    def create(self, validated_data):
        user_data = {**validated_data}
        user_data["email"] = validated_data["username"]
        user_data.pop("is_bank_superadmin")
        user_data.pop("job_description")
        password = user_data.pop("password")
        admin = User.objects.create(**user_data)
        admin.set_password(password)
        admin.save()
        return admin


class GetBankAdminSerializer(serializers.ModelSerializer):

    is_bank_admin = serializers.SerializerMethodField('get_is_bank_admin')
    is_bank_superadmin = serializers.SerializerMethodField('get_is_bank_superadmin')
    job_description = serializers.SerializerMethodField('get_job_description')

    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'is_active', 'last_login', 'is_bank_admin', 'is_bank_superadmin', 'job_description']

    def get_is_bank_admin(self, obj):
        return obj.bankadmin.is_bank_admin
    
    def get_is_bank_superadmin(self, obj):
        return obj.bankadmin.is_bank_superadmin
    
    def get_job_description(self, obj):
        return obj.bankadmin.job_description


class VerifyUserPhoneNumberSerializer(serializers.Serializer):

    validate_otp_at = serializers.CharField(max_length=11, required=True)
    sms_otp = serializers.CharField(max_length=7, required=True)

    class Meta:
        fields = ['validate_otp_at', 'sms_otp'] 
    

class VerifyUserEmailSerializer(serializers.Serializer):

    id = serializers.IntegerField(required=True)
    validate_otp_at = serializers.CharField(max_length=11, required=True)
    email_otp = serializers.CharField(max_length=7, required=True)

    class Meta:
        fields = ['id', 'validate_otp_at', 'email_otp']


class RequestPasswordResetSerializer(serializers.Serializer):

    email = serializers.EmailField(required=True)
    app_code = serializers.CharField(max_length=15, required=False, allow_null=True, allow_blank=True)

    class Meta:
        fields = ('email', 'app_code')


class PasswordResetSerializer(serializers.Serializer):

    id = serializers.IntegerField(required=True)
    validate_otp_at = serializers.CharField(max_length=11, required=True)
    otp = serializers.CharField(max_length=7, required=True)
    password =serializers.CharField(min_length=5)

    class Meta:
        fields = ('id', 'validate_otp_at', 'otp', 'password')


class UpdateBankAdminUserSerializer(serializers.Serializer):
    
    password = serializers.CharField(min_length=5, write_only=True, required=False)
    last_name = serializers.CharField(max_length=20, validators=[validate_name], required=False)
    first_name = serializers.CharField(max_length=20, validators=[validate_name], required=False)
    is_bank_superadmin = serializers.NullBooleanField(default=None, required=False)
    job_description = serializers.CharField(max_length=250, allow_blank=True, required=False)

    class Meta:
        fields = ['first_name', 'last_name', 'password', 'is_bank_superadmin', 'job_description']
    
    def update(self, instance, validated_data):
        password = validated_data.get('password')
        last_name = validated_data.get('last_name')
        first_name = validated_data.get('first_name')
        is_bank_superadmin = validated_data.get('is_bank_superadmin')
        job_description = validated_data.get('job_description')
        if last_name is not None:
            instance.last_name = last_name
        if first_name is not None:
            instance.first_name = first_name
        if is_bank_superadmin is not None:
            instance.bankadmin.is_bank_superadmin = is_bank_superadmin
        if job_description is not None:
            instance.bankadmin.job_description = job_description
        if password is not None:
            instance.set_password(password)
        instance.bankadmin.save()
        instance.save()

        return instance


class OpenAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = AgentOnboardedAccount
        fields = [
            "account_type",
            "address",
            "city",
            "state",
            "means_of_id",
            "id_base_64",
            "id_issue_date",
            "id_expiry_date",
            "id_number",
            "signature_base_64",
            "profile_picture_base_64",
        ]


class BvnSerializer(serializers.ModelSerializer):
    bvn = serializers.CharField(min_length=11, max_length=11, required=True, validators=[validate_numbers])
    app_code = serializers.CharField(max_length=15, required=False, allow_null=True, allow_blank=True)
    class Meta:
        fields = ['bvn', 'app_code']


class OpenAccountSerializerV2(serializers.Serializer):
    
    SAVINGS = "savings"
    CURRENT = "current"
    SINGLE = "single"
    MARRIED = "married"
    MARRIED_CHOICES = (
        (SINGLE, "Single"),
        (MARRIED, "Married")
    )
    ACCOUNT_TYPE = (
        (SAVINGS, "Savings"),
        (CURRENT, "Current")
    )

    account_type = serializers.ChoiceField(choices=ACCOUNT_TYPE, required=True)
    city = serializers.CharField(required=False)
    state = serializers.CharField(required=False)
    address = serializers.CharField(required=False)
    tin = serializers.CharField(required=True) # CHECK IF REQUIRED 
    nin = serializers.CharField(required=True)
    sector = serializers.CharField(required=True) # CHECK IF REQUIRED
    marital_status = serializers.ChoiceField(choices=MARRIED_CHOICES, required=True)

    next_kin_address = serializers.CharField(required=False)
    next_kin_name = serializers.CharField(required=False)
    next_kin_relationship = serializers.CharField(required=False)
    next_kin_phone = serializers.CharField(required=False)
    next_kin_email = serializers.CharField(required=False)
    
