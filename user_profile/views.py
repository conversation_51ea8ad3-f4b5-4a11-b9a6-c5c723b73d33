from common.permissions import DFAAccessPermission, MobileAccessPermission
from dfa.models import Agent, AgentOnboardedAccount
from customer.serializers import KYCDocSerializer
from datetime import datetime
import requests
from threading import Thread
import logging
from copy import deepcopy

import pyotp

from django.contrib.auth import authenticate, get_user_model
from django.contrib.auth.models import update_last_login
from django.core.exceptions import ObjectDoesNotExist
from django.core.cache import cache
from django.conf import settings
from django.contrib.auth.hashers import check_password


from oauth2_provider.contrib.rest_framework import permissions
from rest_framework import status, generics, viewsets
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework.exceptions import AuthenticationFailed, ValidationError
from rest_framework.reverse import reverse
from rest_framework.filters import SearchFilter

from common.models import BVN
from bank.models import Bank, BankAccountType
from common.functions import (
    account_has_platform_access,
    clean_login_log_data,
    get_bvn_dob,
    get_client_credentials,
    get_daily_limit,
    get_latest_app_version,
    get_platform,
    get_reference,
    get_serializer_key_error,
    get_user_token,
    is_bank_admin_username_valid,
    send_email_verification,
    get_onboarding_status,
    set_response_data,
    CustomPageNumberPagination,
    logout_user,
    otp_rand,
    get_email_token,
    ussd_authentication,
)
from customer.models import CustomerAccount, CustomerProfile, KYCDocs
from verdant.models import WalletType
from common.services import (
    account_opening,
    mifos_generate_wallet_no,
    name_enquiry,
    send_email,
    send_sms,
    send_sms_test,
)
from .serializers import (
    OpenAccountSerializer,
    UserSerializer,
    LoginSerializer,
    ActivateDeactivateBankAdminSerializer,
    ChangePasswordSerializer,
    BankAdminUserSerializer,
    GetBankAdminSerializer,
    VerifyUserPhoneNumberSerializer,
    VerifyUserEmailSerializer,
    RequestPasswordResetSerializer,
    PasswordResetSerializer,
    UpdateBankAdminUserSerializer,
    PhoneNumberSerializer,
    UpdateUserPhoneNumberSerializer,
    OpenAccountSerializerV2
)
from .models import ClientApplication, BankAdmin
from .permissions import IsBankSuperAdmin, IsOwner
from .serializers import BvnSerializer

User = get_user_model()


def set_app_code(username, app_code, request_platform=None):
    app_code_dict = cache.get("app_code_dict")
    if app_code_dict:
        app_code_dict[username] = (app_code, request_platform)
    else:
        app_code_dict = {username: (app_code, request_platform)}
    cache.set("app_code_dict", app_code_dict)


def get_app_code(username):
    app_code_dict = cache.get("app_code_dict")
    if app_code_dict:
        app_code, request_platform = app_code_dict.pop(username, (None, None))
        cache.set("app_code_dict", app_code_dict)
        return (app_code, request_platform)
    return (None, None)


class CreateUser(generics.GenericAPIView):

    permission_classes = (AllowAny,)
    serializer_class = UserSerializer

    def post(self, request):

        log = logging.getLogger("omni_create_user")
        log.info(
            f'entering CREATE USER API for username: {request.data.get("username")}'
        )

        try:
            """
            Validate request point of entry
            """
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
            log.info(f"FROM CREATE USER API: request from {request_platform}")
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Credentials",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            log.info(f"exiting CREATE USER API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Application",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            log.info(f"exiting CREATE USER API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            """
            Create user with payload request (the serializer has a create function
            which create user if payload is valid)
            """
            serializer = UserSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            log.info(f"FROM CREATE USER API: request payload successfully validated")

            if request_platform.upper().startswith("VERDANT DIGITAL BANK"):
                """
                For verdant digtal bank this is our own digital bank and the create account action
                also creates a wallet no for the user which will mapped one - to - one with the
                customer's mobile number. so the customer will have his mobile number as his
                wallet number.
                """
                wallet_type = WalletType.objects.get(short_name="sav")
                wallet_args = {
                    "email": serializer.validated_data["username"],
                    "first_name": serializer.validated_data["first_name"],
                    "last_name": serializer.validated_data["last_name"],
                    "mobile_number": serializer.validated_data["phone_number"],
                    "wallet_id": wallet_type.wallet_id,
                    "short_name": wallet_type.short_name,
                    "fineract_product_id": wallet_type.fineract_product_id,
                }
                log.info(f"Wallet args -> {wallet_args}")
                resp = mifos_generate_wallet_no(wallet_args)
                log.info(f"response from mifos generate wallet -> {resp}")
                if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                    wallet_no = resp.get("response_detail", {}).get("account_number")
                    nuban = resp.get("response_detail", {}).get("nuban")
                    mifos_customer_id = resp.get("response_detail", {}).get("client_id")
                else:
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description=resp.get(
                            "response_description", "Unable to Create Account"
                        ),
                        detail=resp.get("response_detail", "Unable to Create Account"),
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)

            user = serializer.save()

            bvn = serializer.validated_data["bvn"]
            phone_number = serializer.validated_data["phone_number"]

            # Profile.objects.create(user=user, bvn=bvn, phone_number=phone_number)
            profile = CustomerProfile.objects.create(
                customer=user, bvn=bvn, phone_number=phone_number, dob=get_bvn_dob(bvn)
            )
            ClientApplication.objects.create(
                user=user, account_created_from=request_platform
            )
            log.info(f"FROM CREATE USER API:Customer Profile Created Successfully")

            if request_platform.upper().startswith("VERDANT DIGITAL BANK"):
                """
                After Successfully creating Customer Profile, for Verdant we have already created
                the wallet number so we populate the customer account with the wallet number.
                the last 10 digit will serve as the customer account number which is mapped one
                to one with the wallet number.
                """
                bank = Bank.objects.get(alias=serializer.validated_data["app_code"])
                account_type = BankAccountType.objects.get(code="verdant")
                account_number = phone_number[len(phone_number) - 10 :]
                CustomerAccount.objects.create(
                    profile=profile,
                    account_number=account_number,
                    bank=bank,
                    wallet_type=wallet_type,
                    is_wallet=True,
                    daily_limit=wallet_type.daily_limit,
                    wallet_no=wallet_no,
                    nuban=nuban,
                    account_type=account_type,
                )
                profile.no_of_customer_account = profile.no_of_customer_account + 1
                profile.mifos_customer_id = mifos_customer_id
                profile.save()

            username = serializer.validated_data["username"]
            password = serializer.validated_data["password"]
            app_code = serializer.validated_data["app_code"]
            if app_code:
                # storing app code in internal memory for future use by the token generation view
                set_app_code(username, app_code)

            log.info(f"FROM CREATE USER API:Generating Token for user")
            token_endpoint = reverse("token", request=request)
            token_parameters = get_user_token(
                client_id,
                client_secret,
                username,
                password,
                request_platform,
                token_endpoint,
            )

            data = {
                "response_code": "00",
                "response_description": "Successful",
                "response_detail": {"token_parameters": token_parameters},
            }
            log.info(f"exiting CREATE USER API with: {data}")
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            log.info(f"exiting CREATE USER API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except TimeoutError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Token Generation Timeout",
                "response_detail": "Error in reaching the token generation endpoint",
            }
            log.info(f"exiting CREATE USER API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Failed to create user: {e}",
                "response_detail": str(e),
            }
            log.info(f"exiting CREATE USER API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class Login(generics.GenericAPIView):

    permission_classes = (AllowAny,)
    serializer_class = LoginSerializer

    def post(self, request):

        log = logging.getLogger("omni_login")
        log.info(
            f'entering LOGIN API with for username: {request.data.get("username")}'
        )

        try:
            """
            Validate request point of entry
            """
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
            log.info(f"FROM LOGIN API:login request from {request_platform}")
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Credentials",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            log.info(f"exiting LOGIN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Application",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            log.info(f"exiting LOGIN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            """
            Validate the request payload
            """
            serializer = LoginSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            username = serializer.validated_data["username"]
            password = serializer.validated_data["password"]
            app_code = serializer.validated_data.get("app_code", None)

            #get the bank with this appcode
            #request_platform.upper().startswith("USSD APPLICATION")
            # find out the reason DFA is not sending app code and change this to if req plat is not bank admin
            if request_platform.upper().startswith("MOBILE APP") or request_platform.upper().startswith("BROWSER APP") or request_platform.upper().startswith("USSD APP"):
                app_bank = Bank.objects.get(alias=app_code)
                app_bank_status = app_bank.is_active
                if not app_bank_status:
                    log.info(f"{app_bank.name}: Suspended. Check with admin")
                    raise ValidationError("Bank is suspended. Please check with the admin.")   
               
            username = username.lower()

            # at this point we do not know what kind of customer is login in
            is_customer = False
            is_agent = False
            is_bank_admin = False

            if app_code:
                # storing app code in internal memory for future use by the token generation view
                set_app_code(username, app_code, request_platform)
            log.info("FROM LOGIN API: request payload successfully validated")
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            log.info(f"exiting LOGIN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            """
            Authenticate the login payload
            """
            is_customer = True
            if request_platform.upper().startswith("USSD APPLICATION"):
                # for USSD Application we use a different scheme for authentication
                user = ussd_authentication(phone_number=username, app_code=app_code)
            else:
                user = authenticate(
                    username=username,
                    password=password,
                    app_code=app_code if app_code else None,
                )

            if not user:
                # valid users which have been set to not active will return None on authenticate
                # extra check to confirm this particular case
                try:
                    if not User.objects.get(
                        username=username, app_code=app_code
                    ).is_active:
                        raise AuthenticationFailed
                except AuthenticationFailed:
                    raise AuthenticationFailed("Account Disabled. Contact Admin")
                except Exception:
                    pass
                raise AuthenticationFailed(
                    "Invalid Credentials. Kindly provide the correct details and try again"
                )

            if request_platform.upper().startswith("BANK ADMIN"):
                log.info(f"FROM LOGIN API:USER LOGIN from Bank Portal")
                if hasattr(user, "bankadmin"):
                    if not user.bankadmin.is_bank_admin:
                        log.info(f"FROM LOGIN API:USER is NOT Bank Admin")
                        raise AuthenticationFailed(
                            f"Account {user.get_username()} is not a Bank Admin"
                        )
                    # Initialize user profile for bank admin user

                    log.info(f"FROM LOGIN API:USER is Bank Admin")

                    is_bank_admin = True
                    user_profile = {
                        "id": user.id,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "email": user.email,
                        "is_active": user.is_active,
                        "last_login": user.last_login,
                        "bank": user.bankadmin.bank.name,
                        "code": user.bankadmin.bank.code,
                        "bank_alias": user.bankadmin.bank.alias,
                        "bank_utility_account": user.bankadmin.bank.utility_account,
                        "is_bank_superadmin": user.bankadmin.is_bank_superadmin,
                        "job_description": user.bankadmin.job_description,
                        "bank_logo_url": user.bankadmin.bank.logo_img.url,
                        "bank_logo_b64_encode": user.bankadmin.bank.get_logo_base64(),
                        # "bank_logo_b64_encode": "Not images yet",
                        "is_first_time_user": True
                        if user.last_login is None
                        else False,
                        "role": user.bankadmin.role,
                    }
                else:
                    raise AuthenticationFailed(
                        f"Account {user.get_username()} is not a Bank Admin"
                    )
            elif request_platform.upper().startswith("DFA"):
                log.info(f"FROM LOGIN API:USER LOGIN from DFA AGENT APP")
                if hasattr(user, "agent"):
                    # Initialize user profile for DFA Agent
                    log.info(f"FROM LOGIN API:USER is a DFA AGENT")

                    is_agent = True
                    user_profile = {
                        "id": user.id,
                        "username": user.get_username(),
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "email": user.email,
                        "phone_number": user.agent.phone_number,
                        "is_active": user.is_active,
                        "last_login": user.last_login,
                        "pin_enabled": True if user.agent.pin else False,
                        "status": user.agent.status,
                        "is_first_time_user": True
                        if user.last_login is None
                        else False,
                    }
                else:
                    raise AuthenticationFailed(
                        f"Account {user.get_username()} is not a Bank Agent"
                    )
            elif hasattr(user, "bankadmin") or hasattr(user, "agent"):
                log.info(
                    f"FROM LOGIN API:USER is login in from a wrong application, USER is either a Bank Admin or Bank Agent but not Customer"
                )
                raise AuthenticationFailed(
                    "Invalid Credentials. Kindly provide the correct details and try again"
                )
            else:
                # At this point it is confirmed that user is not a bank admin user
                # nor a bank agent so we can initialize user profile for a basic user
                is_customer = True

                user_profile = {
                    "id": user.id,
                    "username": user.get_username(),
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "bvn": user.customerprofile.bvn,
                    "phone_number": user.customerprofile.phone_number,
                    "is_active": user.is_active,
                    "last_login": user.last_login,
                    "pin_enabled": True if user.customerprofile.pin else False,
                    "is_email_verified": user.customerprofile.is_email_verified,
                    "onboarding_status": get_onboarding_status(user),
                    "status": user.customerprofile.status,
                    "channel_access": "BLOCK"
                    if not account_has_platform_access(
                        request_platform, user.customerprofile
                    )
                    or user.customerprofile.status == "I"
                    else "ALLOW",
                }

            if hasattr(user, "clientapplication"):
                user.clientapplication.last_login_from = request_platform
                user.clientapplication.save()

            update_last_login(None, user)
        except AuthenticationFailed as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"User Authentication Failed: {e}",
                "response_detail": str(e),
            }
            log.info(f"exiting LOGIN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            """
            get token parameters
            """
            log.info(f"FROM LOGIN API:Getting Token parameters")
            token_endpoint = reverse("token", request=request)
            token_parameters = get_user_token(
                client_id,
                client_secret,
                username,
                password,
                request_platform,
                token_endpoint,
            )
            log.info(f"FROM LOGIN API: Token parameters gotten successfully")
            data = {
                "response_code": "00",
                "response_description": "Successful",
                "response_detail": {
                    "token_parameters": token_parameters,
                    "user_profile": user_profile,
                    "minimum_app_version": get_latest_app_version(),  # it is recognised as minimum app version but is the latest recognised version on the back end
                },
            }
            #log.info(f'Data -> {data}')
        except TimeoutError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Token Generation Timeout",
                "response_detail": "Error in reaching the token generation endpoint",
            }
            log.info(f"exiting LOGIN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            """
            At this stage we now know who is login in so we complete the response payload and
            return to the client
            """

            if is_bank_admin:
                # remove unecessary logs
                _data = deepcopy(data)
                _data.get("response_detail", {}).get("user_profile", {}).pop(
                    "bank_logo_b64_encode", None
                )
                log.info(f"exiting LOGIN API for bank admin with: {_data}")
                return Response(data, status=status.HTTP_200_OK)

            if is_agent:
                # we get extra agent details to be returned in the payload
                url = reverse("get_agent_profile", request=request)
                headers = {
                    "Authorization": f'Bearer {token_parameters.get("access_token")}',
                    "Content-Type": "application/x-www-form-urlencoded",
                }

                log.info(f"FROM LOGIN API:Getting Agent Profile")
                response = requests.request("GET", url, headers=headers)
                response = response.json()

                data["response_detail"]["agent_bank_profile"] = response.get(
                    "response_detail"
                ).get("agent_bank_profile")

                # remove unecessary logs
                log.info(f"exiting LOGIN API for agent admin with: {data}")
                return Response(data, status=status.HTTP_200_OK)

            # this is a customer login
            url = reverse("get_customer_profile", request=request)
            headers = {
                "Authorization": f'Bearer {token_parameters.get("access_token")}',
                "Content-Type": "application/x-www-form-urlencoded",
            }

            #log.info(f"URL: {url}")

            log.info(f"FROM LOGIN API:Getting Customer Profile")
            response = requests.request("GET", url, headers=headers)
            response = response.json()

            #log.info(
                # f"RESPONSE FROM CUSTOMER PROFILE: {response}"
                #)

            data["response_detail"]["customer_bank_profiles"] = response.get(
                "response_detail"
            ).get("customer_bank_profiles")
            if is_customer:
                # send login email notification to customer, it could as well be for bank admin but currently we are not doing that
                log.info(
                    f"FROM LOGIN API:SPINNING UP thread to send login notification email"
                )
                # Thread(
                #     target=self.send_login_notification,
                #     args=(user, app_code, request_platform),
                # ).start()
            # for logged data, remove the bank logo b64 that comes from the customer bank profile
            _data = clean_login_log_data(data)
            #log.info(f"exiting LOGIN API with: {_data}")
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Error Getting Customer Profile: {e}",
                "response_detail": str(e),
            }
            log.info(f"exiting LOGIN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_login_notification(self, customer, app_code, request_platform: str):
        """
        Sends an email on each login to customer account
        """
        try:
            if app_code and app_code != "0000":
                email = customer.email
                bank = Bank.objects.get(alias=app_code)
                if bank.email_img_url:
                    img_url = f"{bank.email_img_url}"
                    foreground = bank.email_foreground_color
                    background = bank.email_background_color
                    email_from = bank.email
                    phone_number = bank.phone_number
                else:
                    img_url = settings.EMAIL_DEFAULT_IMAGE_URL
                    foreground = settings.EMAIL_DEFAULT_FOREGROUND
                    background = settings.EMAIL_DEFAULT_BACKGROUND
                    email_from = settings.DEFAULT_FROM_EMAIL
                    phone_number = "***********"

                if request_platform.lower().startswith("mobile"):
                    channel = "mobile channel"
                elif request_platform.lower().startswith("browser"):
                    channel = "web internet banking channel"
                elif request_platform.lower().startswith("ussd"):
                    channel = "ussd channel"
                else:
                    channel = ""

                now = datetime.now()
                time = now.strftime("%I:%M:%S %p")
                date = now.strftime("%b %d, %Y")
                email_subject = "LOGIN NOTIFICATION"
                email_body = f'Please be informed that your profile was accessed at {time}, (GMT+1)  {date} {f"via the {channel}" if channel else "" }.'

                email_args = {
                    "to": email,
                    "subject": email_subject,
                    "customer_first_name": f"{customer.first_name}".capitalize(),
                    "message": email_body,
                    "img_url": img_url,
                    "foreground": foreground,
                    "background": background,
                    "email_from": email_from,
                    "phone_number": phone_number,
                }

                send_email(email_args, "LOGIN_NOTIFICATION")
            else:
                raise Exception
        except Bank.DoesNotExist:
            # this is extremely unlikely but either ways we are running a thread so for now we just pass the error
            pass
        except Exception:
            # this is running in a thread so we silently pass the error as well
            pass


class ChangePassword(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = ChangePasswordSerializer

    def post(self, request):
        try:
            """
            validate the request payload, then get the user from the authenticated token.
            We then check the password and confirm it is currently valid before setting the
            new password
            """
            serializer = ChangePasswordSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            password = serializer.validated_data["password"]
            new_password = serializer.validated_data["new_password"]

            user = request.auth.user
            is_password_valid = check_password(password, user.password)
            if is_password_valid:
                user.set_password(new_password)
                user.save()
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Password Change Successful",
                    detail="Password has been changed successfully",
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Password",
                    detail="Password does not match set password",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Processing Request",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class CreateBankAdminUser(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope, IsBankSuperAdmin]
    required_scopes = ["bank"]
    serializer_class = BankAdminUserSerializer

    def post(self, request):
        try:
            """
            Create bank admin user with payload request (the serializer has a create function
            which create user if payload is valid)
            """
            serializer = BankAdminUserSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            if not is_bank_admin_username_valid(
                request.auth.user.username, serializer.validated_data["username"]
            ):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Admin Email Mail Server",
                    detail="Unable To Create Bank Admin, Invalid Admin Email Mail Server. Contact Superadmin",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            user = serializer.save()

            bank = request.auth.user.bankadmin.bank

            BankAdmin.objects.create(
                user=user,
                bank=bank,
                is_bank_admin=True,
                is_bank_superadmin=serializer.validated_data["is_bank_superadmin"],
                job_description=serializer.validated_data["job_description"],
            )
            # send mail notification of account creation
            Thread(
                target=self.send_bank_admin_creation_notification,
                args=(bank, serializer.validated_data),
            ).start()
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Bank Admin Created Successfully",
                "response_detail": {"id": user.id},
            }
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Bank",
                "response_detail": f'Bank: {serializer.validated_data["bank"]} is not a partnered bank',
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Failed to Create Bank Admin",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_bank_admin_creation_notification(
        self, partner_bank: Bank, admin_details: dict
    ):
        """
        Notifies partner bank admin via mail the account creation details for login
        """
        log = logging.getLogger("django")
        log.info("Calling Send Bank Admin Creation Email")
        message = f'Hello {admin_details.get("first_name")}\n \n \n \nYOUR BANK ADMIN ACCOUNT WAS CREATED ON OMNI CHANNEL\n \n \nusername: \n \t{admin_details.get("username")}\n \npassword :\n \t{admin_details.get("password")}\n \nYou can now login to https://portal.simpayswitch.com/'

        email_args = {
            "email_from": partner_bank.email,
            "to": admin_details.get("username"),
            "subject": "OMNI CHANNEL BANK ADMIN ACCOUNT CREATION NOTIFICATION",
            "message": message,
        }

        resp = send_email(email_args, "INTERNAL_NOTIFICATION")

        log.info(
            f"response from maling service from bank admin creation notification -> {resp}"
        )


class GetAllBankAdminUsers(generics.ListAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope, IsBankSuperAdmin]
    required_scopes = ["bank"]
    serializer_class = GetBankAdminSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = ("=username", "^first_name", "^last_name", "=email")

    def get_queryset(self):
        user = self.request.auth.user
        user_bank = user.bankadmin.bank
        all_bank_admin = (
            User.objects.filter(bankadmin__is_bank_admin=True)
            .filter(bankadmin__bank=user_bank)
            .order_by("last_name", "first_name")
        )
        search = self.request.query_params.get("search", None)
        superadmin = self.request.query_params.get("superadmin", None)
        if superadmin is not None:
            # the superadmin query param is type string so convert to boolean based on the
            # type of string then filter the query if it is of type boolean
            if superadmin.lower() == "true":
                superadmin = True
            elif superadmin.lower() == "false":
                superadmin = False
            if type(superadmin) == bool:
                all_bank_admin = all_bank_admin.filter(
                    bankadmin__is_bank_superadmin=superadmin
                )
        if search is not None:
            # force call the search field filter
            all_bank_admin = self.filter_queryset(all_bank_admin)
        return all_bank_admin

    def list(self, request):
        try:
            """
            Get all bank admin users by querying the database for users with property -
            is_bank_admin set to True and also belonging to the same bank as the user.
            Then paginate the query to allow only the allowed number to be sent
            """
            queryset = self.get_queryset()
            queryset = self.paginate_queryset(queryset)
            serializer = GetBankAdminSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)

            if queryset:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Bank Admin List",
                    "response_detail": paginated_resp.data,
                }
            else:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Empty Bank Admin List",
                    "response_detail": paginated_resp.data,
                }
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = {
                "reponse_code": settings.FAILED_RESPONSE,
                "response_description": "Error occurred while getting bank admin users",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetToken(generics.ListAPIView):

    permission_classes = (AllowAny,)

    def list(self, request, refresh_token):
        try:
            """
            Validate request point of entry
            """
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Credentials",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Application",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        try:
            """
            Try to generate another token using the refresh token, client id and the client secret
            """
            token_endpoint = reverse("token", request=request)
            token_parameters = get_user_token(
                client_id=client_id,
                client_secret=client_secret,
                token_endpoint=token_endpoint,
                refresh_token=refresh_token,
            )
            data = {
                "response_code": "00",
                "response_description": "Successful",
                "response_detail": {"token_parameters": token_parameters},
            }
            return Response(data, status=status.HTTP_200_OK)
        except TimeoutError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Token Generation Timeout",
                "response_detail": f"Error in reaching the token generation endpoint",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class Logout(generics.ListAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def list(self, request):
        try:
            """
            Validate request point of entry
            """
            access_token = request.auth.token
            client_id, client_secret = get_client_credentials(token=access_token)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Credentials",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Application",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        try:
            """
            revoke the access token used for api access by calling the revoke token endpoint
            """
            token_endpoint = reverse("revoke_token", request=request)
            resp = logout_user(access_token, client_id, client_secret, token_endpoint)
            data = {
                "response_code": "00",
                "response_description": "Successful",
                "response_detail": resp,
            }
            return Response(data, status=status.HTTP_200_OK)
        except TimeoutError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Logout Timeout",
                "response_detail": "Error in reaching the logout endpoint",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class UpdateBankAdminUser(generics.GenericAPIView):

    permission_classes = [
        permissions.TokenHasReadWriteScope,
        IsBankSuperAdmin | IsOwner,
    ]
    required_scopes = ["bank"]
    serializer_class = UpdateBankAdminUserSerializer

    def patch(self, request, pk):
        try:
            """
            Retrieve the user and update the user params with the payload details
            """
            bank_admin = User.objects.get(id=pk)
            serializer = UpdateBankAdminUserSerializer(
                bank_admin, data=request.data, partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()

            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Bank Admin Details Updated Successfully",
                "response_detail": f"Bank Admin with ID {pk} was successfully updated",
            }

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Bank Admin Does Not Exist",
                "response_detail": f"Bank Admin id {pk} is invalid and does not exist",
            }

            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }

            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Failed to Update Bank Admin Details",
                "response_detail": str(e),
            }

            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SendUserSMSOTP(APIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request):
        try:
            """
            From the authenticated request, get the customer and from the customer instance
            get the phonenumber and send otp
            """
            sender = request.auth.user
            app_code = sender.app_code

            if hasattr(sender, "bankadmin"):
                serializer = PhoneNumberSerializer(data=request.data)
                serializer.is_valid(raise_exception=True)

                bank = sender.bankadmin.bank
                bank_app_code = bank.alias

                customer_id = serializer.validated_data["customer_id"]
                phone_number = serializer.validated_data["phone_number"]
                customer_profile = CustomerProfile.objects.get(id=customer_id, customer__app_code=bank_app_code)
                customer = User.objects.get(customerprofile=customer_profile)
                
                if not customer:
                    raise Exception("Customer has no user profile.")
                
                message_sender_name = bank.message_sender_name
                institution_code = bank.code

            else:
            
                if app_code == "0000":
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="Invalid App Code",
                        detail=f"Invalid App Code {app_code}",
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                else:
                    bank = Bank.objects.get(alias=app_code)
                    institution_code = (
                        bank.code
                    )  # using the regular code for now, it could possibly be nip_code in future depending on the type of bank
                    message_sender_name = bank.message_sender_name

                if hasattr(sender, "customerprofile"):
                    customer = sender
                    phone_number = customer.customerprofile.phone_number
                else:
                    data = {
                        "response_code": settings.FAILED_RESPONSE,
                        "response_description": "User Has No Profile",
                        "response_detail": f"User with email: {customer.email} has no valid profile",
                    }
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                
            if phone_number:
                # Generate counter based OTP
                hotp = pyotp.HOTP("base32secret3232")
                validate_otp_at = otp_rand()
                verify_phone_number_otp = hotp.at(validate_otp_at)

                sms_text = f"Welcome to Logic Omni Channel. \nThis is your phone number verfication OTP: \n{verify_phone_number_otp}"

                if settings.APP_ENV == "development":
                    data = {
                        "response_code": settings.SUCCESS_RESPONSE,
                        "response_description": "OTP SMS Sent Successfully",
                        "response_detail": {
                            "user_id": customer.id,
                            "validate_otp_at": validate_otp_at,
                            "otp": verify_phone_number_otp,
                        },
                    }
                    return Response(data, status=status.HTTP_200_OK)
                # send OTP text to phone number
                request_id = get_reference()
                if message_sender_name == "OmoluabiMB":
                    # run our test sms service because this is a test case
                    resp = send_sms_test(phone_number, sms_text)
                else:
                    resp = send_sms(
                        phone_number,
                        sms_text,
                        institution_code=institution_code,
                        message_sender_name=message_sender_name,
                        request_id=request_id,
                    )

                if resp["response_code"] == settings.SUCCESS_RESPONSE:
                    data = {
                        "response_code": settings.SUCCESS_RESPONSE,
                        "response_description": "OTP SMS Sent Successfully",
                        "response_detail": {
                            "user_id": customer.id,
                            "validate_otp_at": validate_otp_at,
                            "otp": verify_phone_number_otp,
                        },
                    }
                    return Response(data, status=status.HTTP_200_OK)
                else:
                    data = {
                        "response_code": settings.FAILED_RESPONSE,
                        "response_description": "Error Sending OTP SMS",
                        "response_detail": resp["response_description"],
                    }
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_description": "User Has No Phone Number",
                    "response_detail": f"User with email: {customer.email} has no phone number recorded",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
        except CustomerProfile.DoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Customer Profile Account Not Found.",
                "response_detail": "Customer Profile Account Not Found.",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Error Verifying User Phone Number",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SendUserEmailVerification(APIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request):
        try:
            """
            From the authenticated request, get the user, then get the user's email and
            send an encrypted token to the mail server gateway
            """
            customer = request.auth.user
            email = customer.email
            img_url = settings.EMAIL_DEFAULT_IMAGE_URL
            foreground = settings.EMAIL_DEFAULT_FOREGROUND
            background = settings.EMAIL_DEFAULT_BACKGROUND
            email_from = settings.DEFAULT_FROM_EMAIL

            if (
                customer.app_code != "0000"
            ):  # check to confirm user belongs to a particular bank else in a general omni channel user
                bank = Bank.objects.get(alias=customer.app_code)
                if bank.email_img_url:
                    img_url = f"{bank.email_img_url}"
                    foreground = bank.email_foreground_color
                    background = bank.email_background_color
                    email_from = bank.email

            # Generate counter based OTP
            hotp = pyotp.HOTP("base32secret3232")
            validate_otp_at = otp_rand()
            verify_email_otp = hotp.at(validate_otp_at)

            email_subject = "Verify Account Email on Omni Channel"
            email_body = f"Thank you for creating an account on our banking platform. Just one more step to verify your account and you begin to enjoy our exciting offerings on all our platfoms. \nPlease complete your verification using this One Time Password (OTP)"

            email_args = {
                "to": email,
                "subject": email_subject,
                "customer_first_name": f"{customer.first_name}".capitalize(),
                "message": email_body,
                "img_url": img_url,
                "foreground": foreground,
                "background": background,
                "email_from": email_from,
                "otp": verify_email_otp,
            }

            resp = send_email(email_args, "VERIFY_EMAIL")

            if resp["response_code"] == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="RESET PASSWORD OTP SENT TO EMAIL SUCCESSFULLY",
                    detail={"validate_otp_at": validate_otp_at, "id": customer.id},
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                return Response(resp, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Error Sending Verification Email",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class VerifyUserPhoneNumber(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = VerifyUserPhoneNumberSerializer

    def post(self, request):
        try:
            """
            Retrieve the OTP parameters from the payload and verify the phone number
            """
            serializer = VerifyUserPhoneNumberSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            validate_otp_at = serializer.validated_data["validate_otp_at"]
            sms_otp = serializer.validated_data["sms_otp"]

            hotp = pyotp.HOTP("base32secret3232")
            if not hotp.verify(sms_otp, int(validate_otp_at)):
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_detail": "Invalid OTP",
                    "response_description": "Invalid OTP, Confirm the OTP",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            user = request.auth.user
            user.customerprofile.is_phone_number_verified = True
            user.customerprofile.save()

            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "User Phone Number Verified",
                "response_detail": f"User with email: {user.email} has phone number successfully verified",
            }
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class VerifyUserEmail(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = VerifyUserEmailSerializer

    def post(self, request):
        try:
            """
            Validate the request payload and then authenticate the email otp
            if email otp is valid, then verify the user email
            """
            serializer = VerifyUserEmailSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            id = serializer.validated_data["id"]
            validate_otp_at = serializer.validated_data["validate_otp_at"]
            email_otp = serializer.validated_data["email_otp"]

            hotp = pyotp.HOTP("base32secret3232")
            if not hotp.verify(email_otp, int(validate_otp_at)):
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_detail": "Invalid OTP",
                    "response_description": "Invalid OTP, Confirm the OTP",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            user = User.objects.get(id=id)
            user.customerprofile.is_email_verified = True
            user.customerprofile.save()

            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "User Email Verified",
                "response_detail": f"User with ID: {id} has email successfully verified",
            }
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid User Id",
                "response_detail": "User does not exist",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class RequestPasswordReset(generics.GenericAPIView):

    permission_classes = (AllowAny,)
    serializer_class = RequestPasswordResetSerializer

    def post(self, request):
        try:
            """
            Validate request point of entry
            """
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Credentials",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Application",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            """
            validate request payload
            """
            serializer = RequestPasswordResetSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            email = serializer.validated_data["email"]
            app_code = serializer.validated_data["app_code"]

            # confirm customer with email exists in user table
            customer = User.objects.get(
                username=email, app_code=app_code if app_code else "0000"
            )

            img_url = settings.EMAIL_DEFAULT_IMAGE_URL
            foreground = settings.EMAIL_DEFAULT_FOREGROUND
            background = settings.EMAIL_DEFAULT_BACKGROUND
            email_from = settings.DEFAULT_FROM_EMAIL

            if (
                customer.app_code != "0000"
            ):  # check to confirm user belongs to a particular bank else in a general omni channel user
                bank = Bank.objects.get(alias=customer.app_code)
                if bank.email_img_url:
                    img_url = f"{bank.email_img_url}"
                    foreground = bank.email_foreground_color
                    background = bank.email_background_color
                    email_from = bank.email

            # Generate counter based OTP
            hotp = pyotp.HOTP("base32secret3232")
            validate_otp_at = otp_rand()
            password_reset_otp = hotp.at(validate_otp_at)

            email_subject = "RESET PASSWORD OTP"
            email_body = (
                f"Kindly use this One Time Password (OTP) to set a new password"
            )

            email_args = {
                "to": email,
                "subject": email_subject,
                "customer_first_name": f"{customer.first_name}".capitalize(),
                "message": email_body,
                "img_url": img_url,
                "foreground": foreground,
                "background": background,
                "email_from": email_from,
                "otp": password_reset_otp,
            }
            resp = send_email(email_args, "RESET_PASSWORD")

            if resp["response_code"] == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="RESET PASSWORD OTP SENT TO EMAIL SUCCESSFULLY",
                    detail={"validate_otp_at": validate_otp_at, "id": customer.id},
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                return Response(resp, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except User.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Email",
                detail="User with email does not exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Error Sending Verification Email",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SendResetPasswordOTP(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = VerifyUserEmailSerializer

    def post(self, request):
        try:
            """
            Validate the request payload and then authenticate the email otp
            if email otp is valid, then verify the user email
            """
            serializer = VerifyUserEmailSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            id = serializer.validated_data["id"]
            validate_otp_at = serializer.validated_data["validate_otp_at"]
            otp = serializer.validated_data["email_otp"]

            customer = User.objects.get(id=id)
            if not hasattr(customer, "customerprofile"):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Customer Has No Profile",
                    detail="Customer does not have valid profile",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            phone_number = customer.customerprofile.phone_number
            message = f"Logic Omni Channel, Reset Password OTP: {otp}"
            if phone_number:
                # send OTP text to phone number
                resp = send_sms(phone_number, message)

                if resp["response_code"] == settings.SUCCESS_RESPONSE:
                    data = {
                        "response_code": settings.SUCCESS_RESPONSE,
                        "response_description": "OTP SMS Sent Successfully",
                        "response_detail": {"validate_otp_at": validate_otp_at},
                    }
                    return Response(data, status=status.HTTP_200_OK)
                else:
                    data = {
                        "response_code": settings.FAILED_RESPONSE,
                        "response_description": "Error Sending OTP SMS",
                        "response_detail": resp["response_description"],
                    }
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_description": "User Has No Phone Number",
                    "response_detail": f"User with email: {customer.email} has no phone number recorded",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except User.DoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid User Id",
                "response_detail": "User does not exist",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class UpdateUserPhoneNumber(generics.GenericAPIView):

    serializer_class = UpdateUserPhoneNumberSerializer
    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    def patch(self, request):
        try:
            """ 
            Validates OTP sent and updates phone number on local
            BVN model and Customer Profile.
            """
            sender = request.auth.user
            bank = sender.bankadmin.bank
            bank_app_code = bank.alias

            serializer = UpdateUserPhoneNumberSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            customer_id = serializer.validated_data["customer_id"]
            phone_number = serializer.validated_data["phone_number"]
            otp = serializer.validated_data["otp"]
            validate_otp_at = serializer.validated_data["validate_otp_at"]

            customer_profile = CustomerProfile.objects.get(id=customer_id, customer__app_code=bank_app_code)

            hotp = pyotp.HOTP("base32secret3232")
            if not hotp.verify(otp, int(validate_otp_at)):
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_detail": "Invalid OTP",
                    "response_description": "Invalid OTP, Confirm the OTP",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            bvn = BVN.objects.filter(phone_number=phone_number)
            if bvn:
                bvn = bvn[0]
                bvn.phone_number = phone_number
                bvn.save()
            customer_profile.phone_number = phone_number
            customer_profile.save()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Phone Number Updated Successfully.",
                detail="Phone Number Updated Successfully.",
            )
            return Response(data, status=status.HTTP_200_OK)
        
        except CustomerProfile.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Customer Profile Not Found.",
                detail="Customer Profile Not Found.",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="FAILED TO UPDATED PHONE NUMBER.",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ResetPassword(generics.GenericAPIView):

    permission_classes = (AllowAny,)
    serializer_class = PasswordResetSerializer

    def post(self, request):
        try:
            """
            Validate request point of entry
            """
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Credentials",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Application",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            """
            validate the request payload, verify the otp then set password
            """
            serializer = PasswordResetSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            id = serializer.validated_data["id"]
            validate_otp_at = serializer.validated_data["validate_otp_at"]
            otp = serializer.validated_data["otp"]
            password = serializer.validated_data["password"]

            hotp = pyotp.HOTP("base32secret3232")
            if not hotp.verify(otp, int(validate_otp_at)):
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_detail": "Invalid OTP",
                    "response_description": "Invalid OTP, Confirm the OTP",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            user = User.objects.get(id=id)
            user.set_password(password)
            user.save()
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Password Change Successful",
                detail="Password change request successful",
            )
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except User.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid ID",
                detail="Invalid ID",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Error Processing Request",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ActivateDeactivateBankAdmin(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope, IsBankSuperAdmin]
    required_scopes = ["bank"]
    serializer_class = ActivateDeactivateBankAdminSerializer

    def post(self, request):
        try:
            """
            From the payload get the bank id, use the the id to get the user instance
            then activate or deactivate the bank admin based on the is_active payload
            params
            """
            serializer = ActivateDeactivateBankAdminSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            id = serializer.validated_data["bank_admin_id"]
            is_active = serializer.validated_data["is_active"]

            bank_admin = User.objects.get(id=id)

            if (
                hasattr(bank_admin, "bankadmin")
                and bank_admin.bankadmin.is_bank_admin == True
            ):
                bank_admin.is_active = is_active
                bank_admin.save()
            else:
                raise ObjectDoesNotExist

            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Activation Status Set Successfully",
                "response_detail": f"Bank Admin is_active Status Successfully set to {is_active}",
            }

            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }

            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Bank Admin Does Not Exist",
                "response_detail": f"Bank Admin with ID {id} does not exist",
            }

            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Error While Setting Bank Admin Active Status",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


def validate_request_point_of_entry(request):
    try:
        """
        Validate request point of entry
        """
        client_id, client_secret = get_client_credentials(request)
        request_platform = get_platform(
            client_id=client_id, client_secret=client_secret
        )

        return request_platform
    except ValidationError:
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": "Invalid Client Credentials",
            "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
        }
        return data
    except ObjectDoesNotExist:
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": "Invalid Client Application",
            "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
        }
        return data


class GetVerificationStatus(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request):
        try:
            """
            Returns the KYC status necessary for international transactions
            and for new account openings
            """
            customer = request.auth.user
            verification_status = customer.customerprofile.kyc_verification_status
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Verification Status Request Successful",
                detail={"status": verification_status},
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Get Verification Status -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetKYCRequirementList(generics.GenericAPIView):

    permission_classes = (AllowAny,)

    def get(self, request):
        try:
            """
            verifies if a customer has a kyc doc table, if not it creates a
            kyc table and gets the requirement list from the kyc table
            """
            customer = request.auth.user
            customer_profile = customer.customerprofile

            if not hasattr(customer_profile, "kycdocs"):
                # customer does not have a kyc docs table so we create that for the customer
                kycdocs = KYCDocs.objects.create(profile=customer_profile)
            else:
                kycdocs = customer_profile.kycdocs

            kyc_requirement_list = kycdocs.get_kyc_requirements_status()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get KYC Requirments List Successful",
                detail={"kyc_requirement_list": kyc_requirement_list},
            )

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            kyc_requirement_list = [
                {
                    "kyc_requirement": "Means of Identification",
                    "is_updated": False,
                    "kyc_requirement_choice": [
                        {
                            "name": "International Passport",
                            "code": "international_passport",
                        },
                        {"name": "Drivers License", "code": "drivers_license"},
                        {"name": "National ID", "code": "national_id"},
                        {"name": "Voters Card", "code": "voters_card"},
                    ],
                    "code": "means_of_identification",
                },
                {
                    "kyc_requirement": "Signature",
                    "is_updated": False,
                    "kyc_requirment_choice": [],
                    "code": "signature",
                },
                {
                    "kyc_requirement": "Profile Picture",
                    "is_updated": False,
                    "kyc_requirment_choice": [],
                    "code": "profile_picture",
                },
            ]
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get KYC Requirments List Successful",
                detail={"kyc_requirement_list": kyc_requirement_list},
            )
            return Response(data, status=status.HTTP_200_OK)


class UpdateKYCDocs(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = KYCDocSerializer

    def patch(self, request):
        try:
            """
            Retrieves customer kyc docs table and updates it with the required documents
            """
            customer = request.auth.user
            customer_profile = customer.customerprofile
            kycdocs = customer_profile.kycdocs

            serializer = KYCDocSerializer(kycdocs, request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="KYC Document uploaded Successfully",
                detail="KYC Document uploaded Successfully",
            )

            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Request Payload",
                detail=serializer.errors,
            )

            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Update KYC Documents {e}",
                detail=str(e),
            )

            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class OpenAccount(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = OpenAccountSerializer

    def post(self, request):

        log = logging.getLogger("django")

        try:
            """
            We then retrieve the bank's own agent account and a create an account from it.
            Account created will be mapped to the custom bank agent.
            """

            serializer = OpenAccountSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # log.info("here one")

            user = request.auth.user
            customer = user.customerprofile
            app_code = user.app_code

            bank = Bank.objects.get(alias=app_code)
            name_list = bank.name.split(" ")

            bank_app_agent, created = get_user_model().objects.get_or_create(
                username=f"{name_list[0]}APP".lower(),
                first_name=name_list[0],
                last_name="APP AGENT",
                email=bank.email,
            )

            # log.info("here two")
            if created:
                # bank app agent does not exist and has just been created, so we create an agent instance
                # for the newly created bank app agent
                bank_app_agent = Agent.objects.create(user=bank_app_agent, bank=bank)
            else:
                bank_app_agent = bank_app_agent.agent

            if bank_app_agent.status == "I":
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="You are unable to Open Account Via this channel. Please Contact Admin",
                    detail="You are unable to Open Account Via this channel. Please Contact Admin",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            # log.info("here three")

            serializer.is_valid(raise_exception=True)

            log.info(f'request data : {serializer.errors}')

            account = serializer.save(
                bvn=customer.bvn,
                first_name=user.first_name,
                last_name=user.last_name,
                email=user.email,
                phone_number=customer.phone_number,
                gender=customer.gender.upper(),
                dob=customer.dob,
                agent=bank_app_agent,
                kyc_verification_status="pending",
                id_base_64="",
                signature_base_64="",
                profile_picture_base_64="",
            )
            #log.info("here four")
            Thread(
                target=self.send_customer_initiate_account_creation_notification,
                args=(account,),
            ).start()

            if account.account_type.code == "savings":
                # approve this type of account creation instead of waiting for approval
                resp = account_opening(account)
                if resp.get("response_code") == settings.SUCCESS_RESPONSE and resp.get(
                    "response_detail", {}
                ).get("account_number"):
                    account.account_number = resp.get("response_detail", {}).get(
                        "account_number"
                    )
                    account.customer_number = resp.get("response_detail", {}).get(
                        "customer_number"
                    )
                    account.is_approved = True
                    account.save()
                    bank_app_agent.accounts_opened += 1
                    bank_app_agent.save()

                    Thread(
                        target=self.send_customer_account_creation_notification,
                        args=(account,),
                    ).start()

                    CustomerAccount.objects.create(
                        profile=customer,
                        account_number=account.account_number,
                        bank=bank,
                        account_type=account.account_type,
                        daily_limit=get_daily_limit(account.account_type, bank),
                    )
                    customer.no_of_customer_account = (
                        customer.no_of_customer_account + 1
                    )
                    customer.save()

                    data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description="Account Successfully Created",
                        detail="Account Successfully Created",
                    )
                    return Response(data, status=status.HTTP_200_OK)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Creation Process Initiated, Kindly Await Approval From The Bank",
                detail="Account Creation Process Initiated, Kindly Await Approval From The Bank",
            )
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            log.info(f'validation error : {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Bank.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to retrieve Customer Bank",
                detail="Unable to retrieve Customer Bank",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Opening Account: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_customer_initiate_account_creation_notification(
        self, account: AgentOnboardedAccount
    ):
        """
        We send the customer a notification email that his account creation process has started
        """
        message = f"A {account.account_type.name.upper()} account creation process has been initated, as soon as your application is approved you'd recieve a mail containing your Account details."

        email_args = {
            "email_from": account.agent.bank.email,
            "to": account.email,
            "subject": f"{account.account_type.name.upper()} ACCOUNT OPENING APPLICATION RECIEVED.",
            "message": message,
            "customer_first_name": account.first_name,
            "img_url": f"{account.agent.bank.email_img_url}",
            "foreground": account.agent.bank.email_foreground_color,
            "background": account.agent.bank.email_background_color,
        }

        send_email(email_args, "ACCOUNT_OPENING_NOTIFICATION")

    def send_customer_account_creation_notification(
        self, account: AgentOnboardedAccount
    ):
        """
        We expect that the originating parent bank is going to send sms text to the customer's
        mobile, so for account creation notification we send the customer a custom email
        """
        message = f"A {account.account_type.name.upper()} has been opened for you. Your account detail are as shown below."

        email_args = {
            "email_from": account.agent.bank.email,
            "to": account.email,
            "subject": f"WELCOME!!! YOU JUST OPENED AN ACCOUNT WITH {account.agent.bank.name.upper()}.",
            "account_number": account.account_number,
            "message": message,
            "customer_first_name": account.first_name,
            "img_url": f"{account.agent.bank.email_img_url}",
            "foreground": account.agent.bank.email_foreground_color,
            "background": account.agent.bank.email_background_color,
            "account_name": f"ACCOUNT NAME: {account.last_name} {account.first_name}",
            "account_number": f"ACCOUNT NUMBER: {account.account_number}",
        }

        send_email(email_args, "ACCOUNT_OPENING")


class OpenAccountV2(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = OpenAccountSerializerV2

    def post(self, request):

        log = logging.getLogger("django")

        try:
            """
            We then retrieve the bank's own agent account and a create an account from it.
            Account created will be mapped to the custom bank agent.
            """

            serializer = OpenAccountSerializerV2(data=request.data)
            serializer.is_valid(raise_exception=True)

            # log.info("here one")

            user = request.auth.user
            customer = user.customerprofile
            app_code = user.app_code

            bank = Bank.objects.get(alias=app_code)
            name_list = bank.name.split(" ")

            bank_app_agent, created = get_user_model().objects.get_or_create(
                username=f"{name_list[0]}APP".lower(),
                first_name=name_list[0],
                last_name="APP AGENT",
                email=bank.email,
            )

            # log.info("here two")
            if created:
                # bank app agent does not exist and has just been created, so we create an agent instance
                # for the newly created bank app agent
                bank_app_agent = Agent.objects.create(user=bank_app_agent, bank=bank)
            else:
                bank_app_agent = bank_app_agent.agent

            if bank_app_agent.status == "I":
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="You are unable to Open Account Via this channel. Please Contact Admin",
                    detail="You are unable to Open Account Via this channel. Please Contact Admin",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            # log.info("here three")

            serializer.is_valid(raise_exception=True)

            log.info(f'request data : {serializer.errors}')

            validated_data = serializer.validated_data
            city = validated_data.get("city")
            state = validated_data.get("state")
            address = validated_data.get("address")
            account_type = validated_data.get("account_type")
            
            acc_type = BankAccountType.objects.get(code=account_type)
            
            account = AgentOnboardedAccount.objects.create(
                account_type=acc_type,
                bvn=customer.bvn,
                first_name=user.first_name,
                last_name=user.last_name,
                email=user.email,
                phone_number=customer.phone_number,
                gender=customer.gender.upper(),
                dob=customer.dob,
                agent=bank_app_agent,
                city=city,
                state=state,
                address=address,
                kyc_verification_status="pending",
                id_base_64="",
                signature_base_64="",
                profile_picture_base_64="",
            )
            #log.info("here four")
            Thread(
                target=self.send_customer_initiate_account_creation_notification,
                args=(account,),
            ).start()

            if account.account_type.code == "savings":

                other_details = {
                    "tin": "234433",
                    "nin": "***********",
                    "marital": validated_data.get("marital_status"),
                    "sector": validated_data.get("sector"),
                    "next_kin_address": validated_data.get("next_kin_address", ""),
                    "next_kin_name": validated_data.get("next_kin_name", ""),
                    "next_kin_relationship": validated_data.get("next_kin_relationship", ""),
                    "next_kin_phone": validated_data.get("next_kin_phone", ""),
                    "next_kin_email": validated_data.get("next_kin_email", "")
                }

                # approve this type of account creation instead of waiting for approval
                resp = account_opening(account, other_details)
                if resp.get("response_code") == settings.SUCCESS_RESPONSE and resp.get(
                    "response_detail", {}
                ).get("account_number"):
                    account.account_number = resp.get("response_detail", {}).get(
                        "account_number"
                    )
                    account.customer_number = resp.get("response_detail", {}).get(
                        "customer_number"
                    )
                    account.is_approved = True
                    account.save()
                    bank_app_agent.accounts_opened += 1
                    bank_app_agent.save()

                    Thread(
                        target=self.send_customer_account_creation_notification,
                        args=(account,),
                    ).start()

                    CustomerAccount.objects.create(
                        profile=customer,
                        account_number=account.account_number,
                        bank=bank,
                        account_type=account.account_type,
                        daily_limit=get_daily_limit(account.account_type, bank),
                    )
                    customer.no_of_customer_account = (
                        customer.no_of_customer_account + 1
                    )
                    customer.save()

                    data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description="Account Successfully Created",
                        detail="Account Successfully Created",
                    )
                    return Response(data, status=status.HTTP_200_OK)
                
                elif resp.get("response_code") == settings.FAILED_RESPONSE:
                    data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description="Failed to created account with CBA",
                        detail=resp.get("response_description"),
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Creation Process Initiated, Kindly Await Approval From The Bank",
                detail="Account Creation Process Initiated, Kindly Await Approval From The Bank",
            )
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            log.info(f'validation error : {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Bank.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to retrieve Customer Bank",
                detail="Unable to retrieve Customer Bank",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Opening Account: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_customer_initiate_account_creation_notification(
        self, account: AgentOnboardedAccount
    ):
        """
        We send the customer a notification email that his account creation process has started
        """
        message = f"A {account.account_type.name.upper()} account creation process has been initated, as soon as your application is approved you'd recieve a mail containing your Account details."

        email_args = {
            "email_from": account.agent.bank.email,
            "to": account.email,
            "subject": f"{account.account_type.name.upper()} ACCOUNT OPENING APPLICATION RECIEVED.",
            "message": message,
            "customer_first_name": account.first_name,
            "img_url": f"{account.agent.bank.email_img_url}",
            "foreground": account.agent.bank.email_foreground_color,
            "background": account.agent.bank.email_background_color,
        }

        send_email(email_args, "ACCOUNT_OPENING_NOTIFICATION")

    def send_customer_account_creation_notification(
        self, account: AgentOnboardedAccount
    ):
        """
        We expect that the originating parent bank is going to send sms text to the customer's
        mobile, so for account creation notification we send the customer a custom email
        """
        message = f"A {account.account_type.name.upper()} has been opened for you. Your account detail are as shown below."

        email_args = {
            "email_from": account.agent.bank.email,
            "to": account.email,
            "subject": f"WELCOME!!! YOU JUST OPENED AN ACCOUNT WITH {account.agent.bank.name.upper()}.",
            "account_number": account.account_number,
            "message": message,
            "customer_first_name": account.first_name,
            "img_url": f"{account.agent.bank.email_img_url}",
            "foreground": account.agent.bank.email_foreground_color,
            "background": account.agent.bank.email_background_color,
            "account_name": f"ACCOUNT NAME: {account.last_name} {account.first_name}",
            "account_number": f"ACCOUNT NUMBER: {account.account_number}",
        }

        send_email(email_args, "ACCOUNT_OPENING")


class DeleteAccount(viewsets.GenericViewSet):
    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer = BvnSerializer    
    def destroy(self, request, bvn, app_code):
        
        try:
            user = request.auth.user
            bank = user.bankadmin.bank
            alias = bank.alias

            if alias == app_code:
                userbvn = BVN.objects.get(bvn=bvn)
                users = User.objects.get(first_name=userbvn.first_name, last_name=userbvn.last_name, app_code=app_code)
                
                if users is not None:  
                    users.delete()
                    data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description= "User Deleted Successfully",
                        detail="User Deleted Successfully",
                        )
                    return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Permission Denied.",
                detail="Permission Denied. Cannot Access Data Outside Your Bank APP CODE.",
            )
            return Response(data, status=status.HTTP_403_FORBIDDEN)
        except BVN.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="BVN Does Not Exist",
                detail="BVN Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except User.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="User Does Not Exist",
                detail="User Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Deleting Account: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)