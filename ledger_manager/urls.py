from django.urls import path, include
from rest_framework.routers import SimpleRouter

from .views import (
    AccountFundTransferView, AccountHistoryStreamedView, AccountStatus, AccountHistoryView,
    AccountViewset, CreateAccountView, CustomerStatus, CustomerViewset, 
    PostingEngine, ProductCategoryViewset, ProductTypeViewset, TransactionStatusViewset,
    GetAccountBalance,
    posting_engine, success, fund_transfer
)



router = SimpleRouter()
router.register('customers', CustomerViewset, basename='customers')
router.register('product_category', ProductCategoryViewset, basename='product_category')
router.register('product_type', ProductTypeViewset, basename='product_type')
router.register('accounts', AccountViewset, basename='accounts')
router.register('transaction_status', TransactionStatusViewset, basename='transaction_status')

urlpatterns = [
    
    path('create_account/', CreateAccountView.as_view(), name='create_account'),
    path('posting_engine/', PostingEngine.as_view(), name='posting_engine'),
    path('account_statement_streamed/', AccountHistoryStreamedView.as_view(), name='account_statement_streamed'),
    path("account_statement/", AccountHistoryView.as_view(), name="account_statememt"),
    path('account_status/', AccountStatus.as_view(), name='account_status'),
    path('customer_status/', CustomerStatus.as_view(), name='customer_status'),
    path('account_fund_transfer/', AccountFundTransferView.as_view(), name='account_fund_transfer'),
    path("wallet_account_balance/", GetAccountBalance.as_view(), name="wallet_account_balance"),
    
    #--- Frontend views calling the Posting Engine and Funds Transfer API views ---#
    path("posting_engine_admin/", posting_engine, name="posting_engine_admin"),
    path("account_fund_transfer_admin/", fund_transfer, name="account_fund_transfer_admin"),
    path('success_page/<path:data>/', success, name="success"), # to accept arbitary kwargs

] + router.urls
