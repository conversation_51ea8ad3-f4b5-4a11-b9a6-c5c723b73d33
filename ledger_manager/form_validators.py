from django import forms

def validate_amount(amount):
    str_obj = str(amount)
    splitter = str_obj.split(".")
    if len(splitter[1]) > 2:
        raise forms.ValidationError(f"{len(splitter[1])} decimal places entered, reduce to 2 or less")
    elif len("".join(splitter)) > 12:
        raise forms.ValidationError(f"You entered a very large figure `{amount}` should be 12 or less digits ")
    elif amount <= 0:
        raise forms.ValidationError(f"You selected an Ivalid amount `{amount}`, amount should be above 0 ")
    return amount

def validate_account(account):
    if len(account) < 15 or len(account) > 15:
        raise forms.ValidationError(f"Invalid account number {len(account)}, must be 15 digits")
    return account
