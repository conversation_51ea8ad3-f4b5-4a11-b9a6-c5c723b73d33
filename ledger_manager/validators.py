from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

def validate_amount(amount):
    if amount < 0:
        raise ValidationError(_(f'Invalid amount. Negative amount {amount} not accepted'))
    return amount

def validate_phone_number(phone_number: str):

    try:
        int(phone_number)
    except Exception:
        raise ValidationError(_(f'Invalid phone number {phone_number}'))

    if not phone_number.startswith('+'):
        raise ValidationError(_(f'Invalid phone number {phone_number}. Confirm phone number format of +<CountryCode><PhoneNumber>'))

    if len(phone_number) > 16:
        raise ValidationError(_(f'Invalid phone number {phone_number}. Length greater than approved length'))

    # African phone number validation
    if phone_number.startswith('+2'):
        country_code = ['213', '244', '229', '267', '226', '257', '237', '238', '236', '269', '243', '253', '240', '291', '251', '241', '220', '233', '224', '245', '225', '254', '266', '231', '218', '261', '265', '223', '222', '230', '212', '258', '264', '227', '234', '242', '262', '250', '290', '239', '221', '248', '232', '252', '211', '249', '268', '255', '228', '216', '256', '212', '260', '263']
        if phone_number[1:4] not in country_code:
            if phone_number[1:3] not in ['20', '27']:
                raise ValidationError(_(f'Invalid phone number --> Invalid country code'))
        return phone_number
    
    # COMPLETE FOR OTHER CONTINENTS
    raise ValidationError(f'Invalid phone number {phone_number}')
        
def validate_posting_type(posting_type: str):
    if posting_type.lower() not in ['credit', 'cr', 'cre', 'debit', 'db', 'dbt']:
        raise ValidationError(_(f'Invalid posting type -> ({posting_type}). (NOTE: posting type either "CREDIT" or "DEBIT")'))
    return posting_type


'''
def amount_validate(amount):
        if amount < 0:
            raise ValidationError(_"Sorry, the email submitted is invalid. All emails have to be registered on this domain only.", status='invalid')
            # raise ValidationError(_'Invalid amount. Negative amount {amount} not accepted', status='invalid')


'''
