from datetime import datetime
import string
import random

from django.core.exceptions import ObjectDoesNotExist

from . import models
# from logic_omnichannel import settings
from django.conf import settings

def generate_user_reference(email, pk):
    """ 
    Formats the First 3 letters of the account email and the pk, with a random alphanumeric string
    """
    alphanumeric = string.ascii_letters + string.digits
    random_string = "".join(random.choice(alphanumeric) for _ in range(15))
    
    splitter = [*email.lower()] # unpacks the email into list element
    characters = splitter[:3]
    short_name = "".join(characters)
    ref = f"{short_name}{pk}_{random_string}"
    
    return ref

def call_user_ref_func(email, pk):
    """ 
    Checks if the generated alphanumeric string(from the `generate_user_reference`) 
    already exists, if it does, it calls the function again to create a new one
    """
    caller = generate_user_reference(email=email, pk=pk)
    
    unique_user_ref = models.AccountHistory.objects.filter(user_ref=caller)
    if unique_user_ref.exists():
       return generate_user_reference(email=email, pk=pk)
    return caller


def generate_account_number():
    '''
        Algorithm for generating an account number is based on getting the 
        time and date from the datetime function, then filtering the numbers. 
        This would provide a 20 digit number. Finally, a extract the first 15
        digits. The account number is unique to the millisecond
    '''
    account_number = str(datetime.now())
    bad_char = ('-', ':', ' ', '.')
    account_number = ''.join(filter(lambda x: x not in bad_char, account_number))
    account_number = account_number[0:15]
    return account_number

def credit_account(account, amount):
    
    account.ledger_balance += amount
    '''
        Call the credit account external switch
    '''
    account.actual_balance += amount
    

def debit_account(account, amount):
    account_number = models.Account.objects.get(account_number=account)
    account_type = account_number.account_product_type
    
    if account_type.allow_overdraft == 'A':
        account.ledger_balance -= amount
        '''
            Call the debit account external switch
        '''
        account.actual_balance -= amount
    elif account_type.minimum_balance:
        if (account.ledger_balance - amount) < account_type.minimum_balance:
            raise Exception(f'Kindly adjust the debit amount. Account has actual balance of {account.actual_balance} and is set with a minimum balance of {account_type.minimum_balance}')
        else:
            account.ledger_balance -= amount
            '''
                Call the debit account external switch
            '''
            account.actual_balance -= amount
    else:
        if (account.ledger_balance - amount) < 0:
            raise Exception(f'Kindly adjust the debit amount. Account has actual balance of {account.actual_balance}.')
        else:
            account.ledger_balance -= amount
            '''
                Call the debit account external switch
            '''
            account.actual_balance -= amount
  

def validate_customer(phone_number, first_name, last_name):
    '''
        Confirm the customer exists
    '''
    try:
        phone_number=phone_number if phone_number[0] == '+' else f'+{phone_number}'
        customer = models.Customer.objects.get(phone_number=phone_number)
        if f'{first_name} {last_name}'.lower() != str(customer).lower():
            raise Exception(f'Customer with phone number {phone_number} exist but name {first_name} {last_name} is invalid. Kindly confirm.')
        return customer
    except ObjectDoesNotExist:
        data = {
            'response_code': settings.FAILED_RESPONSE, 
            'response_description': f'Customer {last_name} {first_name} with phone number {phone_number} does not exist. Confirm phone number format of +<CountryCode><PhoneNumber>'
        }
        return data
    except Exception as e:
        data = {
            'response_code': settings.FAILED_RESPONSE, 
            'response_description': f'{e}'
        }
        return data

def confirm_account_customer_status(account):
    '''
        Confirm Account status and Customer status is active
    '''
    if account.status == 'I':
        raise Exception(f"Customer Account ({account}) currently INACTIVE. Contact Admin for help")
    elif account.account_holders_name.status == 'I':
        raise Exception(f"Customer ({account.account_holders_name}) currently INACTIVE. Contact Admin for help")
    
def currency_formatter(number):
    curr = float(number)
    currency = f"{curr:,}"
    return currency

from datetime import datetime
import string
import random

from django.core.exceptions import ObjectDoesNotExist

from . import models
# from logic_omnichannel import settings
from django.conf import settings

from rest_framework.pagination import PageNumberPagination


class AccountHistoryPagination(PageNumberPagination):
    page_size = 20 
    max_page_size = 100

def generate_user_reference(email, pk):
    """ 
    Formats the First 3 letters of the account email and the pk, with a random alphanumeric string
    """
    alphanumeric = string.ascii_letters + string.digits
    random_string = "".join(random.choice(alphanumeric) for _ in range(15))
    
    splitter = [*email.lower()] # unpacks the email into list element
    characters = splitter[:3]
    short_name = "".join(characters)
    ref = f"{short_name}{pk}_{random_string}"
    
    return ref

def call_user_ref_func(email, pk):
    """ 
    Checks if the generated alphanumeric string(from the `generate_user_reference`) 
    already exists, if it does, it calls the function again to create a new one
    """
    caller = generate_user_reference(email=email, pk=pk)
    
    unique_user_ref = models.AccountHistory.objects.filter(user_ref=caller)
    if unique_user_ref.exists():
       return generate_user_reference(email=email, pk=pk)
    return caller


def generate_account_number():
    '''
        Algorithm for generating an account number is based on getting the 
        time and date from the datetime function, then filtering the numbers. 
        This would provide a 20 digit number. Finally, a extract the first 15
        digits. The account number is unique to the millisecond
    '''
    account_number = str(datetime.now())
    bad_char = ('-', ':', ' ', '.')
    account_number = ''.join(filter(lambda x: x not in bad_char, account_number))
    account_number = account_number[0:15]
    return account_number

def credit_account(account, amount):
    
    account.ledger_balance += amount
    '''
        Call the credit account external switch
    '''
    account.actual_balance += amount
    

def debit_account(account, amount):
    account_number = models.Account.objects.get(account_number=account)
    account_type = account_number.account_product_type
    
    if account_type.allow_overdraft == 'A':
        account.ledger_balance -= amount
        '''
            Call the debit account external switch
        '''
        account.actual_balance -= amount
    elif account_type.minimum_balance:
        if (account.ledger_balance - amount) < account_type.minimum_balance:
            raise Exception(f'Kindly adjust the debit amount. Account has actual balance of {account.actual_balance} and is set with a minimum balance of {account_type.minimum_balance}')
        else:
            account.ledger_balance -= amount
            '''
                Call the debit account external switch
            '''
            account.actual_balance -= amount
    else:
        if (account.ledger_balance - amount) < 0:
            raise Exception(f'Kindly adjust the debit amount. Account has actual balance of {account.actual_balance}.')
        else:
            account.ledger_balance -= amount
            '''
                Call the debit account external switch
            '''
            account.actual_balance -= amount
  

def validate_customer(phone_number, first_name, last_name):
    '''
        Confirm the customer exists
    '''
    try:
        phone_number=phone_number if phone_number[0] == '+' else f'+{phone_number}'
        customer = models.Customer.objects.get(phone_number=phone_number)
        if f'{first_name} {last_name}'.lower() != str(customer).lower():
            raise Exception(f'Customer with phone number {phone_number} exist but name {first_name} {last_name} is invalid. Kindly confirm.')
        return customer
    except ObjectDoesNotExist:
        data = {
            'response_code': settings.FAILED_RESPONSE, 
            'response_description': f'Customer {last_name} {first_name} with phone number {phone_number} does not exist. Confirm phone number format of +<CountryCode><PhoneNumber>'
        }
        return data
    except Exception as e:
        data = {
            'response_code': settings.FAILED_RESPONSE, 
            'response_description': f'{e}'
        }
        return data

def confirm_account_customer_status(account):
    '''
        Confirm Account status and Customer status is active
    '''
    if account.status == 'I':
        raise Exception(f"Customer Account ({account}) currently INACTIVE. Contact Admin for help")
    elif account.account_holders_name.status == 'I':
        raise Exception(f"Customer ({account.account_holders_name}) currently INACTIVE. Contact Admin for help")
    
def currency_formatter(number):
    curr = float(number)
    currency = f"{curr:,}"
    return currency

