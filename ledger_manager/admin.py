from django.contrib import admin
from .models import AccountHistory, CurrencyType, Customer, ProductCategory, ProductType, Account

# Register your models here.

class CurrencyTypeAdmin(admin.ModelAdmin):
    search_fields = ['name', 'symbol', 'exchange_rate', 'status']
    list_display = ('name', 'symbol', 'exchange_rate', 'status')


class AccountInline(admin.TabularInline):
    model = Account
    readonly_fields = ('account_product_type', 'account_number', 'date_created', 'ledger_balance', 'actual_balance')

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False
    
    def has_change_permission(self, request, *args, **kwargs) -> bool:
        return False


class CustomerAdmin(admin.ModelAdmin):
    search_fields = ['last_name', 'first_name', 'phone_number', 'email']
    list_display = ('last_name', 'first_name', 'phone_number', 'email')

    inlines = [
        AccountInline
    ]

class AccountHistoryInline(admin.TabularInline):
    model = AccountHistory
    readonly_fields = ('user_ref', 'posting_type', 'amount', 'balance', 'transaction_date', 'narration', 'transaction_ref')
    # exclude = ['user_ref']

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False
    
    def has_change_permission(self, request, *args, **kwargs) -> bool:
        return False

class AccountAdmin(admin.ModelAdmin):
    # change_list_template = 'admin/account_change_list.html'
    search_fields = ['account_number', 'account_holders_name__last_name', 'account_holders_name__first_name']
    list_display = ('account_holders_name', 'account_product_type', 'account_number')
    fields = ['account_holders_name', 'account_product_type', 'account_number', 'ledger_balance', 'actual_balance', 'status']
    readonly_fields = ('account_number',)
    list_filter = ('account_product_type',)
    # form = AccountForm

    inlines = [
        AccountHistoryInline,
    ]

    def has_add_permission(self, request):
        return True

    # def has_delete_permission(self, request, obj=None):
    #     return False
    
    def has_change_permission(self, request, *args, **kwargs) -> bool:
        return False
    

admin.site.register(CurrencyType, CurrencyTypeAdmin)
admin.site.register(Customer, CustomerAdmin)
admin.site.register(ProductCategory)
admin.site.register(ProductType)
admin.site.register(AccountHistory)
admin.site.register(Account, AccountAdmin)
