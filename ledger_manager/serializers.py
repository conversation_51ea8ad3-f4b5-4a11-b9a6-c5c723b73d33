from rest_framework import serializers

from .models import Account, AccountH<PERSON>ory, Customer, ProductCategory, ProductType, AccountFundTransfer
from .validators import validate_phone_number, validate_amount


class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = Customer


class ProductCategorySerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = ProductCategory


class ProductTypeSerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = ProductType


class CreateWalletAccountSerializer(serializers.ModelSerializer):
    first_name = serializers.Char<PERSON><PERSON>(max_length=30)
    last_name = serializers.CharField(max_length=30)
    phone_no = serializers.CharField(max_length=16, validators=[validate_phone_number])
    email = serializers.EmailField()
    opening_balance = serializers.DecimalField(max_digits=12, default="0.00", decimal_places=2, validators=[validate_amount])

    class Meta:
        fields = ['first_name', 'last_name', 'phone_no', 'email', 'account_product_type', 'opening_balance']
        model = Account


class AccountHistorySerializer(serializers.ModelSerializer):
    class Meta:
        # fields = '__all__'
        model = AccountHistory
        exclude = ['balance', 'user_ref']


class AccountFundTransferSerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = AccountFundTransfer


class AccountSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='account_holders_name')
    phone_number = serializers.CharField(source='account_holders_name.phone_number')
    class Meta:
        fields = (
            'name', 'phone_number', 'account_product_type', 'account_number', 'date_created',
            'ledger_balance', 'actual_balance', 'status'
        )
        model = Account
from rest_framework import serializers

from .models import Account, AccountHistory, Customer, ProductCategory, ProductType, AccountFundTransfer
from .validators import validate_phone_number, validate_amount


class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = Customer


class ProductCategorySerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = ProductCategory


class ProductTypeSerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = ProductType


class CreateWalletAccountSerializer(serializers.ModelSerializer):
    first_name = serializers.CharField(max_length=30)
    last_name = serializers.CharField(max_length=30)
    phone_no = serializers.CharField(max_length=16, validators=[validate_phone_number])
    email = serializers.EmailField()
    opening_balance = serializers.DecimalField(max_digits=12, default="0.00", decimal_places=2, validators=[validate_amount])

    class Meta:
        fields = ['first_name', 'last_name', 'phone_no', 'email', 'account_product_type', 'opening_balance']
        model = Account


class AccountHistorySerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = AccountHistory
        # exclude = ['balance',]


class AccountFundTransferSerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = AccountFundTransfer


class AccountSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='account_holders_name')
    phone_number = serializers.CharField(source='account_holders_name.phone_number')
    class Meta:
        fields = (
            'name', 'phone_number', 'account_product_type', 'account_number', 'date_created',
            'ledger_balance', 'actual_balance', 'status'
        )
        model = Account
