from django import forms
from .form_validators import validate_amount, validate_account

from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit

class PostingEngineForm(forms.Form):
    posting_type = forms.ChoiceField(label="Posting Type", choices=[
        ("credit", "CREDIT"), ("debit", "DEBIT")
    ])
    amount = forms.FloatField(validators=[validate_amount])
    account = forms.CharField(max_length=15, validators=[validate_account])
    
    
class PostingEngineHelper(FormHelper):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.form_method = 'post'
        self.layout = Layout(
            'posting_type',
            'amount',
            'account',
            Submit("submits", "Submit", css_class="btn btn-primary")
        )
        self.render_required_fields = True
        # required fields marked True for css styling

class FundTransferForm(forms.Form):
    source_account = forms.CharField(max_length=15, validators=[validate_account])
    destination_account = forms.CharField(max_length=15, validators=[validate_account])
    amount = forms.FloatField(validators=[validate_amount])
    
class FundTransferHelper(FormHelper):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.form_method = 'post'
        self.layout = Layout(
            'source_account',
            'destination_account',
            'amount',
            Submit("submits", "Submit", css_class="btn btn-primary")
        )
        self.render_required_fields = True