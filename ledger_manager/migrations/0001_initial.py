# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.db import migrations, models
import django.db.models.deletion
import ledger_manager.validators
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_number', models.CharField(max_length=15, unique=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='Date Created')),
                ('ledger_balance', models.DecimalField(decimal_places=2, default='0.00', max_digits=12)),
                ('actual_balance', models.DecimalField(decimal_places=2, default='0.00', max_digits=12)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=1)),
            ],
            options={
                'verbose_name': 'Account',
                'verbose_name_plural': 'Accounts',
                'db_table': 'wallet_account',
            },
        ),
        migrations.CreateModel(
            name='CurrencyType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('symbol', models.CharField(max_length=5, unique=True)),
                ('exchange_rate', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
            ],
            options={
                'verbose_name': 'Currency Type',
                'verbose_name_plural': 'Currency Type',
                'db_table': 'wallet_currency_type',
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=20)),
                ('last_name', models.CharField(max_length=20)),
                ('phone_number', models.CharField(max_length=16, unique=True, validators=[ledger_manager.validators.validate_phone_number])),
                ('email', models.EmailField(blank=True, max_length=254, null=True, unique=True)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=1)),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customer',
                'db_table': 'wallet_customer',
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=10, unique=True)),
                ('name', models.CharField(max_length=20, unique=True)),
            ],
            options={
                'verbose_name': 'Product Category',
                'verbose_name_plural': 'Product Categories',
                'db_table': 'wallet_product_category',
            },
        ),
        migrations.CreateModel(
            name='ProductType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=10, unique=True)),
                ('name', models.CharField(max_length=20, unique=True)),
                ('allow_overdraft', models.CharField(choices=[('A', 'Allow'), ('D', 'Deny')], default='D', max_length=1)),
                ('minimum_balance', models.DecimalField(blank=True, decimal_places=2, default='0.00', max_digits=8, null=True)),
                ('currency', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='ledger_manager.currencytype', to_field='symbol')),
                ('product_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ledger_manager.productcategory', to_field='code')),
            ],
            options={
                'verbose_name': 'Product Type',
                'verbose_name_plural': 'Product Types',
                'db_table': 'wallet_product_type',
            },
        ),
        migrations.CreateModel(
            name='AccountHistory',
            fields=[
                ('transaction_ref', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False, unique=True)),
                ('user_ref', models.CharField(blank=True, max_length=25, null=True, unique=True)),
                ('posting_type', models.CharField(max_length=6, validators=[ledger_manager.validators.validate_posting_type])),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=12, validators=[ledger_manager.validators.validate_amount])),
                ('balance', models.DecimalField(decimal_places=2, default='0.00', max_digits=12)),
                ('narration', models.CharField(blank=True, max_length=250, null=True)),
                ('transaction_date', models.DateTimeField(auto_now_add=True, verbose_name='Transaction Date')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ledger_manager.account', to_field='account_number')),
            ],
            options={
                'verbose_name': 'Account History',
                'verbose_name_plural': 'Account History',
                'db_table': 'wallet_account_history',
                'ordering': ('-transaction_date',),
            },
        ),
        migrations.CreateModel(
            name='AccountFundTransfer',
            fields=[
                ('transaction_ref', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False, unique=True)),
                ('user_ref', models.CharField(max_length=25, null=True, unique=True)),
                ('source_account_ref', models.UUIDField(blank=True)),
                ('destination_account_ref', models.UUIDField(blank=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=12, validators=[ledger_manager.validators.validate_amount])),
                ('narration', models.CharField(blank=True, max_length=250, null=True)),
                ('transaction_date', models.DateTimeField(auto_now_add=True, verbose_name='Transaction Date')),
                ('destination_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='destination_account', to='ledger_manager.account', to_field='account_number')),
                ('source_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_account', to='ledger_manager.account', to_field='account_number')),
            ],
            options={
                'verbose_name': 'Account Transfer',
                'verbose_name_plural': 'Account Transfer',
                'db_table': 'wallet_account_transfer',
            },
        ),
        migrations.AddField(
            model_name='account',
            name='account_holders_name',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ledger_manager.customer', to_field='phone_number'),
        ),
        migrations.AddField(
            model_name='account',
            name='account_product_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ledger_manager.producttype', to_field='code'),
        ),
    ]
