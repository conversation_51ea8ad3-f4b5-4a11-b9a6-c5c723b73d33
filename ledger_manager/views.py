import json
import ast
from datetime import datetime, timedelta
from builtins import UnboundLocalError
from django.core.exceptions import ObjectDoesNotExist
from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import render, redirect

from rest_framework.reverse import reverse
from rest_framework import viewsets, status
from rest_framework.generics import CreateAPIView, get_object_or_404
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError
from rest_framework import generics
from django.views.decorators.csrf import csrf_exempt
from oauth2_provider.contrib.rest_framework import permissions
import requests

from bank.models import Bank
from common.functions import get_user_token
from .models import Account, AccountFundTransfer, AccountHistory, Customer, ProductCategory, ProductType
from .serializers import (AccountFundTransferSerializer, AccountHistorySerializer, 
    AccountSerializer, CreateWalletAccountSerializer, CustomerSerializer, 
    ProductCategorySerializer, ProductTypeSerializer)
# from logic_omnichannel import settings
from django.conf import settings
from .functions import (
    generate_account_number, credit_account, debit_account, validate_customer,
    confirm_account_customer_status, currency_formatter)
from .forms import (
    PostingEngineForm, PostingEngineHelper, 
    FundTransferForm, FundTransferHelper)
from .functions import AccountHistoryPagination



# Create your views here.

class CustomerViewset(viewsets.ModelViewSet):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer


class ProductCategoryViewset(viewsets.ModelViewSet):
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer


class ProductTypeViewset(viewsets.ModelViewSet):
    queryset = ProductType.objects.all()
    serializer_class = ProductTypeSerializer


class CreateAccountView(CreateAPIView):

    # permission_classes = [permissions.TokenHasReadWriteScope]
    
    serializer_class = CreateWalletAccountSerializer
    
    def post(self, request):
        try:
            serializer = CreateWalletAccountSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            first_name = serializer.validated_data['first_name']
            last_name = serializer.validated_data['last_name']
            phone_number = serializer.validated_data['phone_no']
            product_type = serializer.validated_data['account_product_type']
            opening_balance = serializer.validated_data['opening_balance']
        except ValidationError as e:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': serializer.errors
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Data Error / Integrity constraint >>> {e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
            
        # Confirm customer validity
        customer = validate_customer(phone_number, first_name, last_name)
        if type(customer) == dict:
            return Response(customer, status.HTTP_400_BAD_REQUEST)
        
        # Generate an account number
        account_number = generate_account_number()

        # Try to create the account
        try:
            Account.objects.create(
                account_holders_name = customer,
                account_product_type = product_type,
                account_number = account_number,
                ledger_balance = opening_balance,
                actual_balance = opening_balance
            ) 
            data = {
                'response_code': settings.SUCCESS_RESPONSE,
                'data': {
                    'Account Name': f'{customer}',
                    'Account Type': f'{product_type}',
                    'Account Number': f'{account_number}',
                    'Ledger Balance': f'{opening_balance}',
                    'Actual Balance': f'{opening_balance}'
                }
            }
            return Response(data, status.HTTP_201_CREATED)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)


class AccountViewset(viewsets.ViewSet):
    
    def list(self, request):
        try:
            account_number = request.data.get('account_number')
        except Exception:
            account_number = False
        if account_number:
            try:
                account = Account.objects.get(account_number=account_number)
            except ObjectDoesNotExist:
                data = {
                    'response_code': settings.FAILED_RESPONSE, 
                    'response_description': f'Invalid account number({account_number}). Kindly confirm.'
                }
                return Response(data, status.HTTP_400_BAD_REQUEST)
            serializer = AccountSerializer(account)
            return Response(serializer.data)
        else:
            queryset = Account.objects.all()
            serializer = AccountSerializer(queryset, many=True)
            return Response(serializer.data)

    def retrieve(self, request, pk=None):
        queryset = Account.objects.all()
        account = get_object_or_404(queryset, id=pk)
        serializer = AccountSerializer(account)
        return Response(serializer.data)


class TransactionStatusViewset(viewsets.ViewSet):
    
    def list(self, request):
        user_ref = request.data.get('user_ref')
        queryset = AccountHistory.objects.all()
        transaction = get_object_or_404(queryset, user_ref=user_ref)
        serializer = AccountHistorySerializer(transaction)
        return Response(serializer.data)


class PostingEngine(CreateAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    serializer_class = AccountHistorySerializer

    @csrf_exempt
    def post(self, request):
        try:
            serializer = AccountHistorySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            posting_type = serializer.validated_data['posting_type']
            account = serializer.validated_data['account']
            amount = serializer.validated_data['amount']
        except ValidationError as error:
            error_track = []
            for key in error.detail.keys():
                if key == 'account':
                    error_track.append(f'Invalid account number({request.data.get("account")}). Kindly confirm')
                elif key == 'user_ref':
                    error_track.append(f'User ref({request.data.get("user_ref")}) already exists')
                elif key == 'amount':
                    error_track.append(f'Invalid amount. Negative amount {request.data.get("amount")} not accepted')
                else:
                    error_track.append(f'{serializer.errors}')
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f"{' + '.join(error_track)}"
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Data Error / Integrity constraint >>> {e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        # Confirm Account status and Customer status is active
        try:
            confirm_account_customer_status(account)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)

        # Credit or Debit the account based on the posting_type       
        try:
            if posting_type.lower() in ['credit', 'cr', 'cre']:
                credit_account(account, amount)
                posting_type = 'CREDIT'
            elif posting_type.lower() in ['debit', 'db', 'dbt']:
                debit_account(account, amount)
                posting_type = 'DEBIT'
            else:
                raise Exception('Invalid posting type. (NOTE: posting type either "CREDIT" or "DEBIT")')
            account.save()
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)

        # Save into the account history
        try:
            serializer.save(
                balance = account.actual_balance,
                posting_type = posting_type,
                narration = "Admin/trans/Successful"
            )
            transaction_ref = serializer.data.get('transaction_ref')
            # accessing this code from the django rest frame work will make transaction_ref = None due to the way the serializer class is defined. the if block below resolve this
            if transaction_ref is None:
                track_account_statement = AccountHistory.objects.get(user_ref=serializer.data.get('user_ref'))
                transaction_ref = track_account_statement.transaction_ref
            
            data = {
                'response_code': settings.SUCCESS_RESPONSE,
                'data': {
                    'Account Name': f'{account.account_holders_name}',
                    'Account Type': f'{account.account_product_type}',
                    'Transaction Type': posting_type,
                    'Amount': amount,
                    'Account Number': f'{account.account_number}',
                    'Ledger Balance': f'{account.ledger_balance}',
                    'Actual Balance': f'{account.actual_balance}',
                    'Transaction Ref': f'{transaction_ref}'
                }
            }
            return Response(data, status.HTTP_200_OK)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
  

class AccountFundTransferView(APIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    
    def post(self, request):
        try:
            serializer = AccountFundTransferSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            source_account = serializer.validated_data['source_account']
            destination_account = serializer.validated_data['destination_account']
            amount = serializer.validated_data['amount']
            # narration = serializer.validated_data['narration']
        except ValidationError as error:
            error_track = []
            for key in error.detail.keys():
                if key == 'source_account' or key == 'destination_account':
                    error_track.append(f'Invalid {key} number({request.data.get(key)}). Kindly confirm.')
                elif key == 'user_ref':
                    error_track.append(f'User ref({request.data.get("user_ref")}) already exists.')
                elif key == 'amount':
                    error_track.append(f'Invalid amount. Negative amount {request.data.get("amount")} not accepted.')
                else:
                    error_track.append(f'{error.detail}')
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f"{' + '.join(error_track)}"
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Data Error / Integrity constraint >>> {e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        # Confirm Account status and Customer status is active
        try:
            confirm_account_customer_status(source_account)
            confirm_account_customer_status(destination_account)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        # Try to debit the source account and save into the account history
        try:
            debit_account(source_account, amount)
            source_account.save()
            source_account_statement = AccountHistory.objects.create(
                account = source_account,
                posting_type = 'DEBIT',
                amount = amount,
                balance = source_account.actual_balance,
                narration = "FundTransfer/trans/Successful"
            )
            source_account_transaction_ref = source_account_statement.transaction_ref
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        # Try to credit the destination account and save into the account history
        try:
            credit_account(destination_account, amount)
            destination_account.save()
            destination_account_statement = AccountHistory.objects.create(
                account = destination_account,
                posting_type = 'CREDIT',
                amount = amount,
                balance = destination_account.actual_balance,
                narration = "FundTransfer/trans/Successful"
            )
            destination_account_transaction_ref = destination_account_statement.transaction_ref
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        '''
            SOLVING THE REVERSAL PROBLEM
            1) Shallow solution would be to assume the debit action was successful but the credit action failed
            so in the credit exception block we do the reversal.
            2) But if the credit action can fail, that means the debit action can also fail.
            3) The concern would now be at what point can the code likely fail in the debit action.
            4) There are 3 major commands in the debit action which can be refactored to 2
            5) Based on the 2 command refactor if the first command runs successfully, the account would be debited while the second command create a reciept based on the success of the debit.
            6) If the first action fails the account would not be debited which is fine
            7) if the first action succeeds it leaves the second action to possibilty of failure
            8) at this point reversal is neccessary
            9) A possible solution is to utilise the finally block (fuzzy but possible)
            10) Incase of an exception the lifecycle would be try --> finally --> except
            11) So there is possiblity of using finally for reversal
            12) But in cases where there is not exception the lifecyle would be try --> finally so finally runs eitherways which is another source for concern.
        '''

        # Conclude the transaction by saving into the fund transfer table
        try:
            serializer.save(
                source_account_ref =source_account_transaction_ref,
                destination_account_ref = destination_account_transaction_ref
            )
            fund_transfer_statement = AccountFundTransfer.objects.get(
                source_account_ref=source_account_transaction_ref, destination_account_ref=destination_account_transaction_ref
            )
            transaction_ref = fund_transfer_statement.transaction_ref
            data = {
                'response_code' : settings.SUCCESS_RESPONSE,
                'data': {
                    'Source Account Name': f'{source_account.account_holders_name}',
                    'Source Account': f'{source_account}',
                    'Destination Account Name': f'{destination_account.account_holders_name}',
                    'Destination Account': f'{destination_account}',
                    'Transfer Amount': f'{amount}',
                    'Source Account Balance': f'{source_account.actual_balance}',
                    'Destination Account Balance': f"{destination_account.actual_balance}",
                    'Transaction Ref': f'{transaction_ref}'
                }
            }
            return Response(data, status.HTTP_200_OK)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)


class AccountHistoryStreamedView(APIView):
    
    paginator = AccountHistoryPagination()
    
    def post(self, request):
        
        try:
            start_date = datetime.strptime(request.data.get('start_date'), "%Y-%m-%d")    
            end_date = datetime.strptime(request.data.get('end_date'), "%Y-%m-%d") + timedelta(days=1)
            account_number = request.data.get('account_number')
        except ValueError:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': "Incorrect Date format entered. Ensure to enter as: `Year-Month-Date`"
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Error Processing Data: {e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        # Try to confirm the account number
        try:
            account = Account.objects.get(account_number=account_number)
        except ObjectDoesNotExist:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Invalid account number({account_number}). Kindly confirm.'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        # Get the account history based on the set date
        try:
            account_history = AccountHistory.objects.filter(account=account, transaction_date__range=[start_date, end_date]).order_by('-transaction_date')
            
            paginate_queryset = self.paginator.paginate_queryset(account_history, request)
            serializer = AccountHistorySerializer(paginate_queryset, many=True)
            
            data = {'response_code': settings.SUCCESS_RESPONSE,
                    'data': serializer.data}
            return self.paginator.get_paginated_response(data)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)


class AccountHistoryView(APIView):
    
    paginator = AccountHistoryPagination()
    
    def post(self, request):
        
        try:
            start_date = datetime.strptime(request.data.get('start_date'), "%Y-%m-%d")    
            end_date = datetime.strptime(request.data.get('end_date'), "%Y-%m-%d") + timedelta(days=1)
            account_number = request.data.get('account_number')
        except ValueError:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': "Incorrect Date format entered. Ensure to enter as: `Year-Month-Date`"
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Error Processing Data: {e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        # Try to confirm the account number
        try:
            account = Account.objects.get(account_number=account_number)
        except ObjectDoesNotExist:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Invalid account number({account_number}). Kindly confirm.'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        
        # Get the account history based on the set date
        try:
            account_history = AccountHistory.objects.filter(account=account, transaction_date__range=[start_date, end_date]).order_by('-transaction_date')
            payload_data = {}
            for index, transaction in enumerate(account_history):
                payload_data[index] = {
                    "user_ref": f"{transaction.user_ref}",
                    "transaction_ref": transaction.transaction_ref,
                    "posting_type": transaction.posting_type,
                    "amount": transaction.amount,
                    "balance": transaction.balance,
                    "narration": transaction.narration,
                    "account": f"{transaction.account}",
                    "transaction_date": f'{transaction.transaction_date}'
                }
            
            data = {'response_code': settings.SUCCESS_RESPONSE,
                    'data': payload_data}
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)

class GetAccountBalance(generics.GenericAPIView):
    
   
    def post(self, request):
        try:
            wallet_account_no = request.data.get("wallet_account_no")
            wallet_account = Account.objects.get(account_number=wallet_account_no)
            
            data = {
                "wallet_account": f"{wallet_account.account_holders_name.first_name} {wallet_account.account_holders_name.last_name}",
                "balance": wallet_account.actual_balance,
                "ledger_balance": wallet_account.ledger_balance
            }
            
            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "No Wallet Account Found",
                "response_detail": f'No Wallet Account Found for this Wallet Account Number',
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Failed Get Account Wallet Account Balance",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
            

class AccountStatus(APIView):

    def post(self, request):
        try:
            account_number = request.data.get('account_number')
            operation = request.data.get('operation')
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Data Error / Integrity constraint >>> {e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)

        # Confirm account exist and then perform the required operation
        try:
            account = Account.objects.get(account_number=account_number)
            if operation.lower() in ['block', 'inactive']:
                account.status = 'I'
                account_status = 'INACTIVE'
            elif operation.lower() in ['unblock', 'active']:
                account.status = 'A'
                account_status = 'ACTIVE'
            else:
                raise Exception(f'Invalid operation ({operation}). Valid operation -> ["block", "unblock"]')
            account.save()
            data = {
                'response_code': settings.SUCCESS_RESPONSE, 
                'response_description': f'Account Number ({account_number}) --> Account Status ({account_status})'
            }
            return Response(data, status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Invalid account number({account_number}). Kindly confirm.'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)


class CustomerStatus(APIView):
    
    def post(self, request):
        try:
            first_name = request.data.get('first_name')
            last_name = request.data.get('last_name')
            phone_number = request.data.get('phone_number')
            operation = request.data.get('operation')
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'Data Error / Integrity constraint >>> {e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)

        # Confirm customer validity
        customer = validate_customer(phone_number, first_name, last_name)
        if type(customer) == dict:
            return Response(customer, status.HTTP_400_BAD_REQUEST)

        # Perform the required operation on the validated customer
        try:
            if operation.lower() in ['block', 'inactive']:
                customer.status = 'I'
                customer_status = 'INACTIVE'
            elif operation.lower() in ['unblock', 'active']:
                customer.status = 'A'
                customer_status = 'ACTIVE'
            else:
                raise Exception(f'Invalid operation ({operation}). Valid operation -> ["block", "unblock"]')
            customer.save()
            data = {
                'response_code': settings.SUCCESS_RESPONSE, 
                'response_description': f'Customer ({str(customer)}) --> Account Status ({customer_status})'
            }
            return Response(data, status.HTTP_200_OK)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE, 
                'response_description': f'{e}'
            }
            return Response(data, status.HTTP_400_BAD_REQUEST)


@staff_member_required
def posting_engine(request):
    if request.method == "POST":
        form = PostingEngineForm(request.POST)
        if form.is_valid():
            account = form.cleaned_data["account"]
            amount = form.cleaned_data["amount"]
            posting_type = form.cleaned_data["posting_type"]
            
            data = {
                "account": account,
                "amount": amount,
                "posting_type": posting_type
            }
                      
            token_endpoint = reverse("token", request=request)
            get_token = get_user_token(
                client_id = settings.USSD_AUTH_ID,
                client_secret = settings.USSD_AUTH_SEC,
                token_endpoint=token_endpoint
            )
            token = get_token["access_token"]
            
            payload = json.dumps(data)
            url = reverse("posting_engine", request=request)
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
                }
            
            resp = requests.request("POST", url, headers=headers, data=payload)
            response = resp.json() 
            if response.get("response_code") == "00":
                data = {
                    "Account Name": response.get("data")["Account Name"],
                    "Account Type": response.get("data")["Account Type"],
                    "Transaction Type": response.get("data")["Transaction Type"],
                    "Transaction Amount": currency_formatter(response.get("data")["Amount"]),
                    "Account Number": response.get("data")["Account Number"],
                    "Account Balance": currency_formatter(response.get("data")["Actual Balance"]),
                    "Transaction Ref": response.get("data")["Transaction Ref"]
                }
                
                return redirect('success', data=data)
            else:
                data = {
                    "Message": response.get("response_description")
                }
                
                if data is None:
                    data = {"Message": response.get("detail")}
                    
                helper = PostingEngineHelper()
                context = {"data": data}
                return render(request, "failed.html", context)
        else:
            helper = PostingEngineHelper()
            context = {
                "form": form,
                "helper": helper
            }
            return render(request, "posting.html", context=context)
            
    form = PostingEngineForm()
    helper = PostingEngineHelper()
    context = {
        "form": form,
        "helper": helper
    }
    return render(request, "posting.html", context=context)


def success(request, **kwargs):
    str_data = kwargs.get('data', None)
    data = ast.literal_eval(str_data)
    return render(request, 'successful.html', {"data": data})


@staff_member_required
def fund_transfer(request):
    if request.method == "POST":
        form = FundTransferForm(request.POST)
        if form.is_valid():
            source_account = form.cleaned_data["source_account"]
            destination_account = form.cleaned_data["destination_account"]
            amount = form.cleaned_data["amount"]
            
            data = {
                "source_account": source_account,
                "destination_account": destination_account,
                "amount": amount
            }
            
            token_endpoint = reverse("token", request=request)
            get_token = get_user_token(
                client_id = settings.USSD_AUTH_ID,
                client_secret = settings.USSD_AUTH_SEC,
                token_endpoint=token_endpoint
            )
            token = get_token["access_token"]
            
            payload = json.dumps(data)
            url = reverse("account_fund_transfer", request=request)
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
                }
            
            resp = requests.request("POST", url, headers=headers, data=payload)
            response = resp.json() 
            if response.get("response_code") == "00":
                data = {
                    "Source Account Name": response.get("data")["Source Account Name"],
                    "Source Account": response.get("data")["Source Account"],
                    "Destination Account Name": response.get("data")["Destination Account Name"],
                    "Destination Account": response.get("data")["Destination Account"],
                    "Amount Transfered": currency_formatter(response.get("data")["Transfer Amount"]),
                    "Source Account Balance": currency_formatter(response.get("data")["Source Account Balance"]),
                    "Destination Account Balance":currency_formatter(response.get("data")["Destination Account Balance"]),
                    "Transaction Reference": response.get("data")["Transaction Ref"]
                }
                
                return redirect('success', data=data)
            else:
                data = {
                    "Message": response.get("response_description")
                }
                
                if data is None:
                    data = {"Message": response.get("detail")}
                    
                helper = FundTransferHelper()
                context = {"data": data}
                return render(request, "failed.html", context)
        else:
            helper = FundTransferHelper()
            context = {
                "form": form,
                "helper": helper
            }
            return render(request, "fund_trans.html", context=context)
            
    form = FundTransferForm()
    helper = FundTransferHelper()
    context = {
        "form": form,
        "helper": helper
    }
    return render(request, "fund_trans.html", context=context)
                
