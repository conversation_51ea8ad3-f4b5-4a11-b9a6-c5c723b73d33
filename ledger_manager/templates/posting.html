{% extends 'main.html' %}

{% load crispy_forms_tags %}

{% block content %}



<style>
    .outline-frame{
        width: 900px;
        height: 400px;
        margin: 0 auto;
        padding: 40px;
        border-radius: 15px;
        border: 1px solid #e0e0e0;
        background-color: transparent;
    }

    .background-frame{
        width: 1000px;
        height: 600px;
        margin: 0 auto;
        padding-top: 60px;
        background-color: white;
    }

    .trans-rect{
        background-color: black;
        margin-bottom: 6px;
        margin-left: 645px;
        width: 400px;
        height: 58px;
    }

    .posting{
        font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
        font-size: 30px;
        font-weight: bold;
        text-align: center;
        color: white;
        padding-top: 10px;
    }

    .trans-des{
        text-align: center;
        font-size: 25px;
        margin-bottom: 35px;
        font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
        font-weight: lighter;
    }
</style>


<div class="main-section">
    <div class="trans-rect">
        <h5 class="posting">Account Posting Engine</h5>
    </div>
    <div class="trans-des">
        <p>Credit or Debit Wallet Account</p>
    </div>
    <div class="background-frame">
        <div class="outline-frame">
            <div>
                {% crispy form helper %}
            </div>
            <style>
                div .form-group {
                    margin-bottom: 15px;
                    }
            </style>
            </div>
    </div>
    
</div>
    

{% endblock %}


