{% extends 'main.html' %}

{% block content%}

<style>
    .background-frame{
        width: 1000px;
        height: 600px;
        margin: 0 auto;
        padding-top: 60px;
        background-color: white;
    }

    .outline-frame{
        width: 900px;
        height: 400px;
        margin: 0 auto;
        padding: 40px;
        border-radius: 15px;
        border: 1px solid #e0e0e0;
        background-color: transparent;
    }

    .success-rect{
        background-color: rgb(78, 252, 34);
        margin-bottom: 15px;
        margin-left: 380px;
        width: 250px;
        height: 40px;
        border-radius: 20px;
    }

    .success-text{
        font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
        font-size: 21px;
        font-weight: bold;
        text-align: center;
        color: white;
        padding-top: 10px;
    }

    .details{
        font-size: 17px;
        font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
        font-weight: bolder;
        color: black;
    }

    a {
        text-decoration: none;
        color: white;
        font-size: 24px;
        font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
        font-weight: bolder;
        margin-left: 28px;
        
    }

    .btn.btn-primary {
        height: 25px;
        width: 100px; 
        margin-left: 150px; 
        padding-bottom: 38px !important;
      }

    .back-link{
        margin-left: 4px;
    }
    
    

</style>

<div class="btn btn-primary">
    <a class="back-link" href="{{request.META.HTTP_REFERER}}">Back</a>
</div>


<div class="background-frame">
    <div class="success-rect">
        <h5 class="success-text">Successful Transaction</h5>
    </div>
    <div class="outline-frame">
        <ul>
            {% for key, value in data.items %}
                <p class="details">{{ key }}: {{ value }}</p>
            {% endfor %}
        </ul>
    </div>
</div>

{% endblock%}

