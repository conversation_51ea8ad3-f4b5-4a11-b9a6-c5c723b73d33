import uuid

from django.db import models
from django.utils.translation import gettext_lazy as _

from .validators import validate_amount, validate_phone_number, validate_posting_type
from .functions import generate_account_number, call_user_ref_func

# Create your models here.

class CurrencyType(models.Model):

    def __str__(self):
        return self.name

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    name = models.CharField(max_length=200)
    symbol = models.CharField(max_length=5, unique=True)
    exchange_rate = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = 'wallet_currency_type'
        verbose_name = "Currency Type"
        verbose_name_plural = "Currency Type"


class Customer(models.Model):

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    first_name = models.CharField(max_length=20)
    last_name = models.CharField(max_length=20)
    phone_number = models.CharField(max_length=16, unique=True, validators=[validate_phone_number])
    email = models.EmailField(unique=True, blank=True, null=True)
    status = models.CharField(
        max_length=1,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )

    def __str__(self) -> str:
        return f'{self.first_name} {self.last_name}'
    
    def __repr__(self) -> str:
        return f'{self.first_name} {self.last_name}'

    class Meta:
        db_table = 'wallet_customer'
        verbose_name = 'Customer'
        verbose_name_plural= 'Customer'    


class ProductCategory(models.Model):
    class Meta:
        db_table = 'wallet_product_category'
        verbose_name = 'Product Category'
        verbose_name_plural= 'Product Categories'

    code = models.CharField(max_length=10, unique=True)
    name = models.CharField(max_length=20, unique=True)

    def __str__(self) -> str:
        return self.name     


class ProductType(models.Model):
    class Meta:
        db_table = 'wallet_product_type'
        verbose_name = 'Product Type'
        verbose_name_plural= 'Product Types'

    ALLOW = 'A'
    DENY = 'D'
    ALLOW_OVERDRAFT_CHOICES = (
        (ALLOW, 'Allow'),
        (DENY, 'Deny'),
    )
    product_category = models.ForeignKey(ProductCategory, on_delete=models.CASCADE, to_field='code')
    code = models.CharField(max_length=10, unique=True)
    name = models.CharField(max_length=20, unique=True)
    currency = models.ForeignKey(CurrencyType, on_delete=models.CASCADE, null=True, to_field='symbol')
    allow_overdraft = models.CharField(
        max_length=1,
        choices=ALLOW_OVERDRAFT_CHOICES,
        default=DENY
    )
    minimum_balance = models.DecimalField(blank=True, max_digits=8, decimal_places=2, default="0.00", null=True)

    def __str__(self) -> str:
        return self.name


class Account(models.Model):

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )
    
    account_holders_name = models.ForeignKey(Customer, on_delete=models.CASCADE, to_field='phone_number')
    account_product_type = models.ForeignKey(ProductType, on_delete=models.CASCADE, to_field='code')
    account_number = models.CharField(max_length=15, unique=True)
    date_created = models.DateTimeField('Date Created', auto_now_add=True, null=True)
    ledger_balance = models.DecimalField(max_digits=12, default="0.00", decimal_places=2)
    actual_balance = models.DecimalField(max_digits=12, default="0.00", decimal_places=2)
    status = models.CharField(
        max_length=1,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )

    def __str__(self) -> str:
        return self.account_number
    
    def save(self, *args, **kwargs):
        if not self.account_number:
            self.account_number = generate_account_number()
        super().save(*args, **kwargs)

    class Meta:
        db_table = 'wallet_account'
        verbose_name = 'Account'
        verbose_name_plural= 'Accounts'    


class AccountHistory(models.Model):
    
    class Meta:
        db_table = 'wallet_account_history'
        verbose_name = 'Account History'
        verbose_name_plural= 'Account History'
        ordering = ('-transaction_date',)

    transaction_ref = models.UUIDField(primary_key=True, default=uuid.uuid4, unique=True)
    user_ref = models.CharField(max_length=25, unique=True, null=True, blank=True)
    account = models.ForeignKey(
        Account, on_delete=models.CASCADE, to_field='account_number'
        )
    posting_type = models.CharField(
        max_length=6,
        validators=[validate_posting_type]
    )
    amount = models.DecimalField(max_digits=12, default="0.00", decimal_places=2, validators=[validate_amount])
    balance = models.DecimalField(max_digits=12, default="0.00", decimal_places=2)
    narration = models.CharField(max_length=250, null=True, blank=True)
    transaction_date = models.DateTimeField('Transaction Date', auto_now_add=True)

    def __str__(self):
        return ''
    
    def save(self, *args, **kwargs):
        if not self.user_ref:
            self.user_ref = call_user_ref_func(
                email=self.account.account_holders_name.email,
                pk=self.account.account_holders_name.pk
            )
        super().save(*args, **kwargs)
        

class AccountFundTransfer(models.Model):
    transaction_ref = models.UUIDField(primary_key=True, default=uuid.uuid4, unique=True)
    user_ref = models.CharField(max_length=25, unique=True, null=True)
    source_account = models.ForeignKey(Account, on_delete=models.CASCADE, to_field='account_number', related_name='source_account')
    source_account_ref = models.UUIDField(blank=True)
    destination_account = models.ForeignKey(Account, on_delete=models.CASCADE, to_field='account_number', related_name='destination_account')
    destination_account_ref = models.UUIDField(blank=True)
    amount = models.DecimalField(max_digits=12, default="0.00", decimal_places=2, validators=[validate_amount])
    narration = models.CharField(max_length=250, null=True, blank=True)
    transaction_date = models.DateTimeField('Transaction Date', auto_now_add=True)

    class Meta:
        db_table = 'wallet_account_transfer'
        verbose_name = "Account Transfer"
        verbose_name_plural = "Account Transfer"