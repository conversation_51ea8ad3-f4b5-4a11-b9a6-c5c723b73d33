import logging
import datetime

from logic_omnichannel.celery import app
from common.functions import run_settlement, switch_settlement
from common.services import get_name_enquiry, send_money
from .models import Bills
from bank.models import Bank
from django.db.models import Q


@app.task(name="run_failed_settlement")
def run_failed_settlement():
    """
    failed airtime settlement occur when either switch settlement or partner
    bank settlement falls through. This check handles both cases of failure
    and resolves.
    First off, we get the bills where omni channel settlement has been successful
    but either switch settlement or partner settlement has failed
    we rerun the settlement
    """
    log = logging.getLogger("django")
    log.info("FROM CELERY -> Rerunning failed settlement")
    try:
        log.info("FROM CELERY -> Trying to get pending airtime settlement bills")
        bills = Bills.objects.filter(
            Q(partner_bank_config__transaction_type="airtime_data")
            | Q(partner_bank_config__transaction_type="bill_payment"),
            is_switch_settlement_reconciled=False,
            is_reconciled=True,
        )

        for bill in bills:
            transaction_params = {
                "partner_bank_settlement_amount": bill.partner_bank_settlement_amount,
                "switch_settlement_amount": bill.switch_settlement_amount,
            }
            bill_config = bill.partner_bank_config
            bill_id = bill.id
            app_code = bill_config.partner_bank.alias
            log.info(f"FROM CELERY -> Running settlement for bill ID: {bill_id}")
            if not bill.is_partner_bank_settlement_reconciled:
                # run full settlement

                run_settlement(
                    bill_config,
                    bill_id,
                    transaction_params,
                    app_code,
                    send_money,
                    get_name_enquiry,
                )
            else:
                switch_settlement(
                    bill_config,
                    bill_id,
                    transaction_params,
                    app_code,
                    send_money,
                    get_name_enquiry,
                )

            log.info(
                f"FROM CELERY -> Finished Running settlement for bill ID: {bill_id}"
            )

        log.info(f"FROM CELERY -> Finished Running settlement for {len(bills)} bills")

    except Exception as e:
        log.info(f"Error running settlement -> {e}")
        log.info(f"Error running settlement -> {e}")
