import datetime
from django.utils import timezone
from decimal import Decimal
from transactions.models import AirtimeData, BillPayment, SendMoney
from common.services import get_name_enquiry, send_email, send_money
from common.models import SendMoneyClass
from common.permissions import LambdaAccessPermission
from common.functions import CustomPageNumberPagination
from user_profile.permissions import IsBankAdmin
import logging
from threading import Thread

from billing.models import (
    Bills, 
    Configurations, 
    Fee, 
    BankBillDefinitions, 
    BankBillDuePayments
)
from .functions import (
    is_warning,
    is_billable,
    generate_bill_due_payment,
    is_payment_inactive,
    calc_next_bill_date
)
from common.functions import (
    generate_bill_reports,
    get_local_inter_bank_send_money_transaction_charge,
    get_reference,
    run_settlement,
    set_response_data,
    get_serializer_key_error
)
# from logic_omnichannel import 
from django.conf import settings
from bank.models import Bank
from .serializers import(
    BillDefinitionSerializer,
    UpdateBillDefinitionSerializer, 
    GetPaymentsReferenceSerializer
)

from oauth2_provider.contrib.rest_framework import permissions
from rest_framework import generics, status, viewsets
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import AllowA<PERSON>, IsAdminUser
from rest_framework.response import Response
from django.db.models import Sum
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.db import IntegrityError

# Create your views here.


def update_failed_billing_counter(app_code, failed=True):
    """
    tracks the amount of time billing fails
    """
    failed_billing_counter_dict = cache.get("failed_billing_counter_dict")
    if failed_billing_counter_dict:
        if failed:
            failed_billing_count = failed_billing_counter_dict.get(app_code)
            if not failed_billing_count:
                failed_billing_count = 0
                failed_billing_counter_dict[app_code] = failed_billing_count
            failed_billing_counter_dict[app_code] = failed_billing_count + 1
        else:
            failed_billing_counter_dict[app_code] = 0
    else:
        failed_billing_counter_dict = {app_code: 1 if failed else 0}

    cache.set("failed_billing_counter_dict", failed_billing_counter_dict)


def get_failed_billing_count(app_code):
    """
    Get the failed billing count
    """
    failed_billing_counter_dict = cache.get("failed_billing_counter_dict")
    if failed_billing_counter_dict:
        count = failed_billing_counter_dict.get(app_code, 0)
        return count
    else:
        return 0


class TransactionsBilling(generics.ListAPIView):

    permission_classes = (LambdaAccessPermission,)

    def list(self, request, transaction_type, app_code):

        log = logging.getLogger("omni_billing")
        log.info(
            f"entering GET TRANSACTIONS BILL for {transaction_type} for partner {app_code}"
        )

        try:
            """
            Try to generate the transactions bill
            """
            bill_config = Configurations.objects.get(
                transaction_type=transaction_type, partner_bank__alias=app_code
            )
            transaction_params = self.get_transactions_bill_params(
                transaction_type, app_code
            )
            if transaction_params.get("no_of_transactions", 0) > 0:
                bill_ref = get_reference()
                bill = Bills.objects.create(
                    partner_bank_config=bill_config,
                    no_of_transactions=transaction_params.get("no_of_transactions"),
                    total_amount=transaction_params.get("total_amount"),
                    omni_channel_bill_amount=transaction_params.get(
                        "omni_channel_bill_amount"
                    ),
                    bill_ref=bill_ref,
                    starting_transaction_id=transaction_params.get(
                        "start_transaction_id"
                    ),
                    ending_transaction_id=transaction_params.get("end_transaction_id"),
                )
                if transaction_type in ("airtime_data", "bill_payment"):
                    bill.switch_settlement_amount = transaction_params.get(
                        "switch_settlement_amount"
                    )
                    bill.partner_bank_settlement_amount = transaction_params.get(
                        "partner_bank_settlement_amount"
                    )
                    bill.save()
            else:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="No Transactions To Be Billed",
                    detail="No Transactions To Be Billed",
                )
                data["is_partner_bank_active"] = (
                    True if bill_config.partner_bank.is_active else False
                )
                log.info(f"exiting GET TRANSACTIONS BILL with: {data}")
                return Response(data, status=status.HTTP_200_OK)

        except Configurations.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Billing Configuration Not Available",
                detail=f"Bill Configuration not available for app_code: {app_code} and transaction type: {transaction_type}",
            )
            log.info(f"exiting GET TRANSACTIONS BILL with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Generating Bill",
                detail=f"Error Generating Bill -> {e}",
            )
            log.info(f"exiting GET TRANSACTIONS BILL with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            """
            Try to debit partner bank prefund account and credit omni_channels account
            """
            payload_class = SendMoneyClass()

            payload_class.send_money_type = "INTRA"
            payload_class.sender_account = bill_config.prefund_account
            payload_class.sender_institution_code = bill_config.partner_bank.code
            payload_class.destination_account = bill_config.destination_account
            payload_class.destination_institution_code = bill_config.partner_bank.code
            payload_class.amount = transaction_params.get("omni_channel_bill_amount")
            payload_class.narration = (
                f"FT{bill_config.prefund_account} {bill_config.destination_account}"
            )
            payload_class.transaction_ref = bill_ref

            resp = send_money(payload_class, app_code)
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                # # run this thread to update all the charged transactions as resolved it is very important for this thread to run successfully
                # Thread(target=resolve_billed_transactions, args=(transaction_params.get('transactions'), bill_ref)).start()

                # confirm if we should save into the send money transactions table
                bill.is_reconciled = True
                bill.save()

                # run this thread to generate bill report. it is also important for this to run successfully
                Thread(
                    target=generate_bill_reports,
                    args=(
                        transaction_params.get("transactions"),
                        bill,
                        "success",
                        bill_ref,
                        transaction_type,
                        send_email,
                    ),
                ).start()
                update_failed_billing_counter(app_code, failed=False)
                if transaction_type in ("airtime_data", "bill_payment"):
                    """
                    For airtime and data, we also do further settlement to the partner bank and also
                    the switch settlement account
                    """
                    Thread(
                        target=run_settlement,
                        args=(
                            bill_config,
                            bill.id,
                            transaction_params,
                            app_code,
                            send_money,
                            get_name_enquiry,
                        ),
                    ).start()

                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description=f"{transaction_type} Billing Successful",
                    detail=f"{transaction_type} Billing Successful",
                )
                data["is_partner_bank_active"] = (
                    True if bill_config.partner_bank.is_active else False
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                Thread(
                    target=generate_bill_reports,
                    args=(
                        transaction_params.get("transactions"),
                        bill,
                        "failed",
                        bill_ref,
                        transaction_type,
                        send_email,
                    ),
                ).start()
                update_failed_billing_counter(app_code)

                data = set_response_data(
                    code="02",  # special code for failed billing
                    description=f'{transaction_type} Billing Failed -> {resp.get("response_description")}',
                    detail=f'{transaction_type} Billing Failed -> {resp.get("response_description")}',
                )
                data["failed_transactions_count"] = get_failed_billing_count(app_code)
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Occurred in Billing Transaction: {e}",
                detail=str(e),
            )
            log.info(f"exiting GET TRANSACTIONS BILL with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def get_transactions_bill_params(self, transaction_type, app_code):
        """
        Generates the required transaction params required to generate a transactions bill
        """
        if transaction_type == "send_money":
            transactions = SendMoney.objects.filter(
                sender_account__bank__alias=app_code,
                is_resolved=False,
                is_transaction_chargable=True,
                status="S",
            ).order_by("date_created")
        elif transaction_type == "airtime_data":
            transactions = AirtimeData.objects.filter(
                sender_account__bank__alias=app_code,
                is_resolved=False,
                is_transaction_chargable=True,
                status="S",
            ).order_by("date_created")
        elif transaction_type == "bill_payment":
            transactions = BillPayment.objects.filter(
                sender_account__bank__alias=app_code,
                is_resolved=False,
                is_transaction_chargable=True,
                status="S",
            ).order_by("date_created")
        else:
            return {}
        if transactions:
            no_of_transactions = transactions.count()
            total_amount = transactions.aggregate(value=Sum("fee")).get("value")
            start_transaction_id = transactions.first().id
            end_transaction_id = transactions.last().id

            if transaction_type in ("airtime_data", "bill_payment"):
                transactions_value = transactions.aggregate(value=Sum("amount")).get(
                    "value"
                )
                partner_bank_settlement_amount = (
                    self.get_partner_bank_settlement_percent_commission(
                        transaction_type, app_code
                    )
                    * total_amount
                )
                switch_settlement_amount = (
                    transactions.aggregate(value=Sum("amount")).get("value")
                    - total_amount
                )
                transaction_charge = get_local_inter_bank_send_money_transaction_charge(switch_settlement_amount)
                omni_channel_bill_amount = total_amount - partner_bank_settlement_amount - transaction_charge
            elif transaction_type == "send_money":
                omni_channel_bill_amount = total_amount
                transactions_value = None

            return {
                "no_of_transactions": no_of_transactions,
                "total_amount": total_amount,
                "start_transaction_id": start_transaction_id,
                "end_transaction_id": end_transaction_id,
                "transactions": transactions,
                "transaction_value": transactions_value,
                "omni_channel_bill_amount": omni_channel_bill_amount.quantize(
                    Decimal("1.00")
                ),
                "partner_bank_settlement_amount": partner_bank_settlement_amount.quantize(
                    Decimal("1.00")
                )
                if transaction_type in ("airtime_data", "bill_payment")
                else None,
                "switch_settlement_amount": switch_settlement_amount.quantize(
                    Decimal("1.00")
                )
                if transaction_type in ("airtime_data", "bill_payment")
                else None,
            }
        else:
            return {}

    def get_partner_bank_settlement_percent_commission(
        self, transaction_type, app_code
    ):
        """
        Based on partner bank aggreement, return percent commusion
        """
        try:
            fee = Fee.objects.get(
                partner_bank_config__transaction_type=transaction_type,
                partner_bank_config__partner_bank__alias=app_code,
            )
            return fee.partner_bank_percent_commission / Decimal("100.00")
        except Exception:
            return Decimal("0.00")


class Test(generics.ListAPIView):
    permission_classes = (AllowAny,)

    def list(self, request):
        all_send_money = SendMoney.objects.all()

        bill_config = Configurations.objects.get(
            transaction_type="send_money", partner_bank__alias="chmfb"
        )
        bill_ref = "**********"
        test_bill, _ = Bills.objects.get_or_create(
            partner_bank_config=bill_config,
            no_of_transactions=20,
            total_amount=Decimal("200.00"),
            bill_ref=bill_ref,
            starting_transaction_id=1,
            ending_transaction_id=20,
        )
        generate_bill_reports(all_send_money, test_bill, "success", send_email)
        # test_bill.delete()
        return Response("test", status=status.HTTP_200_OK)


class CreateBillDefinition(generics.GenericAPIView):
    
    permission_classes = [IsAdminUser]
    serializer_class = BillDefinitionSerializer
    
    def post(self, request):
        
        try:
            serializer = BillDefinitionSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            partner_bank_id = serializer.validated_data["partner_bank_id"]
            
            bank = Bank.objects.get(id=partner_bank_id)
            
            # In a bid to ensure a bank does not have two instances of the same Billing Type
            bill_type = BankBillDefinitions.objects.filter(
                partner_bank=bank, billing_type=serializer.validated_data["billing_type"]
                )
            if not bank.is_partner_bank:
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_description": "Selected Bank is not a Partner Bank",
                    "response_detail": f"{bank.name} is not a Partner Bank"
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            elif bill_type:
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_description": "Billing Type already exist for Bank",
                    "response_detail": f"`{serializer.validated_data['billing_type']}` Billing Type already exist for this Bank"
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            bill_definition = BankBillDefinitions.objects.create(
                partner_bank = bank,
                amount = serializer.validated_data["amount"],
                billing_start_date = serializer.validated_data["billing_start_date"],
                billing_type = serializer.validated_data["billing_type"],
                billing_frequency = serializer.validated_data["billing_frequency"],
                grace_days = serializer.validated_data["grace_days"],
            )
            
            data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Billing Definition Created Successfully",
                    "detail": {
                        "definition_id": bill_definition.id,
                        "bank": bill_definition.partner_bank.name,
                        "amount": bill_definition.amount,
                        "billing_start_date": bill_definition.billing_start_date,
                        "billing_type": bill_definition.billing_type,
                        "last_bill_date": bill_definition.last_bill_date,
                        "billing_frequency": bill_definition.billing_frequency,
                        "grace_days": bill_definition.grace_days,
                        "date_created": bill_definition.date_created,
                        "last_updated": bill_definition.last_updated
                    }
                }
            return Response(data, status=status.HTTP_201_CREATED)
            
        except ValidationError as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Bank",
                "response_detail": 'Selected Bank does not exist',
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Failed to Create Billing Definition",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        
class UpdateBillDefinition(generics.GenericAPIView):
   
    permission_classes = [IsAdminUser]
    serializer_class = UpdateBillDefinitionSerializer
    
    def patch(self, request, pk):
        
        try:
            
           bill_obj = BankBillDefinitions.objects.get(id=pk)
           serializer = UpdateBillDefinitionSerializer(
               instance=bill_obj, data=request.data, partial=True
               )
           
           serializer.is_valid(raise_exception=True)
           serializer.save()    
      
           data = {
               "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Billing Definition Updated Successfully",
                "detail": {
                        "definition_id": bill_obj.id,
                        "bank": bill_obj.partner_bank.name,
                        "amount": bill_obj.amount,
                        "billing_start_date": bill_obj.billing_start_date,
                        "billing_type": bill_obj.billing_type,
                        "last_bill_date": bill_obj.last_bill_date,
                        "billing_frequency": bill_obj.billing_frequency,
                        "grace_days": bill_obj.grace_days,
                        "date_created": bill_obj.date_created,
                        "last_updated": bill_obj.last_updated
                    }
           }
           return Response(data, status=status.HTTP_200_OK)
       
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Bank",
                "response_detail": 'Selected Bank does not exist',
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Billing Type already exist for Bank",
                "response_detail": f"`{serializer.validated_data['billing_type']}` Billing Type already exist for this Bank"
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Failed to Create Billing Definition",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

class GetPaymentsReference(generics.ListAPIView):
    
    permission_classes = [permissions.TokenHasReadWriteScope, IsBankAdmin]
    pagination_class = CustomPageNumberPagination
    serializer_class = GetPaymentsReferenceSerializer
    
    def get_queryset(self):
        user = self.request.auth.user
        bank = user.bankadmin.bank
        
        payment_status = self.request.query_params.get("status", None) 
        if payment_status is not None:
            if payment_status.lower() == "not-paid":
                payments = BankBillDuePayments.objects.filter(
                    billing_definition__partner_bank=bank,
                    payment_status="Not Paid"
            ).order_by("-created")
                
            elif payment_status.lower() == "paid":
                payments = BankBillDuePayments.objects.filter(
                    billing_definition__partner_bank=bank,
                    payment_status="Paid"
            ).order_by("-created")
            
            else:
                raise ValueError("Invalid query parameter value for `status`. Please pass either " \
                                  "`paid` or `not-paid` or do not pass `status` parmeter, to return all")
        
        elif payment_status is None:
            payments = BankBillDuePayments.objects.filter(
                billing_definition__partner_bank=bank,
            ).order_by("-created")
                    
        return payments
    
    def list(self, request):
        
        try:
            queryset = self.get_queryset()
            queryset_paginate = self.paginate_queryset(queryset)
            serializer = GetPaymentsReferenceSerializer(queryset_paginate, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)

            if queryset:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Payment Referenence(s)",
                    "response_detail": paginated_resp.data,
                }
            else:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "No Payment Reference Found",
                    "response_detail": paginated_resp.data,
                }
            return Response(data, status=status.HTTP_200_OK)
        except ValueError as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid data supplied",
                "response_detail": str(e)
            }
            return Response(data, status=status.HTTP_406_NOT_ACCEPTABLE)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Failed to load Payment Reference(s)",
                "response_detail": str(e)
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
       
        
class RunActivePaymentScheduler(generics.GenericAPIView):
    
    def post(self, request):
        try:
            payments = BankBillDuePayments.objects.all()
            for payment in payments:
                is_active = is_payment_inactive(payment.grace_date)
                if is_active is True:
                    payment.active = False
                    payment.save()
            
            return Response("", status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Updating Bill Reference",
                detail=f"Error Updating Bill Reference -> {e}",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
           
class RunGeneratePaymentsSchduler(generics.GenericAPIView):
    
    def post(self, request):
        
        try:
            today = timezone.now()
            bill_def = BankBillDefinitions.objects.all()
            
            for bill in bill_def:
                is_bank_shutdown = bill.partner_bank.is_active
                
                billable = is_billable(
                    today=today,
                    billing_start_date=bill.billing_start_date,
                    billing_freq=bill.billing_frequency,
                    last_bill_date=bill.last_bill_date,
                    is_shutdown=is_bank_shutdown
                )
                
                if billable is True:
                    generate_bill_due_payment(bill_def_id=bill.id)
                    bill.last_bill_date = today
                    next_bill_date = calc_next_bill_date(
                        bill_freq=bill.billing_frequency,
                        last_bill_date=bill.last_bill_date
                    )
                    bill.next_bill_date = next_bill_date
                    bill.save() 
            return Response("", status=status.HTTP_200_OK) 
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Generating Bill Reference",
                detail=f"Error Generating Bill Reference -> {e}",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
                
class RunShutDownAndEmailer(generics.GenericAPIView):
    
    def post(self, request):
        
        # WHEN A BANK IS SHUTDOWN DAYS AFTER THE GRACE DATE WILL STILL BE CALC, TRY TO FIX THIS
        
        try:
            partner_banks = Bank.objects.filter(is_partner_bank=True)
            paid_payment = BankBillDuePayments.objects.filter(
                payment_status="Paid", active=True
                )
            banks_with_active_payments = Bank.objects.filter(
                bankbilldefinitions__bankbillduepayments__active=True,
                bankbilldefinitions__bankbillduepayments__payment_status="Paid"
            )
            
            is_warning_list = []
            is_not_warning_list = []
            
            for payment in paid_payment:
                is_warning_alert = is_warning(
                    payment.payment_due_date, payment.grace_date
                    )
                if is_warning_alert is True:
                    is_warning_list.append(
                        payment.billing_definition.partner_bank
                    )
                elif is_warning_alert is False:
                    is_not_warning_list.append(
                        payment.billing_definition.partner_bank
                    )
            
            for bank_obj in is_warning_list:
                if bank_obj not in is_not_warning_list:
                    self.send_alert_email(
                        bank_obj,
                        "SHUTDOWN_WARNING_ALERTS"
                        )
                
            safe_banks = []
            shutdown_banks = []

            for bank in banks_with_active_payments:
                safe_banks.append(bank)
                
            for partner_bank in partner_banks:
                if partner_bank not in safe_banks:
                    shutdown_banks.append(partner_bank)
            
            for shutdown_bank in shutdown_banks:
                bank_active_status = shutdown_bank.is_active
                if bank_active_status is True:
                    self.send_alert_email(
                        shutdown_bank, "SHUTDOWN_NOTIFICATION"
                    )
                shutdown_bank.is_active = False
                shutdown_bank.save() 
                
            is_not_warning_list.clear()
            is_warning_list.clear()
            safe_banks.clear()
            shutdown_banks.clear()
            
            return Response("", status=status.HTTP_200_OK) 
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Emailing or Shutting Down",
                detail=f"Error Emailing or Shutting Down -> {e}",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
    def send_alert_email(obj, bank=None, message_type=None):
        
        if message_type == "SHUTDOWN_WARNING_ALERTS":
            message = "Your Billing is due for renewal. Please kindly visit this url: https://octozeep.logicshift-solutions.com/" 
            
        elif message_type == "SHUTDOWN_NOTIFICATION":
            message = "This is to notify you that your account has been shutdown and activities suspended henceforth. Kindly renew your Billing plan to return onboard." 
       
        email_args = {
            "email_from": settings.DEFAULT_FROM_EMAIL,
            "to": [bank.email],
            "subject": "Billing Status",
            "message": message,
            "bank_name": bank.name,
            "background": "FFFFFF",
            "foreground": "0C6ED1",
            "img_url": settings.OMNI_CHANNNEL_IMG_URL
        }
        send_email(email_args, "BILLING_SHUTDOWN_ALERTS")         


    # try:
    #     present_day = datetime.datetime.today()
        
    #     bill_due_dates = BillDuePayment.objects.values_list("payment_due_date", flat=True)
    #     for bill_date in bill_due_dates:
    #         # REMEMBER TO CHANGE BILLING DAYS TO DAYS NOT MINUTES
    #         if present_day >= bill_date:
    #             bill_objs = BillDuePayment.objects.filter(payment_due_date=bill_date)
    #             for bill_obj in bill_objs:
    #                 bill_obj.active = False
    #                 bill_obj.save()
    #     # CREATE HISTORY PARAMETERS AND SAVE EACH DAY TO A HISTORY TABLE
        
    #     vulnerable_bank_obj = []
    #     not_vulnerable_bank_obj = []
        
    #     active_bills = BillDuePayment.objects.filter(active=True)
    #     for active_bill in active_bills:
    #         is_warning_alert = is_warning(active_bill.billing_end_date, active_bill.payment_due_date)
    #         if is_warning_alert is True:
    #             vulnerable_bank_obj.append(active_bill.billing_definition.partner_bank)
    #         elif is_warning_alert is False:
    #             not_vulnerable_bank_obj.append(active_bill.billing_definition.partner_bank)
                
    #     for bank_obj in vulnerable_bank_obj:
    #         if bank_obj not in not_vulnerable_bank_obj:
    #             self.send_alert_email(bank_obj, "SHUTDOWN_WARNING_ALERTS")   
                
    #     unsafe_banks = set() # I am using Set data type to prevent duplicate data
    #     active_bank = Bank.objects.filter(billdefinition__billduepayment__active=True)
    #     partner_bank = Bank.objects.filter(is_partner_bank=True)
        
    #     for bank in partner_bank:
    #         if bank not in active_bank:
    #             unsafe_banks.add(bank) 
    #     for inactive_bank in unsafe_banks:
    #         bank_status = inactive_bank.is_active
    #         if bank_status is True:
    #             self.send_alert_email(inactive_bank, "SHUTDOWN_NOTIFICATION")
    #             # bank_status is to ensure we send the shutdown Email just once, subsequent loop will return False for bank_status and Email will not be sent 
    #         inactive_bank.is_active = False
    #         inactive_bank.save()
    #     unsafe_banks.clear()
        # I am clearing the the Set data type to prevent working with data from a previous day
        
    #     return Response("", status=status.HTTP_200_OK)
    # except Exception as e:
    #     data = {
    #         "response_code": settings.FAILED_RESPONSE,
    #         "response_description": "Failed to Run the Scheduler",
    #         "response_detail": str(e),
    #     }
    #     return Response(data, status=status.HTTP_400_BAD_REQUEST)
    