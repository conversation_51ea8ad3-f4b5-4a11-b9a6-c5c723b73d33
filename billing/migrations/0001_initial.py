# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bank', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BankBillDefinitions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=15)),
                ('billing_type', models.CharField(choices=[('Maintenance', 'MAINTENANCE'), ('Subscription or Support', 'SUBSCRIPTION_OR_SUPPORT')], default='Subscription or Support', max_length=30)),
                ('billing_frequency', models.CharField(choices=[('Daily', 'DAILY'), ('Weekly', 'WEEKLY'), ('Monthly', 'MONTHLY'), ('Annually', 'ANNUALLY')], default='Monthly', max_length=15)),
                ('billing_start_date', models.DateTimeField(null=True)),
                ('last_bill_date', models.DateTimeField(null=True)),
                ('next_bill_date', models.DateTimeField(null=True)),
                ('grace_days', models.PositiveIntegerField(default=0)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('partner_bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bank')),
            ],
            options={
                'verbose_name': 'Bill Definition',
                'verbose_name_plural': 'Bill Definitions',
            },
        ),
        migrations.CreateModel(
            name='Configurations',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=60, unique=True)),
                ('transaction_type', models.CharField(choices=[('send_money', 'SEND MONEY'), ('airtime_data', 'AIRTIME/DATA'), ('bill_payment', 'BILL PAYMENT'), ('dfa_send_money', 'DFA SEND MONEY'), ('i_send_money', 'INTERNATIONAL SEND MONEY')], max_length=15)),
                ('prefund_account', models.CharField(max_length=10)),
                ('destination_account', models.CharField(max_length=10)),
                ('fee_settlement_account', models.CharField(blank=True, max_length=10, null=True)),
                ('switch_settlement_account', models.CharField(blank=True, max_length=10, null=True)),
                ('partner_bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bank')),
                ('switch_settlement_bank', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='switch_settlement_bank', to='bank.bank')),
            ],
            options={
                'verbose_name': 'Configurations',
                'verbose_name_plural': 'Configurations',
            },
        ),
        migrations.CreateModel(
            name='Fee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, null=True, unique=True)),
                ('custom_charge', models.CharField(choices=[('Y', 'Yes'), ('N', 'No')], default='Y', max_length=1, verbose_name='Custom Charge (Note If Set to No, only percent commission field is required)')),
                ('percent_commission', models.DecimalField(decimal_places=2, default='0.00', max_digits=5)),
                ('partner_bank_percent_commission', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('is_bounded', models.CharField(choices=[('Y', 'Yes'), ('N', 'No')], default='Y', max_length=1)),
                ('lower_bound', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('upper_bound', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('charge', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('fee', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('partner_bank_config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing.configurations', verbose_name='Partner Bank Configuration')),
            ],
            options={
                'verbose_name': 'Fee Configuration',
                'verbose_name_plural': 'Fee Configurations',
            },
        ),
        migrations.CreateModel(
            name='Bills',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('no_of_transactions', models.PositiveBigIntegerField()),
                ('total_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('omni_channel_bill_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('switch_settlement_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('partner_bank_settlement_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('is_reconciled', models.BooleanField(default=False)),
                ('is_switch_settlement_reconciled', models.BooleanField(default=False)),
                ('is_partner_bank_settlement_reconciled', models.BooleanField(default=False)),
                ('bill_ref', models.CharField(db_index=True, max_length=25, unique=True)),
                ('starting_transaction_id', models.PositiveBigIntegerField()),
                ('ending_transaction_id', models.PositiveBigIntegerField()),
                ('reciept_pdf', models.FileField(blank=True, null=True, upload_to='reciept/')),
                ('reciept_excel', models.FileField(blank=True, null=True, upload_to='reciept/')),
                ('partner_bank_config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing.configurations', verbose_name='Partner Bank Configuration')),
            ],
            options={
                'verbose_name': 'Bill',
                'verbose_name_plural': 'Bills',
            },
        ),
        migrations.CreateModel(
            name='BankBillDuePayments',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('billing_reference', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('payment_due_date', models.DateTimeField(null=True)),
                ('grace_date', models.DateTimeField(null=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=15)),
                ('payment_status', models.CharField(choices=[('Paid', 'Paid'), ('Not Paid', 'Not Paid')], default='Not Paid', max_length=10)),
                ('active', models.BooleanField(default=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('billing_definition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing.bankbilldefinitions')),
            ],
            options={
                'verbose_name': 'Bill Due Payment',
                'verbose_name_plural': 'Bill Due Payments',
            },
        ),
        migrations.AddConstraint(
            model_name='fee',
            constraint=models.CheckConstraint(check=models.Q(('lower_bound__gte', 0)), name='lower_bound_constraint'),
        ),
        migrations.AddConstraint(
            model_name='fee',
            constraint=models.UniqueConstraint(fields=('lower_bound', 'upper_bound', 'partner_bank_config'), name='unique_bound'),
        ),
        migrations.AddConstraint(
            model_name='bankbilldefinitions',
            constraint=models.UniqueConstraint(fields=('partner_bank', 'billing_type'), name='unique_bank_billing_type_field'),
        ),
    ]
