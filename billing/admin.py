from django.contrib import admin

from .models import Configurations, Fee, Bills, BankBillDuePayments, BankBillDefinitions

# Register your models here.

@admin.register(Configurations)
class ConfigurationAdmin(admin.ModelAdmin):
    
    search_fields = ['name']
    list_filter = ['transaction_type'] #, 'partner_bank'
    list_display = ('name', 'transaction_type', 'partner_bank', 'prefund_account', 'destination_account')


@admin.register(Fee)
class FeeAdmin(admin.ModelAdmin):
    
    search_fields = ['name', 'partner_bank_config__partner_bank__name']
    list_filter = ['is_bounded']
    list_display = ['name', 'percent_commission', 'is_bounded', 'lower_bound', 'upper_bound', 'charge', 'fee']
    readonly_fields = ('fee',)

@admin.register(Bills)
class BillAdmin(admin.ModelAdmin):

    search_fields = ["bill_ref"]
    list_filter = ["partner_bank_config", "is_reconciled"]
    list_display = [
        "bill_ref",
        "partner_bank_config",
        "no_of_transactions",
        "total_amount",
        "omni_channel_bill_amount",
        "date_created",
        "is_reconciled",
    ]
    # readonly_fields = (
    #     "partner_bank_config",
    #     "no_of_transactions",
    #     "total_amount",
    #     "date_created",
    #     "is_reconciled",
    #     "bill_ref",
    #     "starting_transaction_id",
    #     "ending_transaction_id",
    #     "reciept_pdf",
    #     "reciept_excel",
    # )

    def has_change_permission(self, request, obj=None) -> bool:
        return False
    
    
@admin.register(BankBillDefinitions)
class BankBillDefinitionAdmin(admin.ModelAdmin):

    # search_fields = ["bill_ref"]
    list_filter = ["partner_bank"]
    readonly_fields = ("last_bill_date", "next_bill_date")
    list_display = [
        "partner_bank",
        "billing_frequency",
        "billing_start_date",
        "amount",
        "last_bill_date",
        "grace_days",
        "date_created",
        "last_updated",
    ]
    
    
@admin.register(BankBillDuePayments)
class BankBillDuePaymentAdmin(admin.ModelAdmin):
    
    list_per_page = 20
    list_display = [
        "billing_definition",
        "billing_reference",
        "payment_due_date",
        "grace_date",
        "amount",
        "payment_status",
        "active",
        "created",
    ]
    
    def has_change_permission(self, request, obj=None) -> bool:
        return False
    
    
