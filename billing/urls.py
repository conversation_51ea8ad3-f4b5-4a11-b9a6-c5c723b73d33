from django.urls import path

from .views import (
    TransactionsBilling, CreateBillDefinition, 
    UpdateBillDefinition, GetPaymentsReference, 
    RunActivePaymentScheduler, RunGeneratePaymentsSchduler,
    RunShutDownAndEmailer
    )

urlpatterns = [
    path(
        'bill_transactions/<str:transaction_type>/<str:app_code>/', 
        TransactionsBilling.as_view(), name='bill_transactions'
        ),
    path(
        "bill_definition/", 
        CreateBillDefinition.as_view(), 
        name="billing_definition"
        ),
    path(
        "update_bill_definition/<int:pk>/", 
        UpdateBillDefinition.as_view(), 
        name="Update_bill_definition"
        ),
    path(
        "get_bank_payment_references",
        GetPaymentsReference.as_view(),
        name="get_bank_payment_references"
    ),
    path(
        "payment_active_scheduler/", 
        RunActivePaymentScheduler.as_view(), 
        name="payment_active_scheduler"
        ),
    path(
        "generate_payment_scheduler/",
         RunGeneratePaymentsSchduler.as_view(),
         name="generate_payment_scheduler"
         ),
    path(
        "shutdown_and_email_scheduler/", 
        RunShutDownAndEmailer.as_view(),
        name="shutdown_and_email_scheduler"
    ),
]