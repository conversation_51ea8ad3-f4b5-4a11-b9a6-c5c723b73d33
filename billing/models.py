from decimal import Decimal
import uuid
import datetime

from django.db import models
from django.db.models.query_utils import Q
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


from bank.models import Bank

# Create your models here.

DAILY ="Daily"
WEEKLY = "Weekly"
MONTHLY = "Monthly"
ANNUALLY = "Annually"

MAINTENANCE = "Maintenance"
SUBSCRIPTION_OR_SUPPORT = "Subscription or Support"

FREQUENCY = (
    (DAILY, "DAILY"), 
    (WEEKLY, "WEEKLY"), 
    (MONTHLY, "MONTHLY"), 
    (ANNUALLY, "ANNUALLY")
    )
BILLING_TYPE = (
    (MAIN<PERSON><PERSON>NCE, "MAINTENANCE"), 
    (SUBSCRIPTION_OR_SUPPORT, "SUBSCRIPTION_OR_SUPPORT")
    )

class Configurations(models.Model):

    SEND_MONEY = "send_money"
    AIRTIME_DATA = "airtime_data"
    BILL_PAYMENT = "bill_payment"
    INTERNATIONALS_SEND_MONEY = "i_send_money"
    DFA_SEND_MONEY = 'dfa_send_money'

    TRANSACTION_TYPE_CHOICES = (
        (SEND_MONEY, "SEND MONEY"),
        (AIRTIME_DATA, "AIRTIME/DATA"),
        (BILL_PAYMENT, "BILL PAYMENT"),
        (DFA_SEND_MONEY, "DFA SEND MONEY"),
        (INTERNATIONALS_SEND_MONEY, "INTERNATIONAL SEND MONEY"),
    )

    name = models.CharField(max_length=60, unique=True)
    transaction_type = models.CharField(max_length=15, choices=TRANSACTION_TYPE_CHOICES)
    partner_bank = models.ForeignKey(Bank, on_delete=models.CASCADE)
    prefund_account = models.CharField(max_length=10)
    destination_account = models.CharField(max_length=10)
    fee_settlement_account = models.CharField(max_length=10, blank=True, null=True)
    switch_settlement_account = models.CharField(max_length=10, blank=True, null=True)
    switch_settlement_bank = models.ForeignKey(
        Bank,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name="switch_settlement_bank",
    )

    def __str__(self) -> str:
        return self.name

    def clean_fields(self, exclude=None) -> None:
        super().clean_fields(exclude=exclude)
        if not self.partner_bank.is_partner_bank:
            # chosen bank must be a partner bank
            raise ValidationError(
                {
                    "partner_bank": _(
                        "Chosen Bank Must be a Partner Bank. Please Confirm from the Bank Module"
                    )
                }
            )
        if self.transaction_type != "send_money":
            # meaning for airtime_data and bill payment, fee settlement account, switch settlement account and switch settlement bank should not be not be empty
            dependencies = {
                "fee_settlement_account": self.fee_settlement_account,
                "switch_settlement_account": self.switch_settlement_account,
                "switch_settlement_bank": self.switch_settlement_bank,
            }
            for marker, field in dependencies.items():
                if not field:
                    raise ValidationError(
                        {
                            marker: _(
                                f"{marker} must exist for transaction type {self.transaction_type}"
                            )
                        }
                    )

    class Meta:
        verbose_name = "Configurations"
        verbose_name_plural = "Configurations"


class Fee(models.Model):

    YES = "Y"
    NO = "N"

    YES_NO_CHOICES = ((YES, "Yes"), (NO, "No"))

    name = models.CharField(max_length=100, unique=True, null=True)
    custom_charge = models.CharField(
        max_length=1,
        choices=YES_NO_CHOICES,
        default=YES,
        verbose_name="Custom Charge (Note If Set to No, only percent commission field is required)",
    )
    percent_commission = models.DecimalField(
        default="0.00", max_digits=5, decimal_places=2
    )
    partner_bank_config = models.ForeignKey(
        Configurations,
        on_delete=models.CASCADE,
        verbose_name="Partner Bank Configuration",
    )
    partner_bank_percent_commission = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    is_bounded = models.CharField(max_length=1, choices=YES_NO_CHOICES, default=YES)
    lower_bound = models.DecimalField(
        max_digits=12, decimal_places=2, blank=True, null=True
    )
    upper_bound = models.DecimalField(
        max_digits=12, decimal_places=2, blank=True, null=True
    )
    charge = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    fee = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)

    def clean_fields(self, exclude=None) -> None:
        super().clean_fields(exclude=exclude)

        if self.percent_commission < 0 or self.percent_commission > 100:
            raise ValidationError(
                {
                    "percent_commission": _(
                        "Invalid Percent. Make sure percent is between 0 and 100"
                    )
                }
            )
        if self.partner_bank_percent_commission and (
            self.partner_bank_percent_commission < 0
            or self.partner_bank_percent_commission > 100
        ):
            raise ValidationError(
                {
                    "partner_bank_percent_commission": _(
                        "Invalid Percent. Make sure percent is between 0 and 100"
                    )
                }
            )

        if self.partner_bank_config.transaction_type in (
            "airtime_data",
            "bill_payment",
        ):
            if not self.partner_bank_percent_commission:
                raise ValidationError(
                    {
                        "partner_bank_percent_commission": _(
                            f"Partner Bank Commission should exist for {self.partner_bank_config.name}"
                        )
                    }
                )

        if self.custom_charge == "Y":
            if not self.lower_bound:
                raise ValidationError(
                    {"lower_bound": _("Lower Bound must exist for Custom Charge")}
                )
        else:
            # custom charge dependent field to None and return
            self.lower_bound = None
            self.upper_bound = None
            self.charge = None
            self.fee = None
            self.is_bounded = "N"
            return
        if self.is_bounded == "Y":
            if not self.lower_bound:
                raise ValidationError(
                    {
                        "lower_bound": _(
                            "Lower Bound must exist for bounded fee configuration"
                        )
                    }
                )
            if not self.upper_bound:
                raise ValidationError(
                    {
                        "upper_bound": _(
                            "Upper Bound must exist for bounded fee configuration"
                        )
                    }
                )
            elif self.upper_bound <= self.lower_bound:
                raise ValidationError(
                    {
                        "upper_bound": _(
                            "Upper Bound should be greater than the lower bound"
                        )
                    }
                )
        if self.partner_bank_config.transaction_type == "send_money":
            self.partner_bank_percent_commission = None
            return

    def __str__(self) -> str:
        return self.name

    class Meta:
        verbose_name = "Fee Configuration"
        verbose_name_plural = "Fee Configurations"
        constraints = [
            models.CheckConstraint(
                check=Q(lower_bound__gte=0), name="lower_bound_constraint"
            ),
            models.UniqueConstraint(
                fields=["lower_bound", "upper_bound", "partner_bank_config"],
                name="unique_bound",
            ),
        ]

    def save(self, *args, **kwargs):
        if self.custom_charge == "Y":
            self.fee = self.get_fee()
        super().save(*args, **kwargs)

    def get_fee(self):
        if self.percent_commission and self.charge:
            return (self.percent_commission / 100) * self.charge
        else:
            return Decimal("0.00")


class Bills(models.Model):

    partner_bank_config = models.ForeignKey(
        Configurations,
        on_delete=models.CASCADE,
        verbose_name="Partner Bank Configuration",
    )
    no_of_transactions = models.PositiveBigIntegerField()
    total_amount = models.DecimalField(
        max_digits=15, decimal_places=2, blank=True, null=True
    )
    omni_channel_bill_amount = models.DecimalField(
        max_digits=15, decimal_places=2, blank=True, null=True
    )
    switch_settlement_amount = models.DecimalField(
        max_digits=15, decimal_places=2, blank=True, null=True
    )
    partner_bank_settlement_amount = models.DecimalField(
        max_digits=15, decimal_places=2, blank=True, null=True
    )
    date_created = models.DateTimeField(auto_now_add=True)
    is_reconciled = models.BooleanField(default=False)
    is_switch_settlement_reconciled = models.BooleanField(default=False)
    is_partner_bank_settlement_reconciled = models.BooleanField(default=False)
    bill_ref = models.CharField(max_length=25, unique=True, db_index=True)
    starting_transaction_id = models.PositiveBigIntegerField()
    ending_transaction_id = models.PositiveBigIntegerField()
    reciept_pdf = models.FileField(upload_to="reciept/", blank=True, null=True)
    reciept_excel = models.FileField(upload_to="reciept/", blank=True, null=True)

    class Meta:
        verbose_name = "Bill"
        verbose_name_plural = "Bills"

class BankBillDefinitions(models.Model):
    
    partner_bank = models.ForeignKey(Bank, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=15, decimal_places=2, default='0.00')
    billing_type = models.CharField(max_length=30, choices=BILLING_TYPE, default=SUBSCRIPTION_OR_SUPPORT)
    billing_frequency = models.CharField(max_length=15, choices=FREQUENCY, default=MONTHLY)
    billing_start_date = models.DateTimeField(null=True) # make the default as the present date and remove null=True
    last_bill_date = models.DateTimeField(null=True) 
    next_bill_date = models.DateTimeField(null=True)
    grace_days = models.PositiveIntegerField(default=0)
    last_updated = models.DateTimeField(auto_now=True)
    date_created = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.partner_bank} | {self.id}"
    
    class Meta:
        verbose_name = "Bill Definition"
        verbose_name_plural = "Bill Definitions"
        constraints = [
            models.UniqueConstraint(fields=['partner_bank', 'billing_type'], name='unique_bank_billing_type_field')
        ]
    
    # auto_now takes a timestamp anytime we save this item
    # auto_now_add takes a timestamp only once when we first save 

class BankBillDuePayments(models.Model):
    NOT_PAID = "Not Paid"
    PAID = "Paid"

    PAYMENT_STATUS_CHOICES = (
        (PAID, "Paid"),
        (NOT_PAID, "Not Paid"),
    )
    
    billing_definition = models.ForeignKey(BankBillDefinitions, on_delete=models.CASCADE)
    billing_reference = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    payment_due_date = models.DateTimeField(null=True)
    grace_date = models.DateTimeField(null=True)
    amount = models.DecimalField(
        max_digits=15, decimal_places=2, default='0.00'
    )
    payment_status = models.CharField(
        max_length=10,
        choices=PAYMENT_STATUS_CHOICES,
        default=NOT_PAID)
    active = models.BooleanField(default=True)
    created = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = "Bill Due Payment"
        verbose_name_plural = "Bill Due Payments"
        
    def __str__(self):
        return f"{self.billing_definition.partner_bank} | {self.id}"
    

