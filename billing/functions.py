import datetime

from django.utils import timezone

from .models import BankBillDuePayments, BankBillDefinitions


def duration_getter(bill_freq):
    if bill_freq == "Daily":
        bill_duration = 1
    elif bill_freq == "Weekly":
        bill_duration = 7
    elif bill_freq == "Monthly":
        bill_duration = 1
    elif bill_freq == "Annually":
        bill_duration = 365

    return bill_duration

def calc_grace_date(grace_days, billing_freq):
    present_date = timezone.now()
    running_days = datetime.timedelta(minutes=duration_getter(billing_freq)) \
                   + datetime.timedelta(minutes=grace_days)
    due_date = present_date + running_days
    return due_date
    
def calc_payment_due_date(bill_freq):
    present_date = timezone.now()
    payment_due = datetime.timedelta(minutes=duration_getter(bill_freq)) \
                  + present_date
    return payment_due

def calc_next_bill_date(bill_freq, last_bill_date):
    next_bill_date = last_bill_date \
            + datetime.timedelta(minutes=duration_getter(bill_freq))
    return next_bill_date
    
    
def is_billed(today, last_bill_date, bill_date, billing_start_date):
    """To curb generating multiple payment due when the scheduler runs more than once a day."""
    if last_bill_date is None:
       if today >= billing_start_date and today <= bill_date:
           return True
    elif today >= last_bill_date and today <= bill_date:
        return True 
    return False

def is_billable(today, billing_start_date, billing_freq, last_bill_date, is_shutdown):
    if last_bill_date is None:
        bill_date = billing_start_date + \
                    datetime.timedelta(minutes=duration_getter(billing_freq))
    else:
        bill_date = last_bill_date + \
                    datetime.timedelta(minutes=duration_getter(billing_freq))

    is_billed_already = is_billed(
        today=today,
        last_bill_date=last_bill_date,
        bill_date=bill_date,
        billing_start_date=billing_start_date
    )
    
    if today >= bill_date and is_billed_already is False and is_shutdown is True:
        return True
    return False

def is_payment_inactive(grace_date):
    """
    Returns True when the payment grace date is exceeded. 
    """
    present_date = timezone.now()
    if present_date >= grace_date:
        return True
    return False


def is_warning(payment_due, grace_date):
    """To determine the period within which a Bank is due to start receiving warning Emails, before shutdown"""
    present_date = timezone.now()
    if present_date >= payment_due and present_date <= grace_date:
        return True
    return False

def generate_bill_due_payment(bill_def_id):
    
    bill_definition = BankBillDefinitions.objects.get(id=bill_def_id)
    
    bill_due_payment = BankBillDuePayments.objects.create(
        billing_definition = bill_definition,
        payment_due_date = calc_payment_due_date(
            bill_freq=bill_definition.billing_frequency
        ),
        grace_date = calc_grace_date(
            billing_freq=bill_definition.billing_frequency,
            grace_days=bill_definition.grace_days
        ),
        amount = bill_definition.amount
        
    )
    return bill_due_payment