from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from .models import (
    BankBillDefinitions,
    BILLING_TYPE, FREQUENCY
)
from .functions import duration_getter


class BillDefinitionSerializer(serializers.Serializer):
    
    partner_bank_id = serializers.CharField(max_length=3, required=True)
    billing_start_date = serializers.DateField("%Y-%m-%d")
    amount = serializers.DecimalField(max_digits=15, decimal_places=2, default='0.00')
    billing_type = serializers.ChoiceField(choices=BILLING_TYPE, required=True)
    billing_frequency = serializers.ChoiceField(choices=FREQUENCY, required=True)
    grace_days = serializers.IntegerField(required=True)
    
    class Meta:
        model = BankBillDefinitions
        fields = ["billing_type", "amount", "partner_bank_id", "billing_start_date", "billing_frequency", "grace_days"]
        
    def create(self, validated_data):
        return BankBillDefinitions(**validated_data)


class UpdateBillDefinitionSerializer(serializers.Serializer):
    billing_start_date = serializers.DateField("%Y-%m-%d")
    amount = serializers.DecimalField(max_digits=15, decimal_places=2, default='0.00')
    billing_type = serializers.ChoiceField(choices=BILLING_TYPE)
    billing_frequency = serializers.ChoiceField(choices=FREQUENCY)
    grace_days = serializers.IntegerField()
    
    class Meta:
        model = BankBillDefinitions
        fields = ["billing_type", "amount", "billing_start_date", "billing_frequency", "grace_days"]
        
    def update(self, instance, validated_data):
        instance.billing_type = validated_data.get("billing_type", instance.billing_type)
        instance.amount = validated_data.get("amount", instance.amount)
        instance.billing_start_date = validated_data.get("billing_start_date", instance.billing_start_date)
        instance.billing_frequency = validated_data.get("billing_frequency", instance.billing_frequency)
        instance.grace_days = validated_data.get("grace_days", instance.grace_days)
        instance.save()
        return instance    
    
class GetPaymentsReferenceSerializer(serializers.Serializer):
    
    bill_duration = serializers.SerializerMethodField("get_bill_duration")
    bill_type = serializers.SerializerMethodField("get_bill_type")
    billing_reference = serializers.SerializerMethodField("get_billing_reference")
    payment_due_date = serializers.SerializerMethodField("get_payment_due_date")     
    grace_date = serializers.SerializerMethodField("get_grace_date")
    amount = serializers.SerializerMethodField("get_amount") 
    payment_status = serializers.SerializerMethodField("get_payment_status")
    active = serializers.SerializerMethodField("get_active")
    created = serializers.SerializerMethodField("get_created")
    
    class Meta:
        fields = [
            "bill_type",
            "bill_duration"
            "billing_reference",
            "payment_due_date",
            "grace_date",
            "amount",
            "payment_status",
            "active",
            "created"
        ]
        
    def get_bill_type(self, obj):
        return obj.billing_definition.billing_type
    
    def get_bill_duration(self, obj):
        freq =  obj.billing_definition.billing_frequency
        duration = duration_getter(freq)
        return f"{freq.upper()}: {duration} Days"
    
    def get_billing_reference(self, obj):
        return obj.billing_reference
    
    def get_payment_due_date(self, obj):
        return obj.payment_due_date
    
    def get_grace_date(self, obj):
        return obj.grace_date
    
    def get_amount(self, obj):
        return obj.amount
    
    def get_payment_status(self, obj):
        return obj.payment_status
    
    def get_active(self, obj):
        return obj.active
    
    def get_created(self, obj):
        return obj.created
    
