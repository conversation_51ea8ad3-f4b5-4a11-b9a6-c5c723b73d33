# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.db import migrations, models
import django.db.models.deletion
import django_countries.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AirtimeBiller',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('biller_name', models.CharField(max_length=200)),
                ('biller_code', models.CharField(max_length=20, unique=True)),
                ('network_code', models.CharField(max_length=20)),
                ('payment_code', models.CharField(blank=True, max_length=7, null=True)),
                ('type', models.CharField(choices=[('airtime', 'Airtime'), ('data', 'Data')], default='airtime', max_length=8)),
                ('logo_img', models.ImageField(blank=True, null=True, upload_to='airtime_biller_logo')),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
            ],
            options={
                'verbose_name': 'Airtime-Data Biller',
                'verbose_name_plural': 'Airtime-Data Billers',
            },
        ),
        migrations.CreateModel(
            name='BillerCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.CharField(max_length=250)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
                ('apply_fee', models.IntegerField(choices=[(1, 'YES (Fee will be charged at transaction time)'), (0, 'NO (Fee will not be charged at transaction time)')], default=0)),
                ('country', django_countries.fields.CountryField(default='NG', max_length=2)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
            ],
            options={
                'verbose_name': 'Biller Category',
                'verbose_name_plural': 'Biller Categories',
            },
        ),
        migrations.CreateModel(
            name='Biller',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('biller_name', models.CharField(max_length=200)),
                ('biller_code', models.CharField(max_length=20, unique=True)),
                ('currency_code', models.CharField(max_length=200)),
                ('customer_field_1', models.CharField(blank=True, max_length=200, null=True)),
                ('customer_field_2', models.CharField(blank=True, max_length=200, null=True)),
                ('country', django_countries.fields.CountryField(default='NG', max_length=2)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
                ('biller_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bill_payment.billercategory')),
            ],
            options={
                'verbose_name': 'Biller Item',
                'verbose_name_plural': 'Biller Items',
            },
        ),
    ]
