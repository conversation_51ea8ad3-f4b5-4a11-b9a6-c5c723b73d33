from rest_framework import serializers

from bill_payment.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, AirtimeBiller


class BillerCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = BillerCategory
        fields = '__all__'


class BillerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Biller
        fields = '__all__'


class AirtimeBillerSerializer(serializers.ModelSerializer):

    logo_base_64 = serializers.SerializerMethodField('get_logo_base_64')
    logo_url = serializers.SerializerMethodField('get_logo_url')

    class Meta:
        model = AirtimeBiller
        fields = ['id', 'biller_name', 'biller_code', 'network_code', 'type', 'payment_code', 'logo_url', 'logo_base_64', 'date_created', 'status']

    def get_logo_base_64(self, obj):
        return obj.get_logo_base64()
    
    def get_logo_url(self, obj):
        return obj.logo_img.url