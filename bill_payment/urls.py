from django.urls import path, include
from rest_framework import routers
from bill_payment import views
from bill_payment.views import AirtimeDataBillerByType, BillerByCategory, BillerCategoryView, BillerView, BillerPaymentItemsView, \
    BillCustomerValidationView, BillPaymentAdviceView

router = routers.DefaultRouter()
router.register(r'biller_category', views.BillerCategoryViewSet)
router.register(r'biller_item', views.BillerViewSet)
router.register(r'airtime_data_biller', views.AirtimeDataBillerViewset)

urlpatterns = [
    path('', include(router.urls)),
    path('biller_item_by_category_code/<str:code>/', BillerByCategory.as_view(), name='biller_by_category'),
    path('airtime_data_biller_by_type/<str:type>/', AirtimeDataBillerByType.as_view(), name='airtime_data_biller_by_type'),
    # path("biller_categories/", BillerCategoryView.as_view(), name="biller_categories"),
    # path("billers/",  BillerView.as_view(), name="all_billers"),
    # path("airtime_billers/", AirtimeBillerView.as_view(), name="airtime_billers"),
    # path("billers/category_id/<int:category_id>/", BillerByCategoryIDView.as_view(), name="billers_by_category"),
    # path("biller_payment_items/biller_code/<int:biller_code>/", BillerPaymentItemsView.as_view(), name="biller_payment_items"),
    # path("customer_validation", BillCustomerValidationView.as_view(), name="biller_customer_validation"),
    # path("bill_payment_advice", BillPaymentAdviceView.as_view(), name="bill_payment_advice"),
]
