from django.contrib import admin


# Register your models here.
from bill_payment.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, AirtimeBiller


class BillerCategoryAdmin(admin.ModelAdmin):
    search_fields = ['name', 'apply_fee', 'date_created']
    list_filter = ['name', 'apply_fee', 'date_created']
    list_display = ('name',  'apply_fee', 'status', 'date_created')


class BillerAdmin(admin.ModelAdmin):
    search_fields = ['biller_category', 'biller_name', 'biller_code', 'date_created']
    list_filter = ['biller_category']
    list_display = ('biller_category', 'biller_name', 'biller_code',  'status', 'date_created')


class AirtimeBillerAdmin(admin.ModelAdmin):
    search_fields = [ 'biller_name', 'biller_code', 'date_created']
    list_filter = ['network_code', 'type']
    list_display = ( 'biller_name', 'biller_code', 'network_code', 'type', 'status', 'date_created')


admin.site.register(BillerCategory, BillerCategoryAdmin)
admin.site.register(AirtimeBiller, AirtimeBillerAdmin)
admin.site.register(<PERSON><PERSON>, BillerAdmin)