import uuid

from django.shortcuts import render
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status, generics
from rest_framework.permissions import AllowAny
from oauth2_provider.contrib.rest_framework import permissions
from rest_framework import viewsets
from django.core.exceptions import ObjectDoesNotExist

# Create your views here.
from bill_payment.models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, AirtimeBiller
from bill_payment.serializers import BillerCategorySerializer, BillerSerializer, AirtimeBillerSerializer
from common import services
from common.models import BillAdviceClass
from common.functions import set_response_data
# from logic_omnichannel import settings
from django.conf import settings


class BillerCategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows biller category to be viewed or edited.
    """
    queryset = BillerCategory.objects.all().order_by('-date_created')
    serializer_class = BillerCategorySerializer
    permission_classes_by_action = {"retrieve": [AllowAny], "list": [AllowAny]}

    def get_permissions(self):
        try:
            # return permission_classes depending on `action`
            return [
                permission()
                for permission in self.permission_classes_by_action[self.action]
            ]
        except KeyError:
            # action is not set return default permission_classes
            return [permission() for permission in self.permission_classes]

    def list(self, request, *args, **kwargs):

        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            resp = self.get_paginated_response(serializer.data)
        else:
            resp = self.get_serializer(queryset, many=True)

        data = set_response_data(
            code=settings.SUCCESS_RESPONSE,
            description='Biller Category List',
            detail=resp.data
        )
        
        return Response(data, status=status.HTTP_200_OK)


class BillerViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows biller to be viewed or edited.
    """
    queryset = Biller.objects.all().order_by('-date_created')
    serializer_class = BillerSerializer
    permission_classes_by_action = {"retrieve": [AllowAny], "list": [AllowAny]}

    def get_permissions(self):
        try:
            # return permission_classes depending on `action`
            return [
                permission()
                for permission in self.permission_classes_by_action[self.action]
            ]
        except KeyError:
            # action is not set return default permission_classes
            return [permission() for permission in self.permission_classes]

    def list(self, request, *args, **kwargs):

        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            resp = self.get_paginated_response(serializer.data)
        else:
            resp = self.get_serializer(queryset, many=True)

        data = set_response_data(
            code=settings.SUCCESS_RESPONSE,
            description='Biller List',
            detail=resp.data
        )
        
        return Response(data, status=status.HTTP_200_OK)
    
    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)

            resp = {**serializer.data}

            # get package option for chosen bill provider
            package_options = services.get_package_options(instance.biller_code)
            if package_options.get('response_code') == settings.SUCCESS_RESPONSE:
                package_options = package_options['response_detail']['package_options']
                resp['package_options'] = package_options
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=package_options.get('response_description', 'Unable to Retrieve Package Options'),
                    detail=package_options.get('response_detail', 'Unable to Retrieve Package Options')
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Biller Object Retrieved Successfully',
                detail=resp
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Unable To Retrieve Biller Object',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class AirtimeDataBillerViewset(viewsets.ModelViewSet):
    
    """
    API endpoint that allows Airtime Biller to be viewed or edited.
    """
    queryset = AirtimeBiller.objects.all().order_by('-date_created')
    serializer_class = AirtimeBillerSerializer
    permission_classes_by_action = {"retrieve": [AllowAny], "list": [AllowAny]}

    def get_permissions(self):
        try:
            # return permission_classes depending on `action`
            return [
                permission()
                for permission in self.permission_classes_by_action[self.action]
            ]
        except KeyError:
            # action is not set return default permission_classes
            return [permission() for permission in self.permission_classes]

    def list(self, request, *args, **kwargs):

        queryset = self.filter_queryset(self.get_queryset())

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            resp = self.get_paginated_response(serializer.data)
        else:
            resp = self.get_serializer(queryset, many=True)

        data = set_response_data(
            code=settings.SUCCESS_RESPONSE,
            description='Airtime Biller List',
            detail=resp.data
        )
        
        return Response(data, status=status.HTTP_200_OK)

    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)

            resp = {**serializer.data}

            if instance.type == 'data':
                # data biller usually have different package option so we call for them and add it to the response
                package_options = services.get_package_options(instance.biller_code)
                if package_options.get('response_code') == settings.SUCCESS_RESPONSE:
                    package_options = package_options['response_detail']['package_options']
                    resp['package_options'] = package_options
                else:
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description=package_options.get('response_description', 'Unable to Retrieve Package Options'),
                        detail=package_options.get('response_detail', 'Unable to Retrieve Package Options')
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Airtime-Data Biller Object Retrieved Successfully',
                detail=resp
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Unable To Retrieve Airtime-Data Biller Object',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class AirtimeDataBillerByType(generics.ListAPIView):

    serializer_class = AirtimeBillerSerializer
    permission_classes = [AllowAny]

    def get_queryset(self, type):
        queryset = AirtimeBiller.objects.filter(type=type).order_by('network_code')
        return queryset

    def list(self, request, type):
        
        '''
            Try to get all the billers of a certain by most likely either for airtime or for data
            and return the serialized version as response
        '''
        try:

            queryset = self.filter_queryset(self.get_queryset(type))

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                resp = self.get_paginated_response(serializer.data)
            else:
                resp = self.get_serializer(queryset, many=True)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Airtime Biller List',
                detail=resp.data
            )
            
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Unable to get Airtime Data Biller',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)



class BillerCategoryView(APIView):
    # Get an instance of a logger
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            record = BillerCategory.objects.filter().all()
            if not record:
                data = {'response_code': '01', 'response_description': 'No record found'}
            else:
                data_serializer = BillerCategorySerializer(record, many=True).data
                data = {'response_code': '00', 'record': data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class BillerView(APIView):
    # Get an instance of a logger
    permission_classes = [AllowAny]

    def get(self, request):
        try:
            record = Biller.objects.filter().all()
            if not record:
                data = {'response_code': '01', 'response_description': 'No record found'}
            else:
                data_serializer = BillerSerializer(record, many=True).data
                data = {'response_code': '00', 'record': data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class BillerByCategory(generics.ListAPIView):
    # Get an instance of a logger
    permission_classes = [AllowAny]
    serializer_class = BillerSerializer

    def get_queryset(self, code):
        return Biller.objects.filter(biller_category__code=code)

    def list(self, request, code):
        queryset = self.filter_queryset(self.get_queryset(code))

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            resp = self.get_paginated_response(serializer.data)
        else:
            resp = self.get_serializer(queryset, many=True)

        data = set_response_data(
            code=settings.SUCCESS_RESPONSE,
            description='Biller List',
            detail=resp.data
        )
        return Response(data, status=status.HTTP_200_OK)




class BillerPaymentItemsView(APIView):
    # Get an instance of a logger
    permission_classes = [AllowAny]

    def get(self, request, biller_code):
        try:
            response = services.bill_payment_items(biller_code)
            return Response(response, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class BillCustomerValidationView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        customer_code = request.data.get('customer_code')
        vendor_code = request.data.get('biller_code')
        payment_code = request.data.get('payment_code')
        try:
            response = services.bill_customer_validation(customer_code, vendor_code, payment_code)
            return Response(response, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'Error connecting to switch'}
            return Response(data, status=status.HTTP_200_OK)


class BillPaymentAdviceView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        try:
            user_ref = request.data.get('user_ref')
            data = {
                'response_code': '00',
                'response_description': 'success',
                'do_tsq': False,
                'token': '132435',
                'transaction_reference': uuid.uuid4(),
                'user_ref': user_ref
            }
            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'Error connecting to switch'}
            return Response(data, status=status.HTTP_200_OK)


