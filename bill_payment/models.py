import base64
from django.core.exceptions import ValidationError

from django.db import models


# Create your models here.
from django_countries.fields import CountryField


class BillerCategory(models.Model):
    class Meta:
        verbose_name = "Biller Category"
        verbose_name_plural = "Biller Categories"

    def __str__(self):
        return self.name

    ACTIVE = 'A'
    INACTIVE = 'I'
    YES = 1
    NO = 0
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )
    YES_NO_CHOICES = (
        (YES, 'YES (Fee will be charged at transaction time)'),
        (NO, 'NO (Fee will not be charged at transaction time)'),
    )
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=20, unique=True)
    description = models.CharField(max_length=250)
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)
    apply_fee = models.IntegerField(
        choices=YES_NO_CHOICES,
        default=NO,
    )
    country = CountryField(
        default='NG',
    )
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )


class Biller(models.Model):
    class Meta:
        verbose_name = "Biller Item"
        verbose_name_plural = "Biller Items"

    def __str__(self):
        return self.biller_name

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )
    biller_category = models.ForeignKey(BillerCategory, on_delete=models.CASCADE)
    biller_name = models.CharField(max_length=200)
    biller_code = models.CharField(max_length=20, unique=True)
    currency_code = models.CharField(max_length=200)
    customer_field_1 = models.CharField(max_length=200, blank=True, null=True)
    customer_field_2 = models.CharField(max_length=200, blank=True, null=True)
    country = CountryField(
        default='NG',
    )
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )

class AirtimeBiller(models.Model):
    class Meta:
        verbose_name = "Airtime-Data Biller"
        verbose_name_plural = "Airtime-Data Billers"

    def __str__(self):
        return self.biller_name

    ACTIVE = 'A'
    INACTIVE = 'I'
    AIRTIME = 'airtime'
    DATA = 'data'

    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )
    TYPE_CHOICES = (
        (AIRTIME, 'Airtime'),
        (DATA, 'Data'),
    )

    biller_name = models.CharField(max_length=200)
    biller_code = models.CharField(max_length=20, unique=True)
    network_code = models.CharField(max_length=20)
    payment_code = models.CharField(max_length=7, blank=True, null=True)
    type = models.CharField(
        max_length=8,
        choices=TYPE_CHOICES,
        default=AIRTIME
    )
    logo_img = models.ImageField(upload_to='airtime_biller_logo', blank=True, null=True)
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )

    def clean_fields(self, exclude=None) -> None:
        super().clean_fields(exclude=exclude)
        if self.type == 'airtime' and not self.payment_code:
            raise ValidationError({
                'payment_code': 'payment code should exist for airtime biller object'
            })

    def get_logo_base64(self):
        '''
            Retieves the Image logo file and converts it to base 64 and returns it
        '''
        if self.logo_img is not None:
            with open(self.logo_img.path, 'rb') as img_file:
                img_encode = base64.b64encode(img_file.read())
                img_encode = str(img_encode, 'utf-8')
            return img_encode
        return None