from django.contrib import admin

from .models import Countries, CurrencyRate, MFSNetworkPartners, MobileWalletNetworks


# Register your models here.
@admin.register(Countries)
class CountriesAdmin(admin.ModelAdmin):

    list_display = ["name", "country_code", "currency_code"]
    search_fields = ("name",)


@admin.register(MFSNetworkPartners)
class NetworkPartnersAdmin(admin.ModelAdmin):

    list_display = ["name", "code"]
    search_fields = ("name",)


@admin.register(MobileWalletNetworks)
class MobileWalletNetworkAdmin(admin.ModelAdmin):

    list_display = ["mfs_network_partner", "country"]
    search_fields = (
        "mfs_network_partner__name",
        "country__name",
    )


@admin.register(CurrencyRate)
class CurrencyRateAdmin(admin.ModelAdmin):

    list_display = [
        "base_currency_code",
        "source_currency_code",
        "source_conversion_rate",
        "omni_conversion_rate",
    ]
