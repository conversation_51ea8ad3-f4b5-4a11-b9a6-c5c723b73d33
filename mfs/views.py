from bank.models import Bank
from decimal import Decimal

from rest_framework import generics, status
from rest_framework.response import Response
from oauth2_provider.contrib.rest_framework import permissions

from .serializers import CountrySerializer, MobileWalletNetworksSerializer
from .models import Countries, MobileWalletNetworks
from common.functions import (
    get_international_send_money_transaction_fee_list,
    set_response_data,
)
from common.services import (
    get_mfs_banks,
    get_mfs_exhange_rate,
    mfs_mobile_money_account_enquiry,
    mfs_name_enquiry,
)
# from logic_omnichannel import settings
from django.conf import settings

# Create your views here.


class GetCountries(generics.ListAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = CountrySerializer

    def get_queryset(self):
        """
        returns querysets of all the MFS serviced countries ordered by their names
        """
        queryset = Countries.objects.all().order_by("name")
        return queryset

    def list(self, request, *args, **kwargs):
        """
        try to get the queryset and pass it into the serializer and return its
        response
        """
        try:
            queryset = self.get_queryset()
            serializer = CountrySerializer(queryset, many=True)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Countries List Successful",
                detail={"countries": serializer.data},
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Retrieve Countries List",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetBank(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, country_code):
        """
        Try to get banks in a particular country by bank country code
        """
        try:
            resp = get_mfs_banks(country_code)
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Get Bank List Successful",
                    detail={"banks": resp.get("response_detail", {}).get("mfs_banks")},
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable to retrieve Bank List"
                    ),
                    detail=resp.get("response_detail", "Unable to retrieve Bank List"),
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to retrieve Bank List",
                detail=f"Unable to retrieve Bank List {e}",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class NameEnquiry(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, country_code, account_number, mfs_bank_code):
        """
        try to get phone number and name of the authenticated user, then pass it into
        the mfs name enquiry service then return the response
        """
        try:
            customer = request.auth.user
            name = f"{customer.first_name} {customer.last_name}"
            phone_number = customer.customerprofile.phone_number

            resp = mfs_name_enquiry(
                country_code, phone_number, name, account_number, mfs_bank_code
            )
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description=resp.get(
                        "response_description", "Name Enquiry Successful"
                    ),
                    detail={
                        "account_name": resp.get("response_detail", {}).get(
                            "account_name"
                        )
                    },
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable to Retrieve Account Name"
                    ),
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Retrieve Account Name",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetExchangeRate(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, destination_currency_code, destination_country_code):
        """
        tries to get the exchange rate between source currency and destination currency
        """
        try:
            resp = get_mfs_exhange_rate(
                destination_currency_code, destination_country_code
            )

            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                bank = Bank.objects.get(alias=request.auth.user.app_code)
                destination_country = Countries.objects.get(
                    country_code=destination_country_code
                )
                omni_exchange_rate = resp.get("response_detail", {}).get(
                    "omni_exchange_rate"
                )
                source_currency = 1 / omni_exchange_rate
                destination_currency = Decimal("1.00")
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Get Exchange Rate Request Successful",
                    detail={
                        "source_currency": source_currency.quantize(
                            Decimal("1.00")
                        ),
                        "destination_currency": destination_currency,
                        "transaction_fee": get_international_send_money_transaction_fee_list(
                            bank, destination_country
                        ),
                    },
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable to Get Exchange Rate"
                    ),
                    detail=resp.get("response_detail", "Unable to Get Exchange Rate"),
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Get Exchange Rate",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetMobileMoneyPartnerByCountry(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = MobileWalletNetworksSerializer

    def get_queryset(self, country_code):
        queryset = MobileWalletNetworks.objects.filter(
            country__country_code=country_code
        ).order_by("mfs_network_partner__name")
        return queryset

    def get(self, request, country_code):
        try:
            """
            returns lists of mobile money partners for a particular country
            represented by the country code
            """
            queryset = self.get_queryset(country_code)
            serializer = MobileWalletNetworksSerializer(queryset, many=True)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Mobile Money Partner List Successful",
                detail={"mobile_money_partners": serializer.data},
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Retrieve Mobile Money Partner List",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class MobileMoneyAccountEnquiry(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, country_code, phone_number):
        try:
            """
            try to verify mobile wallet account for a particular phone number for a country
            """
            
            resp = mfs_mobile_money_account_enquiry(country_code, phone_number)
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Mobile Money Account Enquiry Successful",
                    detail= resp.get("response_detail")
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description",
                        "Unable to Retrieve Mobile Account Enquiry",
                    ),
                    detail="Unable to Retrieve Mobile Account Enquiry",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Retrieve Mobile Account Enquiry -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
