# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Countries',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=30, unique=True)),
                ('country_code', models.CharField(max_length=2, unique=True)),
                ('currency_code', models.CharField(max_length=3)),
                ('flag_img', models.ImageField(blank=True, null=True, upload_to='flags')),
                ('wallet', models.BooleanField(default=False)),
                ('bank_account', models.BooleanField(default=False)),
                ('cash_pickup', models.BooleanField(default=False)),
                ('percent_transaction_fee', models.DecimalField(decimal_places=2, default='100.00', max_digits=5)),
            ],
            options={
                'verbose_name': 'Country',
                'verbose_name_plural': 'Countries',
            },
        ),
        migrations.CreateModel(
            name='CurrencyRate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_currency_code', models.CharField(default='USD', max_length=3)),
                ('source_currency_code', models.CharField(default='NGN', max_length=3)),
                ('source_conversion_rate', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('omni_conversion_rate', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
            ],
            options={
                'verbose_name': 'Currency Rate',
                'verbose_name_plural': 'Currency Rates',
            },
        ),
        migrations.CreateModel(
            name='MFSNetworkPartners',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=25, unique=True)),
                ('code', models.CharField(max_length=10, unique=True)),
            ],
            options={
                'verbose_name': 'MFS Mobile Network Partner',
                'verbose_name_plural': 'MFS Mobile Network Partners',
            },
        ),
        migrations.CreateModel(
            name='MobileWalletNetworks',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('country_network_code', models.CharField(blank=True, max_length=5)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mfs.countries')),
                ('mfs_network_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mfs.mfsnetworkpartners')),
            ],
            options={
                'verbose_name': 'Mobile Wallet Network',
                'verbose_name_plural': 'Mobile Wallet Networks',
            },
        ),
        migrations.AddConstraint(
            model_name='currencyrate',
            constraint=models.UniqueConstraint(fields=('base_currency_code', 'source_currency_code'), name='unique_currency_rate'),
        ),
        migrations.AddConstraint(
            model_name='mobilewalletnetworks',
            constraint=models.UniqueConstraint(fields=('mfs_network_partner', 'country'), name='unique_country_network'),
        ),
    ]
