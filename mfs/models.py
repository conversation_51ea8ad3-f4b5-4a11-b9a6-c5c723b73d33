from django.db import models
import base64

from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


# Create your models here.


class Countries(models.Model):
    class Meta:
        verbose_name = "Country"
        verbose_name_plural = "Countries"

    name = models.CharField(max_length=30, unique=True)
    country_code = models.CharField(max_length=2, unique=True)
    currency_code = models.CharField(max_length=3)
    flag_img = models.ImageField(upload_to="flags", blank=True, null=True)
    wallet = models.BooleanField(default=False)
    bank_account = models.BooleanField(default=False)
    cash_pickup = models.BooleanField(default=False)
    percent_transaction_fee = models.DecimalField(
        max_digits=5, decimal_places=2, default="100.00"
    )

    def clean_fields(self, exclude=None) -> None:
        super().clean_fields(exclude=exclude)

        if self.percent_transaction_fee < 0 or self.percent_transaction_fee > 100:
            raise ValidationError(
                {
                    "percent_transaction_fee": _(
                        "Invalid Percent. Make sure percent is between 0 and 100"
                    )
                }
            )
    
    def get_flag_base64(self):
        """
        Retieves the Image flag file and converts it to base 64 and returns it
        """
        if self.flag_img is not None:
            with open(self.flag_img.path, "rb") as img_file:
                img_encode = base64.b64encode(img_file.read())
                img_encode = str(img_encode, "utf-8")
            return img_encode
        return None

    def __str__(self) -> str:
        return self.name


class MFSNetworkPartners(models.Model):
    class Meta:
        verbose_name = "MFS Mobile Network Partner"
        verbose_name_plural = "MFS Mobile Network Partners"

    name = models.CharField(max_length=25, unique=True)
    code = models.CharField(max_length=10, unique=True)

    def __str__(self) -> str:
        return self.name


class MobileWalletNetworks(models.Model):
    class Meta:
        verbose_name = "Mobile Wallet Network"
        verbose_name_plural = "Mobile Wallet Networks"
        constraints = [
            models.UniqueConstraint(
                fields=["mfs_network_partner", "country"], name="unique_country_network"
            )
        ]

    mfs_network_partner = models.ForeignKey(
        MFSNetworkPartners, on_delete=models.CASCADE
    )
    country = models.ForeignKey(Countries, on_delete=models.CASCADE)
    country_network_code = models.CharField(max_length=5, blank=True)


class CurrencyRate(models.Model):
    class Meta:
        verbose_name = "Currency Rate"
        verbose_name_plural = "Currency Rates"
        constraints = [
            models.UniqueConstraint(
                fields=["base_currency_code", "source_currency_code"],
                name="unique_currency_rate",
            )
        ]

    base_currency_code = models.CharField(max_length=3, default="USD")
    source_currency_code = models.CharField(max_length=3, default="NGN")
    source_conversion_rate = models.DecimalField(
        max_digits=10, decimal_places=2, default="0.00"
    )
    omni_conversion_rate = models.DecimalField(
        max_digits=10, decimal_places=2, default="0.00"
    )

