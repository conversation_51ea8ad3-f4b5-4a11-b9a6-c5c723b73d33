from django.urls.conf import path
from rest_framework import serializers

from .models import Countries, MobileWalletNetworks


class CountrySerializer(serializers.ModelSerializer):

    flag_img_url = serializers.SerializerMethodField("get_flag_img_url")
    flag_base_64 = serializers.SerializerMethodField("get_flag_base_64")
    available_channels = serializers.SerializerMethodField(
        "get_list_of_available_channels"
    )

    class Meta:
        model = Countries
        fields = [
            "name",
            "country_code",
            "currency_code",
            "bank_account",
            "wallet",
            "cash_pickup",
            "flag_img_url",
            "flag_base_64",
            "available_channels",
        ]

    def get_flag_img_url(self, obj):
        # for image files we can't serialize  them as image but we can provide it's url path
        if flag := obj.flag_img:
            return flag.url
        else:
            return None

    def get_flag_base_64(self, obj):
        # for image files we can't serialize  them as image but we can provide it's base 64 version
        if obj.flag_img:
            return obj.get_flag_base64()
        else:
            return None

    def get_list_of_available_channels(self, obj):
        # returns list of channels
        available_channels = []
        if obj.wallet:
            available_channels.append("mobile_money")
        if obj.bank_account:
            available_channels.append("bank_account")
        if obj.cash_pickup:
            available_channels.append("cash_pickup")
        return available_channels


class MobileWalletNetworksSerializer(serializers.ModelSerializer):

    mfs_network_partner = serializers.SerializerMethodField("get_mfs_network_partner")
    country = serializers.SerializerMethodField("get_country")

    class Meta:
        model = MobileWalletNetworks
        fields = "__all__"

    def get_mfs_network_partner(self, obj):
        return obj.mfs_network_partner.name

    def get_country(self, obj):
        return obj.country.name
