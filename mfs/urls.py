from django.urls import path

from .views import (
    GetBank,
    GetCountries,
    GetExchangeRate,
    GetMobileMoneyPartnerByCountry,
    MobileMoneyAccountEnquiry,
    NameEnquiry,
)


urlpatterns = [
    path("get_countries/", GetCountries.as_view(), name="mfs_get_countries"),
    path("get_banks/<str:country_code>/", GetBank.as_view(), name="mfs_get_banks"),
    path(
        "name_enquiry/<str:country_code>/<str:account_number>/<str:mfs_bank_code>/",
        NameEnquiry.as_view(),
        name="mfs_name_enquiry",
    ),
    path(
        "get_exhange_rate/<str:destination_currency_code>/<str:destination_country_code>/",
        GetExchangeRate.as_view(),
        name="get_exhange_rate",
    ),
    path(
        "mobile_money_partners_by_country_code/<str:country_code>/",
        GetMobileMoneyPartnerByCountry.as_view(),
        name="mobile_money_partners_by_country_code",
    ),
    path(
        "mobile_money_account_enquiry/<str:country_code>/<str:phone_number>/",
        MobileMoneyAccountEnquiry.as_view(),
        name="mobile_money_account_enqiury",
    ),
]
