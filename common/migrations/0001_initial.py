# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.db import migrations, models
import django.db.models.deletion
import django_countries.fields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bank', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccessChannel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=15, unique=True)),
                ('status', models.BooleanField(default=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
            ],
            options={
                'verbose_name': 'Channel Type',
                'verbose_name_plural': 'Access Channels',
                'db_table': 'omni_channel_type',
            },
        ),
        migrations.CreateModel(
            name='AuthenticationType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=15, unique=True)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
            ],
            options={
                'verbose_name': 'Authentication Type',
                'verbose_name_plural': 'Authentication Type',
                'db_table': 'omni_authentication_type',
            },
        ),
        migrations.CreateModel(
            name='BVN',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bvn', models.CharField(db_index=True, max_length=25, unique=True)),
                ('phone_number', models.CharField(blank=True, max_length=25, null=True)),
                ('first_name', models.CharField(blank=True, max_length=40, null=True)),
                ('last_name', models.CharField(blank=True, max_length=40, null=True)),
                ('dob', models.CharField(blank=True, max_length=25, null=True)),
                ('formatted_dob', models.CharField(blank=True, max_length=25, null=True)),
                ('gender', models.CharField(blank=True, max_length=10)),
                ('residential_address', models.TextField(blank=True)),
            ],
            options={
                'verbose_name': 'BVN',
                'verbose_name_plural': 'BVN',
            },
        ),
        migrations.CreateModel(
            name='CurrencyType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('symbol', models.CharField(db_index=True, max_length=5, unique=True)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
            ],
            options={
                'verbose_name': 'Currency Type',
                'verbose_name_plural': 'Currency Type',
                'db_table': 'omni_currency_type',
            },
        ),
        migrations.CreateModel(
            name='DocumentType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=15, unique=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
            ],
            options={
                'verbose_name': 'Document Type',
                'verbose_name_plural': 'Document Type',
                'db_table': 'omni_document_type',
            },
        ),
        migrations.CreateModel(
            name='LocalGovernment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
            ],
            options={
                'verbose_name': 'Local Government',
                'verbose_name_plural': 'Local Government',
                'db_table': 'omni_local_government',
            },
        ),
        migrations.CreateModel(
            name='ManualReversals',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reversal_transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('user_ref', models.CharField(max_length=30, verbose_name="transaction's user ref")),
                ('transaction_type', models.CharField(max_length=200)),
                ('core_banking_ref', models.CharField(db_index=True, max_length=25)),
                ('status', models.CharField(choices=[('S', 'Successful'), ('F', 'Failed')], default='F', max_length=2)),
                ('date', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Platform',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=25, unique=True)),
                ('code', models.CharField(max_length=10, unique=True)),
                ('description', models.TextField(blank=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='SMSToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token_identifier', models.CharField(max_length=100, unique=True)),
                ('token', models.CharField(max_length=15)),
                ('amount', models.DecimalField(blank=True, decimal_places=2, default='0.00', max_digits=10, null=True)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
                ('expiry_date', models.DateTimeField(blank=True, null=True, verbose_name='date published')),
                ('status', models.CharField(blank=True, choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2, null=True)),
            ],
            options={
                'verbose_name': 'SMS Token',
                'verbose_name_plural': 'SMS Token',
                'db_table': 'omni_sms_token',
            },
        ),
        migrations.CreateModel(
            name='State',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
            ],
            options={
                'verbose_name': 'State',
                'verbose_name_plural': 'States',
                'db_table': 'omni_state',
            },
        ),
        migrations.CreateModel(
            name='TelecommunicationNetwork',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=15, unique=True)),
                ('logo', models.TextField(default='paste binary here ....')),
                ('country', django_countries.fields.CountryField(default='NG', max_length=2)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
            ],
            options={
                'verbose_name': 'Telecommunication Network',
                'verbose_name_plural': 'Telecommunication Networks',
                'db_table': 'omni_telecommunication_network',
            },
        ),
        migrations.CreateModel(
            name='TransactionType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=25, unique=True)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
            ],
            options={
                'verbose_name': 'Transaction Type',
                'verbose_name_plural': 'Transaction Type',
                'db_table': 'omni_transaction_type',
            },
        ),
        migrations.CreateModel(
            name='SettlementAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_name', models.CharField(max_length=50, unique=True)),
                ('account_number', models.CharField(max_length=15)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bank')),
                ('transaction_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.transactiontype')),
            ],
            options={
                'verbose_name': 'Settlement Account',
                'verbose_name_plural': 'Settlement Accounts',
            },
        ),
        migrations.CreateModel(
            name='PersistPaystackPayments',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reference_id', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('external_reference', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('payment_type', models.CharField(choices=[('WF', 'Wallet Funding'), ('SUB', 'Subscription')], max_length=3)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=12)),
                ('bill_payment_ref', models.TextField(blank=True, null=True)),
                ('ledger_account_ref', models.CharField(blank=True, max_length=100, null=True)),
                ('last_four_digits', models.CharField(blank=True, max_length=4, null=True)),
                ('verified', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('bank', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='bank_paystack_payments', to='bank.bank')),
            ],
            options={
                'verbose_name': "Paystack Payment's Gateway",
                'verbose_name_plural': "Paystack Payment's Gateways",
            },
        ),
        migrations.CreateModel(
            name='MobileApplicationVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version_id', models.CharField(max_length=10)),
                ('description', models.TextField(blank=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.platform', to_field='code')),
            ],
            options={
                'verbose_name': 'Mobile App Version',
                'verbose_name_plural': 'Mobile App Versions',
            },
        ),
    ]
