# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('common', '0001_initial'),
        ('user_profile', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='manualreversals',
            name='bank_admin',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user_profile.bankadmin'),
        ),
        migrations.AddField(
            model_name='localgovernment',
            name='state',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='common.state'),
        ),
        migrations.AddConstraint(
            model_name='settlementaccount',
            constraint=models.UniqueConstraint(fields=('bank', 'transaction_type'), name='unique_settlement_account'),
        ),
        migrations.AddConstraint(
            model_name='mobileapplicationversion',
            constraint=models.UniqueConstraint(fields=('version_id', 'platform'), name='unique_app_version'),
        ),
    ]
