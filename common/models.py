import uuid

from django.core.exceptions import ValidationError
from bank.models import Bank
from billing.models import BankBillDuePayments
from ledger_manager.models import AccountHistory
from django.db import models
from user_profile.models import BankAdmin


# Create your models here.
from django_countries.fields import CountryField


class AuthenticationType(models.Model):

    def __str__(self):
        return self.name

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    name = models.CharField(max_length=200)
    code = models.CharField(max_length=15, unique=True)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = 'omni_authentication_type'
        verbose_name = "Authentication Type"
        verbose_name_plural = "Authentication Type"


class TransactionType(models.Model):

    def __str__(self):
        return self.name

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    name = models.CharField(max_length=200)
    code = models.CharField(max_length=25, unique=True)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = 'omni_transaction_type'
        verbose_name = "Transaction Type"
        verbose_name_plural = "Transaction Type"


class AccessChannel(models.Model):

    def __str__(self):
        return self.name

    name = models.CharField(max_length=200)
    code = models.CharField(max_length=15, unique=True)
    status = models.BooleanField(default=True)

    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = 'omni_channel_type'
        verbose_name = "Channel Type"
        verbose_name_plural = "Access Channels"


class TelecommunicationNetwork(models.Model):

    def __str__(self):
        return self.name

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    name = models.CharField(max_length=200)
    code = models.CharField(max_length=15, unique=True)
    logo = models.TextField(default="paste binary here ....")
    country = CountryField(
        default='NG',
    )
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = 'omni_telecommunication_network'
        verbose_name = "Telecommunication Network"
        verbose_name_plural = "Telecommunication Networks"


class State(models.Model):

    def __str__(self):
        return self.name

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    name = models.CharField(max_length=200)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )

    class Meta:
        db_table = 'omni_state'
        verbose_name = "State"
        verbose_name_plural = "States"


class LocalGovernment(models.Model):

    def __str__(self):
        return self.name

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    name = models.CharField(max_length=200)
    state = models.ForeignKey(State, on_delete=models.CASCADE)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )

    class Meta:
        db_table = 'omni_local_government'
        verbose_name = 'Local Government'
        verbose_name_plural = 'Local Government'


class DocumentType(models.Model):

    def __str__(self):
        return self.name

    name = models.CharField(max_length=200)
    code = models.CharField(max_length=15, unique=True)

    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = 'omni_document_type'
        verbose_name = "Document Type"
        verbose_name_plural = "Document Type"


class SMSToken(models.Model):
    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    token_identifier = models.CharField(max_length=100, unique=True, blank=False)
    token = models.CharField(max_length=15)
    amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, default='0.00')
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)
    expiry_date = models.DateTimeField('date published', blank=True, null=True)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
        blank=True,
        null=True,
    )

    class Meta:
        db_table = 'omni_sms_token'
        verbose_name = "SMS Token"
        verbose_name_plural = "SMS Token"


class CurrencyType(models.Model):

    def __str__(self):
        return self.name

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    name = models.CharField(max_length=200)
    symbol = models.CharField(max_length=5, unique=True, db_index=True)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = 'omni_currency_type'
        verbose_name = "Currency Type"
        verbose_name_plural = "Currency Type"


class BVN(models.Model):
    class Meta:
        verbose_name = "BVN"
        verbose_name_plural = "BVN"

    YES = "Y"
    NO = "N"
    YES_NO_CHOICES = (
        (YES, "Yes"),
        (NO, "No"),
    )

    bvn = models.CharField(max_length=25, unique=True, db_index=True)
    phone_number = models.CharField(max_length=25, blank=True, null=True)
    first_name = models.CharField(max_length=40, blank=True, null=True)
    last_name = models.CharField(max_length=40, blank=True, null=True)
    dob = models.CharField(max_length=25, blank=True, null=True)
    formatted_dob = models.CharField(max_length=25, blank=True, null=True)
    gender = models.CharField(max_length=10, blank=True)
    residential_address = models.TextField(blank=True)



class SettlementAccount(models.Model):

    account_name = models.CharField(max_length=50, unique=True)
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE)
    account_number = models.CharField(max_length=15)
    transaction_type = models.ForeignKey(TransactionType, on_delete=models.CASCADE)
    date_created = models.DateTimeField(auto_now_add=True)

    def clean_fields(self, exclude=None) -> None:
        super().clean_fields(exclude=exclude)
        if not self.bank.is_partner_bank:
            raise ValidationError({
                'bank': 'Bank Should Be A Partner Bank'
            })

    class Meta:
        verbose_name = 'Settlement Account'
        verbose_name_plural = 'Settlement Accounts'
        constraints = [
            models.UniqueConstraint(fields=['bank', 'transaction_type'], name='unique_settlement_account')
        ]


class Platform(models.Model):

    name = models.CharField(max_length=25, unique=True)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)
    date_created = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return self.name


class MobileApplicationVersion(models.Model):
    class Meta:
        verbose_name = "Mobile App Version"
        verbose_name_plural = "Mobile App Versions"
        constraints = [
            models.UniqueConstraint(
                fields=["version_id", "platform"], name="unique_app_version"
            )
        ]

    version_id = models.CharField(max_length=10)
    platform = models.ForeignKey(Platform, on_delete=models.CASCADE, to_field="code")
    description = models.TextField(blank=True)
    date_created = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return f"{self.platform.name} {self.version_id}"
    
class PersistPaystackPayments(models.Model):
    
    
    WALLET_FUNDING = "WF"
    SUBSCRIPTION = "SUB"
    
    PAYMENT_TYPE = (
        (WALLET_FUNDING, "Wallet Funding"), 
        (SUBSCRIPTION, "Subscription")
    )
    class Meta:
        verbose_name = "Paystack Payment's Gateway"
        verbose_name_plural = "Paystack Payment's Gateways"
    
    reference_id =  models.UUIDField(
        default=uuid.uuid4, unique=True, db_index=True)
    bank = models.ForeignKey(
        Bank, blank=True, null=True, on_delete=models.CASCADE, related_name="bank_paystack_payments"
    )
    external_reference = models.CharField(null=True, blank=True, max_length=50, unique=True)
    payment_type = models.CharField(max_length=3, choices=PAYMENT_TYPE)
    amount = models.DecimalField(
        max_digits=12, decimal_places=2, default="0.00")
    bill_payment_ref = models.TextField(null=True, blank=True)
    ledger_account_ref = models.CharField(null=True, blank=True, max_length=100)
    last_four_digits = models.CharField(max_length=4, null=True, blank=True)  # should be null=True
    verified = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True)
    

class ManualReversals(models.Model):

    FAILED = "F"
    COMPLETED = "S"

    TRANS_STATUS_CHOICES = (
        (COMPLETED, "Successful"),
        (FAILED, "Failed"),
    )

    reversal_transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    user_ref = models.CharField("transaction's user ref", max_length=30) 
    transaction_type = models.CharField(max_length=200)
    core_banking_ref = models.CharField(max_length=25, db_index=True)
    bank_admin = models.ForeignKey(BankAdmin, on_delete=models.CASCADE)
    status = models.CharField(max_length=2, choices=TRANS_STATUS_CHOICES, default=FAILED)
    date = models.DateTimeField(auto_now_add=True)    


class BillAdviceClass:
    request_id = ""
    request_type = ""
    channel_code = ""
    outlet_code = ""
    payment_item_code = ""
    source_currency = ""
    source_inst_code = ""
    sop_account = ""
    sop_inst_code = ""
    prefund_account = ""
    prefund_account_inst_code = ""
    source_account = ""
    customer_unique_number = ""
    customer_name = ""
    customer_email = ""
    customer_mobile = ""
    name_enquiry_reference = ""
    latitude = ""
    longitude = ""
    narration = ""
    amount = ""
    fee = ""


class SendMoneyClass:

    send_money_type = ''
    sender_account = ''
    sender_institution_code = ''
    destination_account = ''
    destination_institution_code = ''
    amount = ''
    narration = ''
    transaction_ref = ''
    bank_name = ""
    sender_account_name = ""
    destination_account_name = ""
    sender_email = ""
    session_id = ""


class BillsPaymentAdviceClass:

    amount = ''
    sender_email = ''
    sender_mobile = ''
    hash_value = ''
    institution_code = ''
    payment_code = ''
    request_id = ''
    billing_unique_number = ''
    bank_name = ''


class InternationalSendMoneyClass:

    request_id = ""
    reciever_account_number = ""
    reciever_amount = ""
    reciever_currency_code = ""
    reciever_mfs_bank_code = ""
    reciever_mfs_mobile_wallet_code = ""
    narration = ""
    reciever_country_code = ""
    reciever_first_name = ""
    reciever_last_name = ""
    reciever_phone_number = ""
    reciever_address = ""
    sender_country_code = ""
    sender_first_name = ""
    sender_last_name = ""
    sender_phone_number = ""
    sender_address = ""
    sender_dob = ""
    sender_document_id = ""
