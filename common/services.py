from bill_payment.models import AirtimeBiller
from common.corebankings.bank_one import BankOneResource
from common.corebankings.dsgs import DSGSResource
from common.corebankings.nibss import NIBSSResource
from dfa.models import AgentOnboardedAccount, Merchant
from customer.models import CustomerAccount
from bank.models import Bank
import base64
from decimal import Decimal
from django.core.cache import cache

from django.db.models import Q

from common.functions import (
    clean_dob,
    clean_mfs_banks,
    clean_package_options,
    get_currency_to_usd_from_usd_to_currency,
    get_nam_token,
    get_omni_channel_usd_to_naira_config,
    get_reference,
    get_reciever_details,
    add_reciever_details,
    mock_get_package_options,
    set_response_data,
)
from common.models import BVN
import logging
import uuid

from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import (
    Mail,
    Attachment,
    FileContent,
    FileName,
    FileType,
    Disposition,
)

import requests
import qrcode
import os
import random
import urllib.parse
from rest_framework.utils import json
# from logic_omnichannel import settings
from django.conf import settings
from nxpay.models import Token
from .validators import validate_phone_number


log = logging.getLogger("django")

def update_nam_token(token:str, app_code):
    nam_token_dict = cache.get('nam_token_dict')
    if nam_token_dict:
        nam_token_dict[app_code] = token
    else:
        nam_token_dict = {app_code: token}
    cache.set('nam_token_dict', nam_token_dict)

def local_retrieve_nam_token(app_code):
    nam_token_dict = cache.get('nam_token_dict')
    if nam_token_dict:
        token = nam_token_dict.get(app_code, '')
        return token
    return ''


def update_exchange_rate(country_code: str, exchange_rate_dict: dict) -> None:
    """
    update exchange rate cache by coutry code
    """
    exchange_rate = cache.get("exchange_rate")
    if exchange_rate:
        exchange_rate[country_code] = exchange_rate_dict
    else:
        exchange_rate = {country_code: exchange_rate_dict}
    cache.set("exchange_rate", exchange_rate)


def retrieve_exchange_rate(country_code: str) -> dict:
    """
    retrieve exchange rate from cache if it exists
    """
    exchange_rate = cache.get("exchange_rate")
    if exchange_rate:
        return exchange_rate.get(country_code, {})
    return {}


def retrieve_balance_enquiry(token: str, account_number: str) -> tuple:
    balance_enquiry_dict = cache.get("balance_enquiry_dict")
    if balance_enquiry_dict:
        customer_dict = balance_enquiry_dict.get(token, {}).get(account_number)
        if customer_dict:
            return (customer_dict.get("balance_enquiry"), customer_dict.get("retry"))
        return (None, True)
    return (None, True)


def update_balance_enquiry(
    token: str, account_number: str = '', balance_enquiry: dict = {}, retry: bool = False
) -> None:
    balance_enquiry_dict = cache.get("balance_enquiry_dict")
    if balance_enquiry_dict:
        new_balance_state = not retry and balance_enquiry != {}
        customer_dict = balance_enquiry_dict.get(token, {})
        if account_number:
            account_balance_enquiry = {
                "balance_enquiry": balance_enquiry
                if new_balance_state
                else balance_enquiry_dict.get(token, {})
                .get(account_number, {})
                .get("balance_enquiry"),
                "retry": retry,
            }
            if customer_dict:
                customer_dict[account_number] = account_balance_enquiry
            else:
                balance_enquiry_dict[token] = {account_number: account_balance_enquiry}
        else:
            # for scenarios where account number is not provided we update all the account belonging to the customer
            for _account_number in customer_dict.keys():
                customer_dict[_account_number]["retry"] = retry
    else:
        balance_enquiry_dict = {
            token: {
                account_number: {
                    "balance_enquiry": balance_enquiry,
                    "retry": retry,
                }
            }
        }
    cache.set("balance_enquiry_dict", balance_enquiry_dict)


def validate_bvn(bvn: str, partner_bank=None):
    """
    Try to verify bvn is valid and return basic details attached to BVN,
    we first try calling nibss bvn service first and when there is failed
    response do a fall back to the partner banks internal account directory.
    falling back to the bank internal directory should not occur often.
    """
    log = logging.getLogger("django")
    url = f"{settings.BVN_SERVICE_BASE_URL}/{bvn}"
    try:
        if settings.APP_ENV != "production":
            log.info(f"Trying to BVN Locally :::::")
            dob, formatted_dob = clean_dob("********", core_banking=True)
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": {
                    "bvn": "***********",
                    "mobile": "***********",
                    "first_name": "Victor",
                    "last_name": "Odu William",
                    "dob": dob,
                    "formatted_dob": formatted_dob,
                    "gender": "MALE",
                    "residential_address": "JB Street, Ekosodin, Benin City, Edo State.",
                },
            }
            return data

        log.info(f"Trying to call NIBBS BVN SERVICE url -> {url}")
        response = requests.get(url)
        resp = response.json()
        if (
            response.status_code == 200
            and resp.get("status") == settings.SUCCESS_RESPONSE
        ):
            log.info(f"SUCCESS RESPONSE FROM NIBBS BVN SERVICE")
            dob, formatted_dob = clean_dob(resp.get("dateOfBirth"), core_banking=False)
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": {
                    "bvn": bvn,
                    "mobile": resp.get("phoneNumber"),
                    "first_name": resp.get("firstName"),
                    "last_name": resp.get("lastName"),
                    "dob": dob,
                    "formatted_dob": formatted_dob,
                    "gender": resp.get("gender"),
                    "residential_address": resp.get("residentialAddress"),
                },
            }
            return data
        else:
            log.info(
                f'FAILED RESPONSE FROM NIBBS BVN SERVICE error -> {resp.get("ResponseDescription", "Unable to resolve BVN")}'
            )
            if partner_bank == None:
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_description": f'{resp.get("ResponseDescription", "Unable to resolve BVN")}',
                }
                return data
    except Exception as e:
        log.info(f"FAILED RESPONSE FROM NIBBS BVN SERVICE error -> {e}")
        if partner_bank == None:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Error Reaching BVN Verification Service: {e}",
            }
            return data

    url = f"{settings.MONEY_FLEX_OPEN_API_BASE_URL}/get_bvn_details/1.0.0/v1/bvn/local"

    payload = {"bvn": bvn, "companyCode": partner_bank.code}
    payload = json.dumps(payload)

    app_code = partner_bank.alias
    token = local_retrieve_nam_token(app_code)
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/x-www-form-urlencoded",
    }

    try:
        log.info(f"Trying to call CORE BANKING LOCAL BVN SERVICE url -> {url}")
        response = requests.request("POST", url, headers=headers, data=payload)
        if response.status_code == 401:
            # token has expired or invalid
            token = get_nam_token(bank_alias=app_code)
            update_nam_token(token, app_code)
            headers["Authorization"] = f"Bearer {token}"
            response = requests.request("POST", url, headers=headers, data=payload)
        resp = response.json()
        if resp.get("ResponseCode") == settings.SUCCESS_RESPONSE:
            log.info(f"SUCCESS RESPONSE FROM CORE BANKING LOCAL BVN SERVICE")
            dob, formatted_dob = clean_dob(resp.get("dob"))
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": {
                    "bvn": bvn,
                    "mobile": resp.get("phoneNumber"),
                    "first_name": resp.get("firstname"),
                    "last_name": resp.get("lastname"),
                    "dob": dob,
                    "formatted_dob": formatted_dob,
                },
            }
            return data
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f'{resp.get("ResponseDescription", "Unable to resolve BVN")}',
        }
        log.info(f"FAILED RESPONSE FROM CORE BANKING LOCAL BVN SERVICE resp -> {data}")
        return data
    except Exception as e:
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Reaching BVN Verification Service: {e}",
        }
        log.info(f"FAILED RESPONSE FROM CORE BANKING LOCAL BVN SERVICE resp -> {data}")
        return data

def bill_payment_items(billerCode):
    url = settings.BILL_PAYMENT_ITEMS_URL + str(billerCode)
    try:
        r = requests.get(url)
        response_data = r.json()
        data = {'response_code': settings.SUCCESS_RESPONSE, 'record': response_data}
        return data
    except Exception as e:
        return {'response_code': settings.FAILED_RESPONSE, 'response_description': 'error connecting to switch'}


def bill_customer_validation(customer_code, vendor_code, payment_code):
    url = settings.BILL_CUSTOMER_VALIDATION_URL
    request_id = f'{random.randrange(1, 10 ** 3):08}'
    # string_to_hash = request_id + customer_code + vendor_code + payment_code
    # hashed_string = hmac.new(key.encode('utf-8'), string_to_hash.encode('utf-8'), hashlib.sha512).hexdigest()

    try:

        request_payload = \
            {
                "requestID": request_id,
                "customerCode": customer_code,
                "vendorCode": vendor_code,
                "paymentCode": payment_code,
                "hash": request_id
            }

        r = requests.post(url, json=request_payload)
        r.request.headers['Content-Type']
        'application/json'
        response_data = r.json()
        if response_data['responseCode'] == 'INLAKS_00':
            data = {'response_code': settings.SUCCESS_RESPONSE, 'response_description': 'success'}
        else:
            data = {'response_code': '01', 'response_description': 'error validating customer'}
        return data
    except Exception as e:
        pass


def name_enquiry(accountNumber, institutionCode):

    url = settings.SWITCH_BASE_URL + 'nameEnquiry/' + accountNumber + '/' + institutionCode

    try:
        r = requests.get(url)
        response_data = r.json()
        account_name = response_data['accountName']
        account_number = response_data['accountNumber']
        transaction_ref = response_data['transactionRef']

        if response_data['responseCode'] and response_data['responseCode'] == 'RESP_00' and account_name:
            data = {
                'response_code': settings.SUCCESS_RESPONSE,
                'response_description': 'success',
                'account_name': account_name,
                'account_number': account_number,
                'transaction_ref': transaction_ref
            }
        else:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': response_data['responseDescription']
            }

        return data
    except Exception as e:
        error = str(e)
        raise Exception(error)


def account_history(**args):
    url = settings.SWITCH_BASE_URL
    try:
        r = requests.get(url)
        response_data = r.json()
        account_name = response_data['accountName']
        account_number = response_data['accountNumber']
        transaction_ref = response_data['transactionRef']

        if response_data['responseCode'] and response_data['responseCode'] == 'RESP_00' and account_name:
            ref = response_data['transactionRef']
            data = {'response_code': settings.SUCCESS_RESPONSE, 'response_description': 'success',
                    'account_name': account_name,
                    'account_number': account_number, 'transaction_ref': transaction_ref}
        else:
            data = {'response_code': settings.FAILED_RESPONSE,
                    'response_description': response_data['responseDescription']
                    }

        return data
    except Exception as e:
        return {'response_code': settings.FAILED_RESPONSE, 'response_description': 'error connecting to switch'}


def bill_advice(BillAdviceClass):
    log = logging.getLogger('ams_bp')
    url = settings.BILL_ADVICE_URL
    request_id = f'{random.randrange(1, 10 ** 3):08}'
    # string_to_hash = request_id + customer_code + vendor_code + payment_code
    # hashed_string = hmac.new(key.encode('utf-8'), string_to_hash.encode('utf-8'), hashlib.sha512).hexdigest()

    try:

        request_payload = \
            {
                "requestID": BillAdviceClass.request_id,
                "requestType": BillAdviceClass.request_type,
                "channelCode": '4',
                "paymentItemCode": BillAdviceClass.payment_item_code,
                "sourceInstitutionCode": BillAdviceClass.source_inst_code,
                "sourceAccountNumber": BillAdviceClass.source_account,
                "sopInstitutionCode": BillAdviceClass.sop_inst_code,
                "sopAccountNumber": BillAdviceClass.sop_account,
                "prefundWalletInstitutionCode": BillAdviceClass.prefund_account_inst_code,
                "prefundWalletAccountNumber": BillAdviceClass.prefund_account,
                # ====pick this up from config/settings====(BP wallet control)*********)
                "prefundWalletDestinationInstitutionCode": "000000",
                "prefundWalletDestinationAccountNumber": "*********",
                # ============================================
                "prefundAmount": BillAdviceClass.amount,
                "customerUniqueNumber": BillAdviceClass.customer_unique_number,
                "customerName": BillAdviceClass.customer_name,
                "customerEmail": BillAdviceClass.customer_email,
                "customerMobile": BillAdviceClass.customer_mobile,
                "latitude": '0.00000',
                "longitude": '0.00000',
                "amount": BillAdviceClass.amount,
                "fee": BillAdviceClass.fee,
                "narration": BillAdviceClass.narration,
                "hash": request_id
            }

        r = requests.post(url, json=request_payload)
        log.info("switch request payload: " + str(request_payload))
        r.request.headers['Content-Type'] = 'application/json'

        response_data = r.json()

        log.info("switch response payload: " + str(response_data))
        do_tsq = False
        token = ''
        response_code = settings.FAILED_RESPONSE
        # set transaction ref
        if not (response_data['transactionRef'] is None):
            ref = response_data['transactionRef']
        else:
            ref = str(uuid.uuid4())
        # check for success response code
        if response_data['responseCode'] == 'INLAKS_00' or response_data['responseCode'] == 'RESP_00':
            response_code = settings.SUCCESS_RESPONSE
            description = 'successful'

            if not (response_data['rechargePIN'] is None):
                token = response_data['token']

        else:

            if not (response_data['doStatusQuery'] is None):

                do_tsq = response_data['doStatusQuery']

                if do_tsq or do_tsq == '':
                    do_tsq = True
                    response_code = settings.PENDING_RESPONSE
                    description = 'BP status unknown:' + response_data['responseDescription']

            else:
                do_tsq = False
                response_code = settings.FAILED_RESPONSE
                description = 'BP failed:' + response_data['responseDescription']

        data = {
            'response_code': response_code,
            'response_description': description,
            'token': token,
            'transaction_ref': ref,
            'do_tsq': do_tsq,
        }

        return data
    except Exception as e:
        data = {
            'response_code': response_code,
            'response_description': 'error connecting to switch',
            'token': token,
            'transaction_ref': str(uuid.uuid4()),
            'do_tsq': do_tsq,
        }
        return data


def account_enquiry(accountNumber, institutionCode):
    url = settings.SWITCH_BASE_URL + 'accountEnquiry/' + accountNumber + '/' + institutionCode

    try:
        r = requests.get(url)
        response_data = r.json()

        customer = response_data['customer']
        account_number = response_data['acctNumber']
        transaction_ref = response_data['transactionRef']

        phone_number = response_data['phoneNumber']
        if not phone_number:
            phone_number = response_data['phoneNumber']

        currency = response_data['currency']
        account_title = response_data['accountTitle']
        account_type = response_data['accountType']

        data = {
            'response_code': settings.SUCCESS_RESPONSE,
            'response_description': 'success',
            'account_name': account_title,
            'account_number': account_number,
            'account_type': account_type,
            'currency': currency,
            'phone_number': phone_number,
            'customer': customer,
            'transaction_ref': transaction_ref
        }
        return data
    except Exception as e:
        return {'response_code': settings.FAILED_RESPONSE, 'response_description': 'error connecting to switch'}


def balance_enquiry(bank: Bank, accountNumber, institutionCode):
    if bank.belongs_to_nibss_core_banking:
        nibbss_resp = NIBSSResource.account_details_enquiry(account_number=accountNumber)
        if nibbss_resp.get("status") == "success":
            data = {'response_code': settings.SUCCESS_RESPONSE, 'response_description': 'success',
                'account_balance': nibbss_resp.get("account_balance"),
                'account_number': accountNumber, 'transaction_ref': "0000"}
            return data
        elif nibbss_resp.get("status") == "empty":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Account Details Not Found"
            }
        elif nibbss_resp.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : nibbss_resp.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Something went wrong with request."
            }
    elif bank.belongs_to_bank_one:
        bank_one_response = BankOneResource.get_account_enquiry(accountNumber)
        if bank_one_response.get("status") == "success":
            data = {
                'response_code': settings.SUCCESS_RESPONSE,
                'response_description': 'success',
                'account_balance': bank_one_response.get('account_balance'),
                'account_number': accountNumber,
                'transaction_ref': "0000"
            }
            return data
        elif bank_one_response.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : bank_one_response.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : bank_one_response.get("message")
            }

    url = settings.SWITCH_BASE_URL + 'balanceEnquiry/' + accountNumber + '/' + institutionCode
    try:
        r = requests.get(url)
        response_data = r.json()
        account_balance = response_data['accountBalance']
        transaction_ref = response_data['transactionRef']
        data = {'response_code': settings.SUCCESS_RESPONSE, 'response_description': 'success',
                'account_balance': account_balance,
                'account_number': accountNumber, 'transaction_ref': transaction_ref}
        return data
    except Exception as e:
        return {'response_code': settings.FAILED_RESPONSE, 'response_description': 'error connecting to switch'}


def account_statement(account_number, institution_code, start_date, end_date):
    url = 'http://************:7079/wallet/v1/statementEnquiry/' + account_number + '/' + start_date + '/' + end_date
    try:
        r = requests.get(url)
        response_data = r.json()
        if response_data['responseCode'] == 'INLAKS_00':
            record = response_data['reports']
            data = {'response_code': settings.SUCCESS_RESPONSE,
                    'record': record,
                    'response_description': 'transaction record between' + start_date + ' and ' + end_date
                    }
        else:
            data = {'response_code': settings.FAILED_RESPONSE,
                    'record': 'record not found between ' + start_date + ' and ' + end_date
                    }

        return data
    except Exception as e:
        return {'response_code': settings.FAILED_RESPONSE, 'response_description': 'error connecting to switch'}


def account_statement_t24(account_number, inst_code, start_date, end_date):

    url = 'http://*************:6099/operations/v1/accountStatementEnquiry/' + account_number + '/' + inst_code \
          + '/' + end_date + '/' + start_date
    try:
        r = requests.get(url)
        response_data = r.json()
        if response_data['responseCode'] == 'RESP_00':
            record = response_data['magentacctstatementdetailType']
            data = {'response_code': settings.SUCCESS_RESPONSE,
                    'record': record,
                    'response_description': 'transaction record between' + start_date + ' and ' + end_date
                    }
        else:
            data = {'response_code': '01',
                    'record': [],
                    'response_description': ' record not found between ' + start_date + ' and ' + end_date
                    }

        return data
    except Exception as e:
        return {'response_code': settings.FAILED_RESPONSE, 'response_description': 'error connecting to switch'}


def fund_transfer(FundTransferClass):
    log = logging.getLogger('ams_cashin')
    url = settings.SWITCH_BASE_MFB_URL
    source_name = FundTransferClass.source_name
    source_inst_code = FundTransferClass.source_inst_code
    source_account = FundTransferClass.source_account
    destination_name = FundTransferClass.destination_name
    destination_inst_code = FundTransferClass.destination_inst_code
    destination_account = FundTransferClass.destination_account
    name_enquiry_ref = FundTransferClass.name_enquiry_reference
    narration = FundTransferClass.narration
    requestID = FundTransferClass.request_id
    amount = FundTransferClass.amount
    prefund_amount = FundTransferClass.prefund_amount
    fee = FundTransferClass.fee

    try:
        ft_request_payload = \
            {
                "requestID": requestID,
                "destinationInstitutionCode": destination_inst_code,
                "destinationAccountName": destination_name,
                "destinationAccountNumber": destination_account,  # "**********",
                "destinationBVN": "**********",
                "destinationKYCLevel": "1",
                "sourceInstitutionCode": source_inst_code,  # "NIP01",
                "sourceAccountNumber": source_account,  # "**********",
                # =====(Agent Operations Control=*********)
                "prefundWalletDestinationInstitutionCode": "000000",
                "prefundWalletDestinationAccountNumber": "*********",
                "prefundWalletInstitutionCode": FundTransferClass.prefund_inst_code,
                "prefundWalletAccountNumber": FundTransferClass.prefund_account,
                "prefundAmount": prefund_amount,
                "depositorName": destination_name,
                "transactionLocation": "6.4300747,3.4110715",
                "sopInstitutionCode": FundTransferClass.sop_inst_code,
                "sopAccountNumber": FundTransferClass.sop_account,
                "amount": amount,
                "fee": fee,
                "remarks": narration,
                "hash": "dd369839-7a73-4dd6-8282-55736c90"
            }
        log.info("switch request payload: " + str(ft_request_payload))
        r = requests.post(url, json=ft_request_payload)
        response_data = r.json()
        log.info("switch response payload: " + str(response_data))

        if response_data['responseCode'] == 'RESP_00' or \
                response_data['responseCode']  == 'INLAKS_00':
            data = {
                'response_code': '00',
                'response_description': 'cashin successful',
                'transaction_ref': response_data['transactionRef'],
                'do_tsq': False,
            }
        else:
            if response_data['retry'] == 'false' or response_data['retry'] == False:
                do_tsq = False
                response_code = settings.FAILED_RESPONSE
            elif response_data['retry'] == 'true' or response_data['retry'] == '' or response_data['retry'] == True:
                response_code = settings.PENDING_RESPONSE
                do_tsq = True

            data = {
                'response_code': response_code,
                'response_description': response_data['responseDescription'],
                'do_tsq': do_tsq
            }

        return data
    except Exception as e:
        data = {
            'response_code': settings.FAILED_RESPONSE,
            'response_description': 'error connecting to switch',
            'do_tsq': False
        }
        return data


def send_money(sender_bank: Bank, payloadClass, app_code=None):
    print(f"PAYLOAD CLASS: {payloadClass}")
    log = logging.getLogger('omni_send_money')

    if settings.APP_ENV == "developments":
        return {
            "response_code": settings.SUCCESS_RESPONSE,
            "response_description": "transaction Successful",
            "response_detail": {
                "nam_payment_reference": random.randrange(********0000, **********99)
            },
        }

    if sender_bank.belongs_to_nibss_core_banking:
        if payloadClass.send_money_type == "INTRA":
            nibbss_response = NIBSSResource.fund_transfer_internal(payloadClass) # log entire respone body that comes back
            if nibbss_response.get("status") == "success":
                data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description': "Fund Transfer Successful.",
                    'response_detail' : {
                        'cba_reference' : nibbss_response.get('cba_reference'),
                        "nam_payment_reference": nibbss_response.get('cba_reference')
                    }
                }
                log.info(f'Response from SEND MONEY service: {data}')
                return data
            elif nibbss_response.get("status") == "failed":
                return {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : nibbss_response.get("message")
                }
            else:
                return {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : "Something went wrong with request."
                }
        else:
            nibbss_response = NIBSSResource.fund_transfer_outward(payloadClass)
            if nibbss_response.get("status") == "success":
                data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description': "Fund Transfer Successful.",
                    'response_detail' : {
                        'cba_reference' : nibbss_response.get('cba_reference'),
                        "nam_payment_reference": nibbss_response.get('cba_reference')
                    }
                }
                log.info(f'Response from SEND MONEY service: {data}')
                return data
            elif nibbss_response.get("status") == "failed":
                return {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : nibbss_response.get("message")
                }
            else:
                return {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : "Something went wrong with request."
                }
    if sender_bank.belongs_to_bank_one:
        if payloadClass.send_money_type == "INTRA":
            bank_one_response = BankOneResource.fund_transfer_internal(payloadClass)
            if bank_one_response.get("status") == "success":
                data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description': "Fund Transfer Successful.",
                    'response_detail' : {
                        'cba_reference' : bank_one_response.get('cba_reference'),
                        "nam_payment_reference": bank_one_response.get('cba_reference')
                    }
                }
                log.info(f'Response from SEND MONEY service: {data}')
                return data
            elif bank_one_response.get("status") == "failed":
                return {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : bank_one_response.get("message")
                }
            else:
                return {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : "Something went wrong with request."
                }

        elif payloadClass.send_money_type == "INTER":
            bank_one_response = BankOneResource.fund_transfer_external(payloadClass)
            if bank_one_response.get("status") == "success":
                data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description': "Fund Transfer Successful.",
                    'response_detail' : {
                        'cba_reference' : bank_one_response.get('cba_reference'),
                        "nam_payment_reference": bank_one_response.get('cba_reference')
                    }
                }
                log.info(f'Response from SEND MONEY service: {data}')
                return data
            elif bank_one_response.get("status") == "failed":
                return {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : bank_one_response.get("message")
                }
            else:
                return {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : "Something went wrong with request."
                }

    if payloadClass.send_money_type == 'INTRA':
        url = f'{settings.MONEY_FLEX_OPEN_API_BASE_URL}/intra_ft/1.0.0/intrabank'
        payload = {
            "CreditAccount": payloadClass.destination_account,
            "DebitAccount": payloadClass.sender_account,
            "CompanyCode": payloadClass.sender_institution_code,
            "TransactionAmount": str(payloadClass.amount),
            "TransactionReference": str(payloadClass.transaction_ref),
            "Narration": payloadClass.narration
        }
        log.info(f'Calling SEND MONEY service: SEND MONEY TYPE -> INTRA, URL -> {url} PAYLOAD -> {payload}')
        payload = json.dumps(payload)
    elif payloadClass.send_money_type == 'INTER':
        url = f'{settings.MONEY_FLEX_OPEN_API_BASE_URL}/inter_ft/1.0.0/interbank'

        # get more reciever details from internal dynamic storage
        reciever_details = get_reciever_details(payloadClass.sender_account)
        sender_phone_number = reciever_details.get('SenderPhoneNumber')
        sender_name = reciever_details.get('SenderName')
        sender_bvn = reciever_details.get('SenderBVN')
        reciever_bvn = reciever_details.get('ReceiverBVN')
        reciever_name = reciever_details.get('ReceiverName')
        reciever_kyc_level = reciever_details.get('ReceiverKYCLevel')
        name_enq_ref = reciever_details.get('NameEnquiryRef')

        payload = {
            "SenderAccountNo": payloadClass.sender_account,
            "SenderPhoneNumber": sender_phone_number,
            "SenderName": sender_name,
            "CompanyCode": payloadClass.sender_institution_code,
            "SenderBVN": sender_bvn,
            "NameEnquiryRef": name_enq_ref,
            "ReceiverBVN": reciever_bvn,
            "ReceiverName": reciever_name,
            "ReceiverAccountNo": payloadClass.destination_account,
            "DestinationInstitutionCode": payloadClass.destination_institution_code,
            "ReceiverKYCLevel": reciever_kyc_level,
            "TransactionAmount": str(payloadClass.amount),
            "Narration": payloadClass.narration,
            "TransactionReference": str(payloadClass.transaction_ref),
            "hash": ""
        }
        log.info(f'Calling SEND MONEY service: SEND MONEY TYPE -> INTER, URL -> {url} PAYLOAD -> {payload}')
        payload = json.dumps(payload)
    token = local_retrieve_nam_token(app_code)
    headers ={
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    try:
        response = requests.request('POST', url, headers=headers, data=payload)
        if response.status_code == 401:
            # token has expired or invalid
            token = get_nam_token(bank_alias=app_code)
            update_nam_token(token, app_code)
            headers['Authorization'] = f'Bearer {token}'
            response = requests.request('POST', url, headers=headers, data=payload)
        resp = response.json()
        log.info(f'Response from SEND MONEY service: {resp}')
        if response.status_code == 200 and resp.get('ResponseCode') == '00':
            data = {
                'response_code' : settings.SUCCESS_RESPONSE,
                'response_description': resp.get('ResponseDescription'),
                'response_detail' : {
                    'nam_payment_reference' : resp.get('PaymentReference')
                }
            }
            log.info(f'Response from SEND MONEY service: {data}')
            return data
        else:
            # Get a friendlier description if it exists
            response_description = ''
            if code:= resp.get('ResponseCode'):
                response_description = settings.CORE_BANKING_RESPONSE_CODE.get(code)

            data = {
                "response_code": resp.get("ResponseCode") or settings.FAILED_RESPONSE,
                "response_description": response_description or resp.get("ResponseDescription"),
            }
            log.info(f"Response from SEND MONEY service: {data}")
            return data
    except Exception as e:
        data = {
            'response_code' : settings.FAILED_RESPONSE,
            'response_description' : str(e)
        }
        log.info(f'Response from SEND MONEY service: {data}')
        return data


def fund_transfer_no_prefund(FundTransferClass):
    log = logging.getLogger('ams_cash_out')
    url = settings.SWITCH_BASE_URL
    source_name = FundTransferClass.source_name
    source_inst_code = FundTransferClass.source_inst_code
    source_account = FundTransferClass.source_account
    destination_name = FundTransferClass.destination_name
    destination_inst_code = FundTransferClass.destination_inst_code
    destination_account = FundTransferClass.destination_account
    name_enquiry_ref = FundTransferClass.name_enquiry_reference
    narration = FundTransferClass.narration
    requestID = FundTransferClass.request_id
    amount = FundTransferClass.amount
    fee = FundTransferClass.fee

    try:
        ft_request_payload = \
            {
                "requestID": requestID,
                "destinationInstitutionCode": destination_inst_code,
                "destinationAccountName": destination_name,
                "destinationAccountNumber": destination_account,  # "**********",
                "destinationBVN": "**********",
                "destinationKYCLevel": "1",
                "sourceInstitutionCode": source_inst_code,  # "NIP01",
                "sourceAccountNumber": source_account,  # "**********",
                "prefundWalletDestinationInstitutionCode": "000000",
                "prefundWalletDestinationAccountNumber": "*********",
                "prefundWalletInstitutionCode": FundTransferClass.prefund_inst_code,
                "prefundWalletAccountNumber": FundTransferClass.prefund_account,
                "depositorName": destination_name,
                "transactionLocation": "6.4300747,3.4110715",
                "sopInstitutionCode": FundTransferClass.sop_inst_code,
                "sopAccountNumber": FundTransferClass.sop_account,
                "amount": amount,
                "fee": fee,
                "remarks": narration,
                "hash": "dd369839-7a73-4dd6-8282-55736c90"
            }
        log.info("switch request payload: " + str(ft_request_payload))
        r = requests.post(url, json=ft_request_payload)
        response_data = r.json()
        log.info("switch response payload: " + str(response_data))

        if response_data['responseCode'] == 'RESP_00' or \
                response_data['responseCode']  == 'INLAKS_00':
            data = {
                'response_code': '00',
                'response_description': 'cashin successful',
                'transaction_ref': response_data['transactionRef'],
                'do_tsq': False,
            }
        else:
            if response_data['retry'] == 'false' or response_data['retry'] == False:
                do_tsq = False
                response_code = settings.FAILED_RESPONSE
            elif response_data['retry'] == 'true' or response_data['retry'] == '' or response_data['retry'] == True:
                response_code = settings.PENDING_RESPONSE
                do_tsq = True

            data = {
                'response_code': response_code,
                'response_description': response_data['responseDescription'],
                'do_tsq': do_tsq
            }

        return data
    except Exception as e:
        data = {
            'response_code': settings.FAILED_RESPONSE,
            'response_description': 'error connecting to switch',
            'do_tsq': False
        }
        return data


def send_sms_test(phone_number, message):
    message_from = "VerdantMFB"

    # BASE_SMS_URL = "http://cloud.nuobjects.com/api/send/?user=teezzan&pass=teezzan1234&"

    # url = (
    #     BASE_SMS_URL
    #     + "to="
    #     + phone_number
    #     + "&from="
    #     + message_from
    #     + "&msg="
    #     + message
    # )

    url = "https://app.smartsmssolutions.ng/io/api/client/v1/sms/"

    payload = {
        "token": "Xb6jUSK5D5MHbII6L7FOQkAhWQSEtfa4fOKllukyFd6BX7kxZY",
        "sender": message_from,
        "to": phone_number,
        "message": message,
        "type": "0",
        "routing": "3",
    }

    try:
        # response = requests.get(url)

        response = requests.post(url, data=payload)
        status_code = response.status_code
        # response: int = 200
        if status_code == 200:
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "success",
            }
        else:
            return {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "error generating token",
            }

    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": "error connecting to sms gateway",
        }


def send_sms(phone_number, message, institution_code, message_sender_name, request_id):

    log = logging.getLogger('django')

    url = settings.SMS_SERVICE_URL

    payload = {
        'appId': institution_code,
        'carrier': 'INTERSWITCH',
        'from': message_sender_name,
        'hashValue': '234545665656', #it can be anything
        'message': message,
        'requestId': f'{request_id}',
        'to': validate_phone_number(phone_number)[1:], #phone number should be in country code format, strip the + away
        'institutionCode': institution_code
    }
    log.info(f'FROM SEND SMS: URL -> {url}')
    log.info(f'FROM SEND SMS: PAYLOAD -> {payload}')

    payload = json.dumps(payload)
    log.info(f'FROM SEND SMS: PAYLOAD JSON-> {payload}')

    headers = {
        'Content-Type': 'application/json'
    }

    try:
        resp = requests.request('POST', url, headers=headers, data=payload)
        resp = resp.json()
        log.info(f'FROM SEND SMS: SERVICE RESPONSE-> {resp}')
        if resp.get('responseCode') == '00':
            return {'response_code': settings.SUCCESS_RESPONSE, 'response_description': 'success'}
        else:
            return {'response_code': settings.FAILED_RESPONSE, 'response_description': resp.get('responseDescription', 'Unable to send sms')}
    except Exception as e:
        return {'response_code': settings.FAILED_RESPONSE, 'response_description': 'error connecting to sms gateway'}



def send_email(email_args:dict, email_type=None):
    '''
        Forward the mail parameters to the Mail server gateway
    '''
    email = Mail(
        from_email=email_args.get('email_from'),
        to_emails=email_args.get('to'),
        subject=email_args.get('subject')
    )
    if email_type == 'VERIFY_EMAIL':
        email.template_id = 'd-e8d9bd42aae846e4ae98a06131bd1dea'
        email.dynamic_template_data = {
            'otp': email_args.get('otp'),
            'subject': email_args.get('subject'),
            'message': email_args.get('message'),
            'customer_first_name': email_args.get('customer_first_name'),
            'img_url': email_args.get('img_url'),
            'foreground': email_args.get('foreground'),
            'background': email_args.get('background')
        }
    elif email_type == 'RESET_PASSWORD':
        email.template_id = 'd-9cdbbe45dc0d426398114e6abf82dc16'
        email.dynamic_template_data = {
            'subject': email_args.get('subject'),
            'message': email_args.get('message'),
            'customer_first_name': email_args.get('customer_first_name'),
            'img_url': email_args.get('img_url'),
            'foreground': email_args.get('foreground'),
            'background': email_args.get('background'),
            'otp': email_args.get('otp')
        }
    elif email_type == 'LOGIN_NOTIFICATION':
        email.template_id = 'd-61f7764d49c94a54808092bb8f60d812'
        email.dynamic_template_data = {
            'subject': email_args.get('subject'),
            'message': email_args.get('message'),
            'customer_first_name': email_args.get('customer_first_name'),
            'img_url': email_args.get('img_url'),
            'foreground': email_args.get('foreground'),
            'background': email_args.get('background'),
            'email_from': email_args.get('email_from'),
            'phone_number': email_args.get('phone_number'),
        }
    elif email_type == "BILLING_SHUTDOWN_ALERTS":
        email.template_id = "d-98b861326e474bef9bcacd3c4c8ce908"
        email.dynamic_template_data = {
            # 'subject': email_args.get('subject'),
            'message': email_args.get('message'),
            "img_url": email_args.get("img_url"),
            "background": email_args.get("background"),
            "email_from": email_args.get("email_from"),
            "bank_name": email_args.get("bank_name"),
            'foreground': email_args.get('foreground'),
            'otp': email_args.get('otp'),
        }

    elif email_type == 'BILLING':
        with open(email_args.get('file_path'), 'rb') as f:
            data = f.read()
            f.close()
        encoded_file = base64.b64encode(data).decode()
        attachment = Attachment()
        attachment.file_content = FileContent(encoded_file)
        attachment.file_type = FileType(email_args.get('file_type'))
        attachment.file_name = FileName(email_args.get('file_name'))
        attachment.disposition = Disposition('attachment')

        email.attachment = attachment

        email.template_id = 'd-e91d08ea2dfc498ea827e3586d586aaf'
        email.dynamic_template_data = {
            'subject': email_args.get('subject'),
            'message': email_args.get('message'),
            'customer_first_name': email_args.get('customer_first_name'),
            'img_url': email_args.get('img_url'),
            'foreground': email_args.get('foreground'),
            'background': email_args.get('background'),
            'total_amount': email_args.get('total_amount'),
            'total_amount_words': email_args.get('total_amount_words'),
            'no_of_transactions': email_args.get('no_of_transactions'),
            'status': email_args.get('status'),
            'status_color': email_args.get('status_color')
        }
    elif email_type == "REPORTING" or email_type == "STATEMENT":
        with open(email_args.get("file_path"), "rb") as f:
            data = f.read()
            f.close()
        encoded_file = base64.b64encode(data).decode()
        attachment = Attachment()
        attachment.file_content = FileContent(encoded_file)
        attachment.file_type = FileType(email_args.get("file_type"))
        attachment.file_name = FileName(email_args.get("file_name"))
        attachment.disposition = Disposition("attachment")

        email.attachment = attachment

        email.template_id = (
            "d-21c8443d28a145cb993b3d87a6b76f84"
            if email_type == "REPORTING"
            else "d-54512ce9ebf844489948828d285a9848"
        )
        email.dynamic_template_data = {
            "subject": email_args.get("subject"),
            "message": email_args.get("message"),
            "admin_name": email_args.get("admin_name"),
            "img_url": email_args.get("img_url"),
            "foreground": email_args.get("foreground"),
            "background": email_args.get("background"),
        }
    elif email_type == 'INTERNAL_NOTIFICATION':
        email = Mail(
            from_email=email_args.get('email_from'),
            to_emails=email_args.get('to'),
            subject=email_args.get('subject'),
            plain_text_content=email_args.get('message')
        )
    elif email_type == "ANNOUNCEMENT":
        email.template_id = "d-1601e6e82e984e8ba54e30adef6af440"
        email.dynamic_template_data = {
            "name": email_args.get("name"),
            "img_url": email_args.get("img_url"),
            "bg_col": email_args.get("bg_col"),
            "email_from": email_args.get("email_from"),
            "email": email_args.get("email"),
            "bank_name": email_args.get("bank_name"),
        }
    elif email_type == "ACCOUNT_OPENING":
        email.template_id = "d-0997299e6cd148f5b8562b3ccd510303"
        email.dynamic_template_data = {
            "otp": email_args.get("account_number"),
            "subject": email_args.get("subject"),
            "message": email_args.get("message"),
            "customer_first_name": email_args.get("customer_first_name"),
            "img_url": email_args.get("img_url"),
            "foreground": email_args.get("foreground"),
            "background": email_args.get("background"),
            "account_name": email_args.get("account_name"),
            "account_number": email_args.get("account_number"),
        }
    elif email_type == "ACCOUNT_OPENING_NOTIFICATION":
        email.template_id = "d-43d7ee51fa364672bcf81f2c5ec013a6"
        email.dynamic_template_data = {
            "otp": email_args.get("account_number"),
            "subject": email_args.get("subject"),
            "message": email_args.get("message"),
            "customer_first_name": email_args.get("customer_first_name"),
            "img_url": email_args.get("img_url"),
            "foreground": email_args.get("foreground"),
            "background": email_args.get("background"),
        }
    elif email_type == "QR_NOTIFICATION":
        email = Mail(
            from_email=email_args.get("email_from"),
            to_emails=email_args.get("to"),
            subject=email_args.get("subject"),
            plain_text_content=email_args.get("message"),
        )
        with open(email_args.get("file_path"), "rb") as f:
            data = f.read()
            f.close()
        encoded_file = base64.b64encode(data).decode()
        attachment = Attachment()
        attachment.file_content = FileContent(encoded_file)
        attachment.file_type = FileType(email_args.get("file_type"))
        attachment.file_name = FileName(email_args.get("file_name"))
        attachment.disposition = Disposition("attachment")

        email.attachment = attachment

    try:
        sg = SendGridAPIClient(settings.S_G_KEY)
        resp = sg.send(email)
        if resp.status_code == 202:
            data = {
                'response_code' : settings.SUCCESS_RESPONSE,
                'response_description' : 'Email Sent Successfully',
                'response_detail' : f'Email to {email_args.get("to")} sent successfully'
            }
            return data
        else:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description': 'Error Sending Email',
                'response_detail' : f'{resp.body}'
            }
            return data
    except Exception as e:
        data = {
            'response_code' : settings.FAILED_RESPONSE,
            'response_description' : 'Error Sending Email',
            "response_detail": f"{e}",
        }
        return data


def get_package_options(code, institution_code="000001"):
    """
    for biller items with different packages we use the biller code to query for the packages,
    clean up the response and return
    """
    log = logging.getLogger("django")
    if settings.APP_ENV == "development":
        try:
            biller = AirtimeBiller.objects.get(biller_code=code)
            if biller.type == "data":
                return mock_get_package_options("data")
        except AirtimeBiller.DoesNotExist:
            return mock_get_package_options("bills_pay")
    if settings.APP_ENV == "production":
        url = f"{settings.UAT_BILL_PAYMENT_SERVICE_BASE_URL}/interswitch/{institution_code}/paymentitems/{code}"
    try:
        resp = requests.get(url)
        log.info(f"response from calling get_package_options {resp}")
        resp = resp.json()
        log.info(f"response from calling get_package_options {resp}")
        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Get Package Options Successful",
                "response_detail": {
                    "package_options": clean_package_options(
                        resp.get("paymentItems", [])
                    )
                },
            }
    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": "Unable to Retrieve Package Options",
            "response_detail": str(e),
        }


def bill_payment_advice(payload_class, type=None, institution_code="000001"):
    """
    call the bill payment advice service, this service handles both airtime and bill payment
    transactions
    """
    if type == "airtime_data":
        log = logging.getLogger("omni_airtime_data")
    elif type == "bill_payment":
        log = logging.getLogger("omni_bill_payment")
    else:
        log = logging.getLogger("django")

    if settings.APP_ENV == "development":
        return {
            "response_code": settings.SUCCESS_RESPONSE,
            "response_description": "Bill Payment Advice Successful",
            "response_detail": {
                "switch_ref": random.randrange(********00, **********),
                "recharge_pin": random.randrange(********00, **********),
            },
        }
    else:
        url = f"{settings.UAT_BILL_PAYMENT_SERVICE_BASE_URL}/interswitch/{institution_code}/paymentadvice"

        payload = {
            "amount": str(payload_class.amount),
            "customerEmail": payload_class.sender_email,
            "customerId": payload_class.billing_unique_number,
            "customerMobile": payload_class.sender_mobile,
            "paymentCode": payload_class.payment_code,
            "requestId": payload_class.request_id,
        }

    log.info(f"Calling BILLS PAY service for {type}: URL -> {url} PAYLOAD -> {payload}")
    payload = json.dumps(payload)

    headers = {"accept": "*/*", "Content-Type": "application/json"}

    try:
        resp = requests.request("POST", url, headers=headers, data=payload)
        resp = resp.json()
        log.info(f"Response from BILLS PAY service ({type}): {resp}")
        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Bill Payment Advice Successful",
                "response_detail": {
                    "switch_ref": resp.get("transactionRef", ""),
                    "recharge_pin": resp.get("rechargePIN", ""),
                },
            }
            log.info(f"Response from BILLS PAY service ({type}): {data}")
            return data
        elif resp.get("responseCode") in ["21", "29", "57", "61", "17"]:
            """
            57 Routing Error
            61 System Malfunction
            29 Unknown Status
            17 No mapping found for Response Code
            """
            data = {
                "response_code": "02",
                "response_description": "Bill Payment Advice Possible Service Timeout",
                "response_detail": {
                    "switch_ref": resp.get("transactionRef", ""),
                    "recharge_pin": resp.get("rechargePIN", ""),
                },
            }
            log.info(f"Response from BILLS PAY service ({type}): {data}")
            return data
        data = {
            "response_code": resp.get("responseCode") or settings.FAILED_RESPONSE,
            "response_description": resp.get(
                "responseDescription", "Unable To Complete Transaction"
            ),
        }
        log.info(f"Response from BILLS PAY service ({type}): {data}")
        return data
    except Exception as e:
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Unable To Complete Transaction -> ERROR : {e}",
        }
        log.info(f"Response from BILLS PAY service ({type}): {data}")
        return data


def bill_payment_advice_get_tsq(
    switch_ref: str, type: str = "", institution_code: str = "000001"
) -> tuple:
    """
    Calls the bill payment advice for transactions status query and returns the status
    of the transaction
    """
    if type == "airtime_data":
        log = logging.getLogger("omni_airtime_data")
    elif type == "bill_payment":
        log = logging.getLogger("omni_bill_payment")
    else:
        log = logging.getLogger("django")

    try:
        if settings.APP_ENV != "production":
            # response_code = settings.SUCCESS_RESPONSE
            response_code = settings.FAILED_RESPONSE
            recharge_pin = ''.join(str(random.randint(0, 9)) for _ in range(15))
            log.info(f"Response from BILLS PAY TSQ service ({type}): response code -> {response_code}")
            return (response_code, recharge_pin)

        else:
            url = f"{settings.UAT_BILL_PAYMENT_SERVICE_BASE_URL}/interswitch/{institution_code}/tsq/advice/{switch_ref}"
            log.info(f"Calling BILLS PAY TSQ service for {type}: URL -> {url} REFERENCE -> {switch_ref}")
            resp = requests.get(url)
            resp = resp.json()
            log.info(f"Response from BILLS PAY TSQ service ({type}): {resp}")
            if response_code := resp.get("responseCode"):
                recharge_pin = resp.get("serviceCode", "")
                log.info(
                    f"Response from BILLS PAY TSQ service ({type}): response code -> {response_code}"
                )
                return (response_code, recharge_pin)
            else:
                return ("", "")

            # generate some random number pin and return random response code from a list of 00, 01, 02
    #         if status := resp.get("status"):
    #             print(f"STATUS >> {status}")
    #             recharge_pin = resp.get("serviceCode", "")
    #             print(f"RECH PIN >> {recharge_pin}")
    #             log.info(
    #                 f"Response from BILLS PAY TSQ service ({type}): status -> {status}"
    #             )
    #             return (status, recharge_pin)
    #         else:
    #             return ("", "")
    except Exception as e:
        log.info(f"Error from BILLS PAY TSQ service ({type}): status -> {e}")
        return ("", "")


def validate_customer_unique_number(
    customer_unique_number, payment_code, institution_code="000001"
):
    """
    Validates customer unique number attached to bills payment
    """
    log = logging.getLogger("omni_bill_payment")

    if settings.APP_ENV == "development":
        return {
            "response_code": settings.SUCCESS_RESPONSE,
            "response_description": "Customer Unique Number Validation Successful",
            "customer_unique_number": customer_unique_number,
            "payment_code": payment_code,
            "customer_name": "Adesare Olugbagi",
        }
    else:
        url = f"{settings.UAT_BILL_PAYMENT_SERVICE_BASE_URL}/interswitch/{institution_code}/validate/customer"

        payload = {
            "customers": [
                {"customerId": customer_unique_number, "paymentCode": payment_code}
            ]
        }

    log.info(
        f"Calling BILLS PAY service for customer unique number validation: URL -> {url} PAYLOAD -> {payload}"
    )
    payload = json.dumps(payload)

    headers = {"accept": "*/*", "Content-Type": "application/json"}

    try:
        resp = requests.request("POST", url, headers=headers, data=payload)
        resp = resp.json()
        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": resp.get("responseDescription")
                or "Customer Unique Number Validation Successful",
                "customer_unique_number": resp.get("customerId"),
                "payment_code": resp.get("paymentCode"),
                "customer_name": resp.get("fullName"),
            }
            log.info(
                f"Response from BILLS PAY service for customer unique number validation: URL -> {data}"
            )
            return data
        else:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp.get("responseDescription")
                or "Unable to Resolve Request",
            }
            return data
    except Exception as e:
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": "Unable to Resolve Request",
            "response_detail": str(e),
        }
        return data


def verify_paystack_payment(reference, email, amount, refund, last_four_digits):

    if settings.APP_ENV == "development":
        return {
            "response_code": settings.SUCCESS_RESPONSE,
            "response_description": "Payment Reference Verified",
            "response_detail": "Payment Reference was Verified Successfully",
        }

    else:
        url = f"{settings.PAYSTACK_PAYMENT_VERIFY_URL}/paystack/card/tokenize"
        payload = {
            "reference": reference,
            "email": email,
            "amount": amount,
            "last4": last_four_digits,
            "refund": refund
        }
        headers = {"accept": "*/*", "Content-Type": "application/json"}
        payload = json.dumps(payload)

        try:
            resp = requests.request("POST", url, headers=headers, data=payload)
            resp = resp.json()
            if resp.get("responseCode") == "201":
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Payment Reference Verified",
                    "response_detail": "Payment Reference was Verified Successfully",
                }
                return data
            else:
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_description": resp.get("responseDescription")
                    or "Unable to Resolve Request",
                }
                return data
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Unable to Resolve Request",
                "response_detail": str(e),
            }
            return data


'''def send_sms(phone_number, message):
    phone_number = str(phone_number)
    url = settings.SMS_URL
    request_id = f'{random.randrange(1, 10 ** 3):08}'
    key = 'inlaks$1'
    sender = 'NAMBUIT'
    string_to_hash = request_id + sender + phone_number + message
    hashed_string = hmac.new(key.encode('utf-8'), string_to_hash.encode('utf-8'), hashlib.sha512).hexdigest()

    try:

        request_payload = \
            {
                "appId": "nam_agency",
                "carrier": "interswitch",
                "from": sender,
                "hashValue": hashed_string,
                "message": message,
                "requestId": request_id,
                "to": phone_number
            }

        # print(str(request_payload))
        r = requests.post(url, json=request_payload)
        r.request.headers['Content-Type']
        'application/json'
        response_data = r.json()
        # print(str(response_data))
        if response_data['responseCode'] == 'INLAKS_87' or response_data['responseCode'] == 'INLAKS_00':
            data = {'response_code': settings.SUCCESS_RESPONSE, 'response_description': 'success'}
        else:
            data = {'response_code': '01', 'response_description': 'error generating token'}
        return data
    except Exception as e:
        return {'response_code': settings.FAILED_RESPONSE, 'response_description': 'error connecting to sms gateway'}'''


# noinspection PyBroadException
def account_opening(account: AgentOnboardedAccount, other_details_dict: dict) -> dict:
    """
    Calls the Nambuit Account Opening service for Partnered Banks within Nambuit
    """
    log = logging.getLogger("django")
    company_code = account.agent.bank.code # NG1590001
    #path = "/create_new_account/1.0.0/v1/account/openAccount"
    create_customer_path = f"/nambuit_tier1_create_customer/0.0.1/create/tier1/{company_code}/customer"
    create_customer_url = f"{settings.MONEY_FLEX_OPEN_API_BASE_URL}{create_customer_path}"
    log.info(f'url -> {create_customer_url}')

    bvn = BVN.objects.get(bvn=account.bvn)
    if settings.APP_ENV == "developments":
        data = set_response_data(
            code=settings.SUCCESS_RESPONSE,
            description="Account Created Successfully",
            detail={
                "account_number": random.randrange(********00, **********),
                "customer_number": random.randrange(********00, **********),
            },
        )
        return data

    print(f"BANK : {account.agent.bank.name}")
    if account.agent.bank.belongs_to_nibss_core_banking:
        nibss_resp = NIBSSResource.customer_account_opening(
            bvn=bvn,
            account=account,
            other_details_dict=other_details_dict
        )
        if nibss_resp.get("status") == "success":
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Created Successfully",
                detail={
                    "account_number": nibss_resp.get("account_number"),
                    "customer_number": nibss_resp.get("customer_number"),
                },
              )
            return data
        elif nibss_resp.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : nibss_resp.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Something went wrong with request."
            }
    elif account.agent.bank.belongs_to_bank_one:
        bank_one_resp = BankOneResource.create_customer_and_account(
            bvn=bvn,
            account_type=account.account_type,
            account=account,
            other_details_dict=other_details_dict
        )
        if bank_one_resp.get("status") == "success":
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Created Successfully",
                detail={
                    "account_number": bank_one_resp.get("account_number"),
                    "customer_number": bank_one_resp.get("customer_number"),
                },
              )
            return data
        elif bank_one_resp.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : bank_one_resp.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Something went wrong with request."
            }

    """payload = {
        "bvn": account.bvn,
        "dateOfBirth": account.dob,
        "gender": "M" if account.gender == "MALE" else "F",
        "CompanyCode": account.agent.bank.code,
        "phone": account.phone_number,
        "email": account.email,
        "homeAddress": account.city,
        "firstName": account.first_name,
        "lastName": account.last_name,
        "middleName": account.middle_name,
        "city": account.city,
        "state": account.state,
        "imageType": "image/jpeg",
        "signatureType": "image/jpeg",
        "image": account.profile_picture_base_64,
        "signature": account.signature_base_64,
    }"""


    create_customer_payload = {
        "bvn": account.bvn,
        "city": account.city,
        "dateOfBirth": account.dob,
        "email": account.email,
        "firstName":  account.first_name,
        "gender": bvn.gender.upper(),
        "homeAddress":  account.address,
        "lastName":  account.last_name,
        "middleName": account.middle_name if account.middle_name else account.first_name,
        "phone": account.phone_number,
        "state": account.state,
    }

    log.info(f'payload -> {create_customer_payload}')

    #payload = json.dumps(payload)
    create_customer_payload = json.dumps(create_customer_payload)

    log.info(f'JSON payload -> {create_customer_payload}')

    token = local_retrieve_nam_token(account.agent.bank.alias)

    log.info(f'token retrieved -> {token}')

    headers = {
        "Authorization": f"Bearer {token}",
        'Content-Type': 'application/json'
    }

    try:
        log.info(
            f"Calling create customer service, url -> {create_customer_url}, with payload -> {create_customer_payload}"
        )

        response = requests.request("POST", create_customer_url, headers=headers, data=create_customer_payload)

        if response.status_code == 401:
            # token has expired or invalid
            token = get_nam_token(bank_alias=account.agent.bank.alias)
            update_nam_token(token, account.agent.bank.alias)
            headers["Authorization"] = f"Bearer {token}"
            response = requests.request("POST", create_customer_url, headers=headers, data=create_customer_payload)

        cust_resp = response.json()

        log.info(f"Response from create customer service -> {cust_resp}")

        if response.status_code == 200 and cust_resp.get("responseCode") == "00":

            customer_no = cust_resp.get("customerNo")
            accountName =  account.last_name + " " + account.first_name

            log.info(f'customer no -> {customer_no}')
            log.info(f'account name -> {accountName}')

            # create_account_path = "/nambuit_tier1_create_account/0.0.1/create/tier1/"
            # + company_code + "/account?accountCategory=6001&customerNumber=" + customer_no + "&accountName=" + accountName

            # remove this and add to database

            # avemaria account category
            company_codes_list = ['NG1520001', 'NG0160001']  # Anchorage and Ave Maria

            if company_code in company_codes_list:
                account_categ = '6047'
            else:
                account_categ = '6001'


            create_account_path = f'/nambuit_tier1_create_account/0.0.1/create/tier1/{company_code}/account?accountCategory={account_categ}&customerNumber={customer_no}&accountName={accountName}'
            log.info(f'path -> {create_account_path}')

            create_account_url = f"{settings.MONEY_FLEX_OPEN_API_BASE_URL}{create_account_path}"

            log.info(f"Calling create account service, url -> {create_account_url}, with payload -> {create_account_url}")

            create_account_response = requests.request("PUT", create_account_url, headers=headers)
            act_resp = create_account_response.json()
            log.info(f"Response from create account service -> {act_resp}")

            if create_account_response.status_code == 200 and act_resp.get("responseCode") == "00":
                account_no = act_resp.get("accountNo")
                data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Created Successfully",
                detail={
                    "account_number": account_no,
                    "customer_number": customer_no,
                },
              )
            else:
                data = set_response_data(
                code=settings.FAILED_RESPONSE, description=act_resp, detail=act_resp
            )


        else:
            data = set_response_data(
                code=settings.FAILED_RESPONSE, description=cust_resp, detail=cust_resp
            )
        return data
    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Connecting to Switch: {e}",
        }




def get_balance_enquiry(
    bank: Bank, account_number: str, institution_code: str, app_code=None, b_token=None
) -> dict:
    """
    Check cache for balance and then determine whether to call the
    Nambuit Balance Enquiry service for Partnered Banks within Nambuit
    """

    if settings.APP_ENV == "developments":
        return {
            "response_code": settings.SUCCESS_RESPONSE,
            "response_description": {
                "descripiton": "Balance Enquiry Retreived Successfully",
                "available_balance": "7852.00",
                "book_balance": "7852.00",
            },
        }

    if bank.belongs_to_nibss_core_banking:
        print("CALLING NIBSS >>")
        nibbss_resp = NIBSSResource.account_details_enquiry(account_number=account_number)
        if nibbss_resp.get("status") == "success":
            data = {
                'response_code': settings.SUCCESS_RESPONSE,
                'response_description': {
                    "descripiton": "Balance Enquiry Retreived Successfully",
                    'available_balance': str(nibbss_resp.get('account_balance')),
                    'account_number': account_number,
                    'book_balance': str(nibbss_resp.get("book_balance"))
                }, 
            }
            return data
        elif nibbss_resp.get("status") == "empty":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Account Details Not Found"
            }
        elif nibbss_resp.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : nibbss_resp.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Something went wrong with request."
            }
    elif bank.belongs_to_bank_one:
        print("CALLING BANKONE >>> ")
        bank_one_response = BankOneResource.get_account_enquiry(account_number=account_number)
        if bank_one_response.get("status") == "success":
            # Convert kobo to naira by dividing by 100 and formatting to 2 decimal places
            available_balance_kobo = bank_one_response.get('account_balance')
            book_balance_kobo = bank_one_response.get("book_balance")

            # Convert to float, divide by 100, then format to 2 decimal places
            available_balance_naira = "{:.2f}".format(float(available_balance_kobo) / 100) if available_balance_kobo else "0.00"
            book_balance_naira = "{:.2f}".format(float(book_balance_kobo) / 100) if book_balance_kobo else "0.00"

            data = {
                'response_code': settings.SUCCESS_RESPONSE,
                'response_description': {
                    "descripiton": "Balance Enquiry Retreived Successfully",
                    'available_balance': available_balance_naira,
                    'account_number': account_number,
                    'book_balance': book_balance_naira
                },

            }
            return data
        elif bank_one_response.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : bank_one_response.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : bank_one_response.get("message")
            }
        
    elif bank.belongs_to_dsgs_core_banking:
        print("CALLING DSGS >>> ")
        dsgs_response = DSGSResource.get_account_enquiry(account_number=account_number)
        if dsgs_response.get("status") == "success":
            
            data = {
                'response_code': settings.SUCCESS_RESPONSE,
                'response_description': {
                    "descripiton": "Balance Enquiry Retreived Successfully",
                    'available_balance': dsgs_response.get("available_balance"),
                    'account_number': account_number,
                    'book_balance': dsgs_response.get("book_balance"),
                },

            }
            return data
        elif bank_one_response.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : dsgs_response.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : dsgs_response.get("message")
            }
        


    if b_token:
        balance_enquiry, make_request = retrieve_balance_enquiry(
            b_token, account_number
        )
        if not make_request and balance_enquiry is not None:
            return balance_enquiry

    #path = f"/get_account_balances/1.0.0/balances"
    path = f"/nambuit_intra_balance_inquiry/0.0.1/balance/enquiry/" + institution_code + "/" + account_number
    url = f"{settings.MONEY_FLEX_OPEN_API_BASE_URL}{path}"

    #https://openapi.inlaks-nambuit.com:8248/nambuit_intra_balance_inquiry/0.0.1/balance/enquiry/NG0530001/**********

    payload = {"accountNo": account_number, "CompanyCode": institution_code}
    payload = json.dumps(payload)

    token = local_retrieve_nam_token(app_code)

    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json",
    }
    try:
        response = requests.request("GET", url, headers=headers)
        if response.status_code == 401:
            # token has expired or invalid
            token = get_nam_token(bank_alias=app_code)
            update_nam_token(token, app_code)
            headers["Authorization"] = f"Bearer {token}"
            response = requests.request("GET", url, headers=headers)
        resp = response.json()

        if response.status_code == 200 and resp.get("responseCode") == "00":
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": {
                    "descripiton": resp["responseMessage"],
                    "available_balance": f'{resp["availableBalance"]}',
                    "book_balance": f'{resp["bookBalance"]}',
                },
            }
            if b_token:
                update_balance_enquiry(b_token, account_number, data)
            return data
        else:
            return {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp,
            }
    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Connecting to Switch: {e}",
        }


def get_wallet_balance(wallet_no: str):
    """
    for digital bank we return the balance on the the wallet, this calls the MIFOS service.
    """
    url = f"{settings.WALLET_SERVICE_BASE_URL}/wallet/inquiry/balance/{wallet_no}"

    print(url)

    try:
        resp = requests.get(url)
        resp = resp.json()

        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": {
                    "descripiton": resp["responseDescription"],
                    "available_balance": f'{resp["availableBalance"]}',
                    "book_balance": f'{resp["ledgerBalance"]}',
                },
            }
        else:
            return {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp,
            }
    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Connecting to Switch: {e}",
        }


def get_name_enquiry(sender_bank: Bank, account_number:str, institution_code:str, sender_account_number=None, sender_institution_code=None, app_code=None, type='INTRA') -> dict:
    '''
        Calls the Nambuit Name Enquiry service for Intra and Intra bank. It defaults to INTRA
    '''
    log = logging.getLogger("django")
    # if settings.APP_ENV != "production":
    #     return {
    #         "response_code": settings.SUCCESS_RESPONSE,
    #         "response_description": {
    #             "account_name": "Adejare Tijani",
    #             "bvn": "***********",
    #             "name_enquiry_ref": "0000",
    #             "email": "<EMAIL>",
    #             "phone_number": "***********",
    #         },
    #     }
    if sender_bank.belongs_to_nibss_core_banking:
        if type == "INTRA":
            nibbss_response = NIBSSResource.account_details_enquiry(account_number=account_number)
        else:
            nibbss_response = NIBSSResource.name_enq(account_number, institution_code)

        if nibbss_response.get("status") == "success":
            return {
                'response_code' : settings.SUCCESS_RESPONSE,
                'response_description': {
                    'account_number': nibbss_response.get('account_number') or '',
                    'account_name': nibbss_response.get('account_name') or '',
                    'customer_no': nibbss_response.get('account_customer_id') or '',
                    'nip_session_id': nibbss_response.get('nip_session_id') or '',
                    'name_enquiry_ref' : '0000'
                }
            }

        elif nibbss_response.get("status") == "empty":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Account Details Not Found"
            }

        elif nibbss_response.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : nibbss_response.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Something went wrong with request."
            }
    elif sender_bank.belongs_to_bank_one:
        if type == "INTRA":
            bank_one_response = BankOneResource.get_account_enquiry(account_number=account_number)
        else:
            dest_bank = Bank.objects.filter(Q(nip_code=institution_code) | Q(code=institution_code)).first()
            bank_one_response = BankOneResource.get_name_enquiry(account_number=account_number, bank_code=dest_bank.code)
        if bank_one_response.get("status") == "success":
            return {
                'response_code' : settings.SUCCESS_RESPONSE,
                'response_description': {
                    # 'email' : bank_one_response.get('account_email') or '',
                    'account_number': account_number,
                    'account_name' : bank_one_response.get('account_name') or '',
                    'customer_no' : bank_one_response.get('account_customer_id') or '',
                    'nip_session_id': bank_one_response.get('nip_session_id') or '',
                    'name_enquiry_ref' : '0000'
                }
            }

        elif bank_one_response.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : bank_one_response.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Something went wrong with request."
            }
    
    elif sender_bank.belongs_to_dsgs_core_banking:
       
        dsgs_response = DSGSResource.get_name_enquiry(account_number=account_number)
        if dsgs_response.get("status") == "success":
            return {
                'response_code' : settings.SUCCESS_RESPONSE,
                'response_description': {
                    # 'email' : bank_one_response.get('account_email') or '',
                    'account_number': account_number,
                    'account_name' : dsgs_response.get('account_name') or '',
                    'customer_no' : dsgs_response.get('account_customer_id') or '',
                    'name_enquiry_ref' : '0000'
                }
            }

        elif dsgs_response.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : dsgs_response.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Something went wrong with request."
            }

    if type == 'INTRA':
        url = f'{settings.MONEY_FLEX_OPEN_API_BASE_URL}/intra_bank_name_inquiry/1.0.0/v1/transfer/intrabanknameenquiry'
        payload = {
            'accountNo': account_number,
            'CompanyCode': institution_code
        }
        payload = json.dumps(payload)
    elif type == 'INTER' and sender_account_number and sender_institution_code:
        bank = Bank.objects.filter(Q(nip_code=institution_code) | Q(code=institution_code))
        bank = bank[0]
        url = f'{settings.MONEY_FLEX_OPEN_API_BASE_URL}/inter_bank_name_inquiry/1.0.0/v1/transfer/interbanknameenquiry'
        payload = {
            "senderAccountNo": sender_account_number,
            "destinationInstitutionCode": bank.nip_code,
            "receiverAccountNo":account_number,
            "CompanyCode":sender_institution_code
        }
        payload = json.dumps(payload)

    token = local_retrieve_nam_token(app_code)

    headers ={
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    try:
        response = requests.request('POST', url, headers=headers, data=payload)
        if response.status_code == 401:
            # token has expired or invalid
            token = get_nam_token(bank_alias=app_code)
            update_nam_token(token, app_code)
            headers['Authorization'] = f'Bearer {token}'
            response = requests.request('POST', url, headers=headers, data=payload)
        resp = response.json()
        #print(headers)
        #print(payload)
        #log.info("INTER NE RESP >>" +json.dumps(resp))
        if response.status_code == 200 and resp.get('ResponseCode') == '00':
            if type == 'INTER':
                # populate local dynamic storage key is set to sender_account_number
                add_reciever_details(f'{sender_account_number}', {**resp, **{'reciever_account': account_number}})
            return {
                'response_code' : settings.SUCCESS_RESPONSE,
                'response_description': {
                    'email' : resp.get('Email') or '',
                    'bvn' : resp.get('BankVerificationNumber') or resp.get('ReceiverBVN'),
                    'phone_number' : resp.get('PhoneNumber') or resp.get('SenderPhoneNumber'),
                    'account_name' : resp.get('CustomerName') or resp.get('ReceiverName'),
                    'customer_no' : resp.get('CustomerNo') or '',
                    'name_enquiry_ref' : resp.get('NameEnquiryRef', '0000')
                }
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : resp.get('ResponseDescription')
            }
    except Exception as e:
        return {
            'response_code' : settings.FAILED_RESPONSE,
            'response_description' : f'Error Connecting to Switch: {e}'
        }


def nip_name_enquiry(account_number: str, bank: Bank):
    """
    Call NIP outward for inter bank name enquiry. this is to be used mainly by
    the wallet service.
    """
    log = logging.getLogger("django")
    url = f"{settings.NIP_OUTWARD_NAME_ENQUIRY_BASE_URL}/v1/out/nameenquiry/{bank.nip_code}/C01/{account_number}"

    if settings.APP_ENV != "production":
        return {
            "response_code": settings.SUCCESS_RESPONSE,
            "response_description": {
                "account_name": "Adejare Tijani",
                "bvn": "***********",
                "name_enquiry_ref": "0000",
            },
        }

    try:
        log.info(f"Calling nip name enquiry -> {url}")
        resp = requests.get(url)
        resp = resp.json()
        log.info(f"Response from nip name enquiry service -> {resp}")
        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": {
                    "account_name": resp.get("accountName"),
                    "bvn": resp.get("bankVerificationNumber"),
                    "name_enquiry_ref": resp.get("transactionRef"),
                },
            }
        else:
            return {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp.get(
                    "ResponseDescription", "Unable To Retrieve Name For Account"
                ),
            }
    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Connecting to NIP OUTWARD SERVICE: {e}",
        }


def get_wallet_name_enquiry(account_number: str, bank: Bank, name_enq_type):
    """
    returns the name enquiry for account numberm this could either be intra or
    inter, for intra we check the internal database and for inter we call nip to
    retrieve name enquiry details
    """
    try:
        if name_enq_type == "INTRA":
            account = CustomerAccount.objects.get(
                account_number=account_number, bank=bank
            )
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": {
                    "email": account.profile.customer.email,
                    "bvn": account.profile.bvn,
                    "phone_number": account.profile.phone_number,
                    "account_name": f"{account.profile.customer.first_name} {account.profile.customer.last_name}",
                    "name_enquiry_ref": "0000",
                },
            }
        else:
            # call nip for this
            return nip_name_enquiry(account_number, bank)
    except CustomerAccount.DoesNotExist:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": "Customer Account Does Not Exist",
        }

def get_account_transactions(query_params, account_number, app_code, institution_code):
    '''
        Call to the core banking to get the account history for a particular account
    '''
    url = f'{settings.MONEY_FLEX_OPEN_API_BASE_URL}/get_account_transactions/1.0.0/v1/account/transactions/search'
    payload = {
        'accountNo': account_number,
        'startDate': query_params.get('start_date'),
        'CompanyCode': institution_code,
        'endDate': query_params.get('end_date')
    }
    payload = json.dumps(payload)

    token = local_retrieve_nam_token(app_code)

    headers ={
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    try:
        response = requests.request('POST', url, headers=headers, data=payload)
        if response.status_code == 401:
            # token has expired or invalid
            token = get_nam_token(bank_alias=app_code)
            update_nam_token(token, app_code)
            headers['Authorization'] = f'Bearer {token}'
            response = requests.request('POST', url, headers=headers, data=payload)
        resp = response.json()
        if response.status_code == 200 and resp.get('ResponseCode') == '00':
            return {
                'response_code': settings.SUCCESS_RESPONSE,
                'response_description': resp.get('ResponseDescription'),
                'transactions': resp.get('transactions')
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : resp.get('ResponseDescription')
            }

    except Exception as e:
        return {
            'response_code' : settings.FAILED_RESPONSE,
            'response_description' : f'Error Connecting to Switch: {e}'
        }


def get_mfs_banks(country_code: str) -> dict:
    """
    calls the mfs service for list of banks in a particular country
    """
    url = f"{settings.MFS_BASE_URL}/general/banks/{country_code}"

    try:
        resp = requests.get(url)
        resp = resp.json()

        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            banks = clean_mfs_banks(resp.get("banks", []))
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="success",
                detail={"mfs_banks": banks},
            )
            return data
        else:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=resp.get(
                    "responseDescription", "Unable to retrieve bank list"
                ),
                detail=resp.get("responseDescription", "Unable to retrieve bank list"),
            )
            return data
    except Exception as e:
        data = set_response_data(
            code=settings.FAILED_RESPONSE,
            description="Unable to retrieve bank list",
            detail=f"Unable to retrieve bank list -> {e}",
        )
        return data


def mfs_name_enquiry(
    country_code: str,
    phone_number: str,
    name: str,
    account_number: str,
    mfs_bank_code: str,
) -> dict:
    """
    construct name enquiry url, call the mfs service and return response
    """
    url = f"{settings.MFS_BASE_URL}/bank/account/inquiry/{country_code}/{phone_number}/{name.replace(' ', '%20')}/{account_number}/{mfs_bank_code}"
    url = f"{settings.MFS_BASE_URL}/bank/account/inquiry/NG/************/Tosin%20Paul/**********/063"
    try:
        resp = requests.get(url)
        resp = resp.json()

        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Name Enquiry Successful",
                detail={"account_name": resp.get("accountName")},
            )
            return data
        else:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=resp.get(
                    "responseDescription", "Unable to Retrieve Account Name"
                ),
                detail=resp.get(
                    "responseDescription", "Unable to Retrieve Account Name"
                ),
            )
            return data
    except Exception as e:
        data = set_response_data(
            code=settings.FAILED_RESPONSE,
            description="Unable to Retrieve Account Name",
            detail=f"Unable to Retrieve Account Name -> {e}",
        )
        return data


def get_mfs_exhange_rate(
    destination_currency_code: str, destination_country_code: str
) -> dict:
    """
    returns the exhange rate from NGN to the coresponding destination country currency
    """
    url = f"{settings.MFS_BASE_URL}/mobile/money/rate/inquiry/{destination_country_code}/USD/{destination_currency_code}"
    try:
        resp = requests.get(url)
        status_code = resp.status_code
        resp = resp.json()

        if status_code == 200:
            fx_rate = resp.get("fxRate")

            usd_to_naira, omni_usd_to_naira = get_omni_channel_usd_to_naira_config()
            naira_to_usd = get_currency_to_usd_from_usd_to_currency(usd_to_naira)
            omni_naira_to_usd = get_currency_to_usd_from_usd_to_currency(
                omni_usd_to_naira
            )
            currency_to_usd = get_currency_to_usd_from_usd_to_currency(
                Decimal(str(fx_rate))
            )

            exchange_rate = naira_to_usd / currency_to_usd
            omni_exchange_rate = omni_naira_to_usd / currency_to_usd

            exchange_rate_dict = {
                "exchange_rate": exchange_rate,
                "omni_exchange_rate": omni_exchange_rate,
            }

            update_exchange_rate(destination_country_code, exchange_rate_dict)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Exchange Rate Successful",
                detail=exchange_rate_dict,
            )
            return data
        else:
            raise Exception
    except Exception as e:
        data = set_response_data(
            code=settings.FAILED_RESPONSE,
            description="Unable To Retrieve Exchange Rate",
            detail=str(e),
        )
        return data


def mfs_bank_send_money(payload_class):
    """
    calls mfs bank deposit endpoint with data from the payload class
    """
    log = logging.getLogger("omni_international_send_money")

    url = f"{settings.MFS_BASE_URL}/bank/initialize/receive"
    payload = {
        "accountNumber": payload_class.reciever_account_number,
        "amountTransactionDetails": {
            "amount": str(payload_class.reciever_amount),
            "currencyCode": payload_class.reciever_currency_code,
        },
        "mfsBankcode": payload_class.reciever_mfs_bank_code,
        "narration": payload_class.narration,
        "recipient": {
            "address": payload_class.reciever_address,
            "country": payload_class.reciever_country_code,
            "dob": None,
            "documentId": None,
            "firstName": payload_class.reciever_first_name,
            "lastName": payload_class.reciever_last_name,
            "msisdn": payload_class.reciever_phone_number,
        },
        "requestId": payload_class.request_id,
        "sender": {
            "address": payload_class.sender_address,
            "country": payload_class.sender_country_code,
            "dob": payload_class.sender_dob,
            "documentId": payload_class.sender_document_id,
            "firstName": payload_class.sender_first_name,
            "lastName": payload_class.sender_last_name,
            "msisdn": payload_class.sender_phone_number,
        },
    }

    log.info(
        f"FROM SEND MONEY INTERNATIONAL CALLING THE INTERNATIONAl SEND MONEY SERVICE {url} with payload {payload}"
    )

    try:
        resp = requests.post(url, json=payload)
        resp = resp.json()
        log.info(f"FROM SEND MONEY INTERNATIONAL RESPONSE FROM SERVICE {resp}")
        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            log.info(
                f"FROM SEND MONEY INTERNATIONAL -> About to execute the transaction"
            )
            transaction_ref = resp.get("transactionRef")
            resp = execute_mfs_transaction(transaction_ref)
            log.info(
                f"FROM SEND MONEY INTERNATIONAL Response from executing the transaction -> {resp}"
            )
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description=resp.get(
                        "responseDescription",
                        "Send Money Request Initiated Successfully",
                    ),
                    detail={"mfs_reference": transaction_ref},
                )
                log.info(f"Exiting FROM SEND MONEY INTERNATIONAL service with {data}")
                return data
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable to Execute MFS Transaction",
                    detail={"mfs_reference": transaction_ref},
                )
                log.info(f"Exiting FROM SEND MONEY INTERNATIONAL service with {data}")
                return data
        else:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=resp.get(
                    "responseDescription", "Unable to Complete Send Money Request"
                ),
                detail="Unable to Complete Send Money Request",
            )
            log.info(f"Exiting FROM SEND MONEY INTERNATIONAL service with {data}")
            return data
    except Exception as e:
        data = set_response_data(
            code=settings.FAILED_RESPONSE,
            description="Unable to Complete Send Money Request",
            detail=str(e),
        )
        return data


def execute_mfs_transaction(transaction_ref: str, is_mobile_money: bool = False) -> dict:
    """
    calls the execute function on transaction to initiate the transaction
    """
    if is_mobile_money:
        url = f"{settings.MFS_BASE_URL}/mobile/money/execute/{transaction_ref}"
    else:
        url = f"{settings.MFS_BASE_URL}/bank/execute/{transaction_ref}"
    try:
        resp = requests.get(url)
        if resp.status_code != 200:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to execute transaction",
                detail="Unable to execute transaction",
            )
        else:
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Transaction Executed Successfully",
                detail="Transaction Executed Successfully",
            )
        return data
    except Exception as e:
        data = set_response_data(
            code=settings.FAILED_RESPONSE,
            description="Error Occurred While Executing Transaction",
            detail="Error Occurred While Executing Transaction",
        )
        return data


def get_mfs_transaction_status(transaction_ref: str, check_level: int = 0) -> str:
    """
    calls the mfs transaction status endpoint to get the state of transaction
    based on the transaction_ref
    """
    if check_level == 0:
        url = f"{settings.MFS_BASE_URL}/general/transaction/tsq/{transaction_ref}"
    if check_level == 1:
        url = f"{settings.MFS_BASE_URL}/general/transaction/mm/tsq/{transaction_ref}"
    try:
        resp = requests.get(url)
        resp = resp.json()

        if resp.get("responseCode") == "54":
            status = "pending"
        elif resp.get("responseCode") == "00":
            status = "success"
        else:
            status = "failed"

        return status
    except Exception as e:
        return "pending"


def mfs_mobile_money_account_enquiry(country_code: str, phone_number: str) -> dict:
    """
    construct mobile money account enquiry url, call the mfs service and return response
    """
    url = f"{settings.MFS_BASE_URL}//mobile/money/account/inquiry/{country_code}/{phone_number}"

    try:
        resp = requests.get(url)
        resp = resp.json()

        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": resp.get(
                    "responseDescription", "Mobile Account Enquiry Successful"
                ),
                "response_detail": "Mobile Account Enquiry Successful",
            }
        else:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp.get(
                    "responseDescription", "Unable to Retrieve Mobile Account Enquiry"
                ),
                "response_detail": "Unable to Retrieve Mobile Account Enquiry",
            }
        return data
    except Exception as e:
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Unable to Retrieve Mobile Account Enquiry -> {e}",
            "response_detail": str(e),
        }
        return data


def mfs_mobile_money_send_money(payload_class):
    """
    calls mfs mobile money deposit endpoint with data from the payload class
    """
    log = logging.getLogger("omni_international_send_money")

    url = f"{settings.MFS_BASE_URL}/mobile/money/initialize/withdraw"

    payload = {
        "amountTransactionDetails": {
            "amount": str(payload_class.reciever_amount),
            "currencyCode": payload_class.reciever_currency_code,
        },
        "cashPickUpPartners": None,
        "recipient": {
            "address": None,
            "country": payload_class.reciever_country_code,
            "dob": None,
            "documentId": None,
            "firstName": payload_class.reciever_first_name,
            "lastName": payload_class.reciever_last_name,
            "msisdn": payload_class.reciever_phone_number,
        },
        "requestId": payload_class.request_id,
        "sender": {
            "address": payload_class.sender_address,
            "country": payload_class.sender_country_code,
            "dob": payload_class.sender_dob,
            "documentId": payload_class.sender_document_id,
            "firstName": payload_class.sender_first_name,
            "lastName": payload_class.sender_last_name,
            "msisdn": payload_class.sender_phone_number,
        },
    }

    log.info(
        f"FROM SEND MONEY INTERNATIONAL CALLING THE INTERNATIONAl MOBILE MONEY SERVICE {url} with payload {payload}"
    )

    try:
        resp = requests.post(url, json=payload)
        resp = resp.json()
        log.info(f"FROM SEND MONEY INTERNATIONAL RESPONSE FROM SERVICE {resp}")
        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            log.info(
                f"FROM SEND MONEY INTERNATIONAL -> About to execute the transaction"
            )
            transaction_ref = resp.get("transactionRef")
            resp = execute_mfs_transaction(transaction_ref, is_mobile_money=True)
            log.info(
                f"FROM SEND MONEY INTERNATIONAL Response from executing the transaction -> {resp}"
            )
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description=resp.get(
                        "responseDescription",
                        "Send Money Request Initiated Successfully",
                    ),
                    detail={"mfs_reference": transaction_ref},
                )
                log.info(f"Exiting FROM SEND MONEY INTERNATIONAL service with {data}")
                return data
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable to Execute MFS Transaction",
                    detail={"mfs_reference": transaction_ref},
                )
                log.info(f"Exiting FROM SEND MONEY INTERNATIONAL service with {data}")
                return data
        else:
            return {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp.get(
                    "responseDescription", "Unable to Complete Send Money Request"
                ),
            }
    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Unable to Complete Send Money Request : Error -> {e}",
        }


def mifos_generate_wallet_no(wallet_args: dict):
    """
    Calls the mifos wallet servicee to generate wallet no which will serve as account
    number for customers
    """

    log = logging.getLogger("django")
    url = f"{settings.WALLET_SERVICE_BASE_URL}/wallet/create/user"

    payload = {
        "email": wallet_args.get("email"),
        "firstName": wallet_args.get("first_name"),
        "lastName": wallet_args.get("last_name"),
        "mobileNumber": wallet_args.get("mobile_number"),
    }

    try:
        log.info(f"Calling wallet service url -> {url} with payload -> {payload}")
        resp = requests.post(url, json=payload)
        resp = resp.json()
        log.info(f"Response from Wallet Service -> {resp}")
        if resp.get("responseCode") != settings.SUCCESS_RESPONSE:
            email = wallet_args.get("email")
            phone_number = wallet_args.get("mobile_number")
            short_name = wallet_args.get("short_name")
            fineract_product_id = wallet_args.get("fineract_product_id")
            customer_has_wallet, client_id = get_customer_wallet(
                email, phone_number, short_name, fineract_product_id
            )
            if customer_has_wallet:
                return {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_description": "Customer Already Has Existing Wallet Account, Please Proceed to Login",
                }
            else:
                return create_customer_wallet(client_id, wallet_args.get("wallet_id"))
        else:
            client_id = resp.get("clientId")
            return create_customer_wallet(client_id, wallet_args.get("wallet_id"))

    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Reaching Wallet Service: Error -> {e}",
        }


def get_customer_wallet(
    email: str, phone_number: str, short_name: str, fineract_product_id: str
):
    """
    we try to retrieve existing wallet belonging to customer as well as the client id
    """
    log = logging.getLogger("django")

    email = urllib.parse.quote(email)
    url = f"{settings.WALLET_SERVICE_BASE_URL}/wallet/inquiry/account?email={email}&mobileNumber={phone_number}"

    log.info(f"Calling wallet service url -> {url}")
    response = requests.get(url)
    resp = response.json()
    log.info(f"Response from Wallet Service -> {resp}")
    if response.status_code == 200:
        client_id = resp.get("clientId")
        for wallet in resp.get("connectedWallets", []):
            if (
                wallet.get("typeId", {}).get("shortName") == short_name
                and wallet.get("typeId", {}).get("fineractProductId")
                == fineract_product_id
            ):
                return (True, client_id)
        return (False, client_id)
    else:
        raise Exception("Invalid Response Retrieving Customer Wallet")


def create_customer_wallet(client_id, wallet_id):

    log = logging.getLogger("django")

    url = f"{settings.WALLET_SERVICE_BASE_URL}/wallet/update/user"

    payload = {"clientId": client_id, "productId": wallet_id}

    log.info(f"Calling wallet service url -> {url}")
    resp = requests.put(url, json=payload)
    resp = resp.json()

    log.info(f"Response from Wallet Service -> {resp}")
    if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
        return {
            "response_code": settings.SUCCESS_RESPONSE,
            "response_detail": {
                "account_number": resp.get("accountNumber"),
                "nuban": resp.get("nuban"),
                "client_id": client_id,
            },
        }
    else:
        raise Exception(
            f'Unable to Create Customer Wallet: Error -> {resp.get("responseDescription")}'
        )


def is_payment_option_valid(options: dict) -> bool:
    return True


def generate_qr_image(merchant: Merchant, message="") -> str:
    """
    generates qr image and save image to the qr code folder and returns the
    image path.
    """
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_H,
        box_size=10,
        border=4,
    )
    qr.add_data(merchant.merchant_qr_data)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white").convert("RGB")

    path = f"qrcode/image_{random.randint(0, ********)}.png"
    img.save(path)

    email_args = {
        "email_from": merchant.agent.bank.email,
        "to": merchant.email,
        "subject": "MERCHANT QR",
        "file_path": path,
        "file_name": "image_qr",
        "file_type": "image/png",
        "message": "Please find attached your custom merchant qr image"
        if not message
        else message,
    }
    resp = send_email(email_args, "QR_NOTIFICATION")
    data = set_response_data(
        code=resp.get("response_code", settings.FAILED_RESPONSE),
        description=resp.get("response_description", "Error Sending Email"),
        detail=resp.get("response_detail", resp.get("response_description")),
    )
    try:
        os.remove(path)
        return data
    except Exception:
        # dont break anything even if image does not get deleted
        return data


def createNqrMerchant(merchant: Merchant) -> Merchant:
    """
    creates Merchant on NQR and updates merchant with Merchant Number and approves
    merchant.
    """
    log = logging.getLogger("django")
    resp = nip_name_enquiry(merchant.account_number, merchant.bank)
    log.info(f"CREATING NQR MERCHANT, NIP NAME ENQUIRY RESPONSE -> {resp}")
    if resp.get("response_code") != settings.SUCCESS_RESPONSE:
        raise Exception(
            f'Unable To Retrieve Account Name -> {resp.get("response_description")}'
        )
    account_name = resp.get("response_description", {}).get("account_name")
    bvn = resp.get("response_description", {}).get("bvn")
    merchant.account_name = account_name
    if bvn != merchant.bvn and settings.APP_ENV == "production":
        log.info(
            f"ERROR CREATING NQR MERCHANT, BVN NOT EQUAL TO MERCHANT BVN bvn -> {bvn}, merchant bvn -> {merchant.bvn}"
        )
        merchant.is_approved = False
        merchant.save()
        return merchant
    name = f"{merchant.first_name} {merchant.last_name}"
    payload = {
        "accountName": account_name,
        "accountNumber": merchant.account_number,
        "address": merchant.address,
        "bankNumber": merchant.bank.nip_code,
        "email": merchant.email,
        "fee": 0,
        "feeBearer": "MERCHANT",
        "institutionNumber": merchant.agent.bank.institution_number,
        "mobileNumber": f"234{merchant.phone_number[(len(merchant.phone_number) -10):]}",
        "name": name if len(name) <= 25 else name[:25],
        "tin": "",
    }

    url = f"{settings.NQR_SERVICE_BASE_URL}/merchant/create"

    if settings.APP_ENV == "development":
        merchant.merchant_number = f"{random.randint(111111, **********)}"
        merchant.is_approved = True
        merchant.save()
        return merchant

    log.info(f"Calling Create Merchant service url -> {url}, payload -> {payload}")
    resp = requests.post(url, json=payload)
    resp = resp.json()
    log.info(f"Response from calling create nqr merchant resp -> {resp}")
    if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
        merchant.merchant_number = resp.get("merchantNumber")
        merchant.is_approved = True
        merchant.save()
        nqrUpdateSettlementInformation(merchant)
        return merchant
    else:
        merchant.is_approved = False
        merchant.save()
        return merchant


def getMerchantQr(merchant: Merchant):
    """
    checks if merchant is approved, then calls nqr service to generate merchant qrdata
    """
    log = logging.getLogger("django")

    if settings.APP_ENV == "development":
        qr_data = merchant.email
        merchant.merchant_qr_data = qr_data
        merchant.merchant_sub_number = f"{random.randint(111111, **********)}"
        merchant.save()
        data = {
            "response_code": settings.SUCCESS_RESPONSE,
            "response_description": {"qr_data": qr_data},
        }
        log.info(f"Returning Response from Get Merchant QR function {data}")
        return data

    if merchant.is_approved != True:
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": "Merchant Is Not Approved",
        }
        log.info(f"Get Merchant QR -> Merchant is not Approved, data -> {data}")
        return data

    name = f"{merchant.first_name} {merchant.last_name}"
    url = f"{settings.NQR_SERVICE_BASE_URL}/merchant/submerchant/create"
    payload = {
        "amount": 0,
        "email": merchant.email,
        "institutionNumber": merchant.agent.bank.institution_number,
        "merchantNumber": merchant.merchant_number,
        "mobileNumber": f"234{merchant.phone_number[(len(merchant.phone_number) -10):]}",
        "name": name if len(name) <= 25 else name[:25],
        "qrtype": "DYNAMIC",
    }

    try:
        log.info(f"Calling Get Merchant QR service url -> {url}, payload -> {payload}")
        resp = requests.post(url, json=payload)
        resp = resp.json()
        log.info(f"Response from calling Get Merchant QR service {resp}")
        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            qr_data = resp.get("qrData")
            merchant_sub_number = resp.get("merchantNumber")
            merchant.merchant_qr_data = qr_data
            merchant.merchant_sub_number = merchant_sub_number
            merchant.save()
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": {"qr_data": qr_data},
            }
            log.info(f"Returning Response from Get Merchant QR function {data}")
            return data
        else:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp.get(
                    "response_description", "Unable to generate QR Data"
                ),
            }
            log.info(f"Returning Response from Get Merchant QR function {data}")
            return data
    except Exception as e:
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Generating QR Data -> {e}",
        }
        log.info(f"Returning Response from Get Merchant QR function {data}")
        return data


def nqrUpdateSettlementInformation(merchant: Merchant):
    """
    Update merchant settlement information
    """
    log = logging.getLogger("django")
    url = f"{settings.NQR_SERVICE_BASE_URL}/merchant/update"

    payload = {
        "accountName": merchant.account_name,
        "accountNumber": merchant.account_number,
        "institutionCode": merchant.bank.nip_code,
        "institutionNumber": merchant.agent.bank.institution_number,
        "merchantNumber": merchant.merchant_number,
    }

    try:
        log.info(
            f"Calling NQR Merchant Update Settlement Information url -> {url}, payload -> {payload}"
        )
        resp = requests.post(url, json=payload)
        resp = resp.json()
        log.info(f"Response from Calling NQR Merchant Settlement Infomation -> {resp}")
        if resp.get("responseCode") != settings.SUCCESS_RESPONSE:
            raise Exception("Error Updating Settlement Information")
    except Exception as e:
        raise Exception(f"{e}")


def initiateNxPayToken(token: Token):
    """
    initiate NxPayToken, call the NxPay Service
    """
    log = logging.getLogger("django")
    url = f"{settings.NXPAY_SERVICE_BASE_URL}/institution/{token.account.bank.nip_code}/initialize"

    payload = {
        "amount": f"{token.amount}",
        "beneficiary_email": token.email,
        "beneficiary_name": "",
        "beneficiary_phone": token.phone_number,
        "originating_account_number": token.account.wallet_no
        if token.account.is_wallet
        else token.account.account_number,
        "originating_institution_code": token.account.bank.nip_code,
        "sender_email": token.account.profile.customer.email,
        "sender_name": f"{token.account.profile.customer.first_name} {token.account.profile.customer.last_name}",
        "sender_phone": token.account.profile.phone_number,
        "token_pin": token.retrieval_pin,
        "transport_type": f"{token.retrieval_channel}".upper(),
        "user_reference": f"{get_reference()}",
    }

    try:
        log.info(
            f"Calling NxPay Service Initiate Token with payload {payload} at url {url}"
        )
        resp = requests.post(url, json=payload)
        resp = resp.json()
        log.info(f"Response from NxPay Service response -> {resp}")

        if resp.get("response_code") == settings.SUCCESS_RESPONSE:
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": resp.get(
                    "response_description", "SUCCESS RESPONSE"
                ),
                "response_detail": {"token": resp.get("token")},
            }
        else:
            return {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp.get(
                    "response_description", "Error Initiating Token"
                ),
            }
    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Initiating Token {e}",
        }


def validateNxPayToken(bank_code: str, token: str):
    """
    Validate NxPay Token from the NxPay service
    """
    log = logging.getLogger("django")
    url = f"{settings.NXPAY_SERVICE_BASE_URL}/institution/{bank_code}/validate/{token}"

    try:
        log.info(f"Calling validate toke service at url -> {url}")
        resp = requests.put(url)
        resp = resp.json()
        log.info(f"Response from validate nxpay service {resp}")

        if resp.get("response_code") == settings.SUCCESS_RESPONSE:
            data = resp.get("data", {})
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": resp.get(
                    "response_description", "SUCCESS RESPONSE"
                ),
                "response_detail": {
                    "amount": data.get("amount"),
                    "originating_institution_code": data.get(
                        "originating_institution_code"
                    ),
                    "expiry_date": data.get("expiry_date"),
                    "fee": data.get("fee"),
                    "originating_account_number": data.get(
                        "originating_account_number"
                    ),
                    "vat": data.get("vat"),
                    "sender_name": data.get("sender_name"),
                    "token_ref": data.get("soft_token_reference"),
                },
            }
        else:
            return {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp.get(
                    "responseDescription", "Error Initiating Token"
                ),
            }
    except Exception as e:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Validating Token {e}",
        }


def redeemNxPayToken(account: CustomerAccount, data: dict):
    """
    Redeem NxPay Token using the NxPay Service
    """
    url = (
        f"{settings.NXPAY_SERVICE_BASE_URL}/institution/{account.bank.nip_code}/redeem"
    )

    payload = {
        "amount": f"{data.get('amount')}",
        "destination_account_number": account.account_number,
        "destination_institution_code": account.bank.nip_code,
        "token": data.get("token"),
        "token_retrieval_pin": data.get("retrieval_pin"),
        "user_reference": f"{get_reference()}",
    }

    try:
        resp = requests.post(url, json=payload)
        resp = resp.json()
        if resp.get("response_code") == settings.SUCCESS_RESPONSE:
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": resp.get(
                    "response_description", "Token Redeemed Successfully"
                ),
            }
        else:
            return {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": resp.get(
                    "response_description", "Error redeeming NxPay Token"
                ),
            }
    except Exception as e:
        data = set_response_data(
            code=settings.FAILED_RESPONSE,
            description=f"Error Redeeming NxPay Token, Error -> {e}",
            detail=str(e),
        )
        return data

def retrieve_account_statement(
    bank: Bank, account_number: str, start_date: str, end_date: str
) -> dict:
    """
    Retrives account statement data from the core banking system
    """

    url = f"{settings.MONEY_FLEX_OPEN_API_BASE_URL}/nambuit_statement/1.0.0/account/statement/{bank.code}/{account_number}/{start_date}/{end_date}"

    token = local_retrieve_nam_token(bank.alias)
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/x-www-form-urlencoded",
    }

    try:
        log.info(f"Trying to call CORE BANKING Account statement service -> {url}")
        response = requests.get(url, headers=headers)
        if response.status_code == 401:
            # token has expired or invalid
            token = get_nam_token(bank_alias=bank.alias)
            update_nam_token(token, bank.alias)
            headers["Authorization"] = f"Bearer {token}"
            response = requests.get(url, headers=headers)
        resp = response.json()
        if resp.get("responseCode") == settings.SUCCESS_RESPONSE:
            log.info(f"SUCCESS RESPONSE FROM CORE BANKING Account statement service")
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_detail": {
                    "start_balance": resp.get("balanceAtStart"),
                    "end_balance": resp.get("balanceAtEnd"),
                    "statement": resp.get("statements", []),
                },
            }
        log.info(
            f"FAILED RESPONSE FROM CORE BANKING Account statement service resp -> {resp}"
        )
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": resp.get(
                "responseMessage", "Invalid Response from Core banking"
            ),
        }

    except Exception as e:
        data = {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f"Error Reaching Account statement service: {e}",
        }
        log.info(
            f"FAILED RESPONSE FROM CORE BANKING Account statement service -> {data}"
        )
        return data




dummy_transactions =  [
        {
            "Id": 696864,
            "CurrentDate": "2025-04-19T14:22:39",
            "IsReversed": False,
            "ReversalReferenceNo": None,
            "WithdrawableAmount": 0,
            "UniqueIdentifier": "************",
            "InstrumentNo": "************",
            "TransactionDate": "2020-09-09T00:00:00",
            "TransactionDateString": "Wednesday, September 9, 2020 12:00 AM",
            "ReferenceID": "A20090946981",
            "Narration": "FT********** **********",
            "Amount": 10,
            "OpeningBalance": 0,
            "Balance": 10,
            "PostingType": "ISOPosting",
            "Debit": "",
            "Credit": "0.10",
            "IsCardTransation": False,
            "AccountNumber": None,
            "ServiceCode": "1007",
            "RecordType": "Credit",
            "ProductInfo": None
        },
        {
            "Id": 696865,
            "CurrentDate": "2025-04-19T14:22:39",
            "IsReversed": False,
            "ReversalReferenceNo": None,
            "WithdrawableAmount": 0,
            "UniqueIdentifier": "************",
            "InstrumentNo": "************",
            "TransactionDate": "2020-09-09T00:00:00",
            "TransactionDateString": "Wednesday, September 9, 2020 12:00 AM",
            "ReferenceID": "A20090946982",
            "Narration": "FT********** **********",
            "Amount": 10,
            "OpeningBalance": 10,
            "Balance": 0,
            "PostingType": "ISOPosting",
            "Debit": "0.10",
            "Credit": "",
            "IsCardTransation": False,
            "AccountNumber": None,
            "ServiceCode": "1007",
            "RecordType": "Debit",
            "ProductInfo": None
        },
        {
            "Id": 696549,
            "CurrentDate": "2025-04-11T23:21:38",
            "IsReversed": False,
            "ReversalReferenceNo": None,
            "WithdrawableAmount": 0,
            "UniqueIdentifier": "************",
            "InstrumentNo": "************",
            "TransactionDate": "2020-09-09T00:00:00",
            "TransactionDateString": "Wednesday, September 9, 2020 12:00 AM",
            "ReferenceID": "A2009094681200",
            "Narration": "FT********** **********",
            "Amount": 300,
            "OpeningBalance": 0,
            "Balance": 300,
            "PostingType": "ISOPosting",
            "Debit": "",
            "Credit": "3.00",
            "IsCardTransation": False,
            "AccountNumber": None,
            "ServiceCode": "1007",
            "RecordType": "Credit",
            "ProductInfo": None
        }
]

def get_account_transactions_history(account_number: str, bank: Bank, from_date=None, to_date=None, page_number=1):
    """
    Get account transactions history from the core banking system
    """
    if bank.belongs_to_bank_one:
        if from_date and to_date:
            bank_one_response = BankOneResource.get_account_transactions(
                account_number=account_number, from_date=from_date, to_date=to_date
            )
        else:
            bank_one_response = BankOneResource.get_account_transactions(account_number)
        if bank_one_response.get("status") == "success":
            transactions = []
            cba_transactions = bank_one_response.get("transactions")
            # cba_transactions = dummy_transactions
            for trnx in cba_transactions:
                filtered_transaction = {
                    "cba_id": trnx.get("Id"),
                    "current_date": trnx.get("CurrentDate"),
                    "is_reversed": trnx.get("IsReversed"),
                    "withdrawable_amount": trnx.get("WithdrawableAmount"),
                    "unique_identifier": trnx.get("UniqueIdentifier"),
                    "instrument_no": trnx.get("InstrumentNo"),
                    "transaction_date": trnx.get("TransactionDate"),
                    "reference_id": trnx.get("ReferenceID"),
                    "narration": trnx.get("Narration"),
                    "amount": trnx.get("Amount"),
                    "balance": trnx.get("Balance"),
                    "posting_type": trnx.get("PostingType"),
                    "debit": trnx.get("Debit"),
                    "credit": trnx.get("Credit"),
                    "is_card_transaction": trnx.get("IsCardTransation"),
                    "record_type": trnx.get("RecordType"),
                    "product_info": trnx.get("ProductInfo")
                }
                transactions.append(filtered_transaction)
            data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description': "Get Account History Successful.",
                    'response_detail' : {
                        'transactions' : transactions
                    }
                }
            logging.info(f"RESPONSE FROM TRANSACTION SERVICE >>> {data}")
            return data
        elif bank_one_response.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : bank_one_response.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Something went wrong with request."
            }
            
    elif bank.belongs_to_nibss_core_banking:
        if from_date and to_date:
            nibbss_responsee = NIBSSResource.get_account_transactions(
                account_number=account_number,
                page_number=page_number,
                start_date=from_date,
                end_date=to_date
            )
            
        else:
            nibbss_responsee = NIBSSResource.get_account_transactions(
                account_number=account_number,
                page_number=page_number
            )
            
        if nibbss_responsee.get("status") == "success":
            transactions = []
            cba_transactions = nibbss_responsee.get("transactions")
            # cba_transactions = dummy_transactions
            for trnx in cba_transactions:
                record = trnx.get("journalDrCr")
                if record == "CR":
                    record_type = "CREDIT"
                elif record == "DR":
                    record_type = "DEBIT"
                else:
                    record_type = None
                filtered_transaction = {
                    "cba_id": trnx.get("journalId") or "",
                    "current_date": trnx.get("CurrentDate") or "",
                    "is_reversed": trnx.get("IsReversed") or "",
                    "withdrawable_amount": trnx.get("WithdrawableAmount"),
                    "unique_identifier": trnx.get("journalContractReference") or "",
                    "instrument_no": trnx.get("InstrumentNo") or "",
                    "transaction_date": trnx.get("journalTransactionDate") or None,
                    "reference_id": trnx.get("journalContractReference") or "",
                    "narration": trnx.get("journalNarration") or "",
                    "amount": trnx.get("journalAmount") or "",
                    "balance": trnx.get("Balance") or "",
                    "posting_type": trnx.get("PostingType") or "",
                    "debit": trnx.get("Debit") or "",
                    "credit": trnx.get("Credit") or "",
                    "is_card_transaction": trnx.get("IsCardTransation") or "",
                    "record_type":  record_type,
                    "product_info": trnx.get("ProductInfo") or ""
                }
                transactions.append(filtered_transaction)
            data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description': "Get Account History Successful.",
                    'response_detail' : {
                        'transactions' : transactions
                    }
                }
            logging.info(f"RESPONSE FROM TRANSACTION SERVICE >>> {data}")
            return data
        elif nibbss_responsee.get("status") == "failed":
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : nibbss_responsee.get("message")
            }
        else:
            return {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : "Something went wrong with request."
            }
        

"""
{
  "response_code": "00",
  "response_description": "GET TRANSACTIONS SUCCESSFUL -> Retrieve, Update Successful",
  "response_detail": {
    "count": 10,
    "next": "http://example.com/api/customer/transactions/**********/?page=2",
    "previous": null,
    "results": [
      {
        "transaction_details": {
          "transaction_date": "2025-04-19T14:22:39",
          "transaction_class": "DEBIT",
          "transaction_type": "Fund Transfer",
          "tsq_type": "FT",
          "transaction_status": "S",
          "amount": 10.0000,
          "amount_in_words": "Ten Naira Only",
          "transaction_description": "Transaction Successful",
          "transaction_narration": "FT********** **********",
          "customer_reference": "Y079099924",
          "channel": "Mobile"
        },
        "beneficiary_details": {
          "beneficiary_account_number": "**********",
          "beneficiary_name": "Jason Bourne",
          "beneficiary_bank": "Polaris Bank"
        },
        "sender_details": {
          "sender_account_number": "**********",
          "sender_name": "Victor Azubike Odu",
          "sender_bank": "Bank One"
        }
      }
    ]
  }
}
"""
"""
{
            "journalId": 1222214,
            "journalCustomerId": "CUS0000089343",
            "journalAccountNumber": "**********",
            "journalDrCr": "CR",
            "journalContractReference": "20250505FI0000113212",
            "journalAmount": "2.23",
            "journalNarration": "MONTHLY INTEREST ACCRUED : **********",
            "journalMaker": "SYSTEM",
            "journalChecker": "SYSTEM",
            "journalStatus": "ACTIVE",
            "journalCreatedAt": "2025-05-05T09:46:47.453",
            "journalUpdatedAt": "2025-05-05T09:46:47.453",
            "journalAccountName": "OLASOJI DAVID AKOGUN OLASOJI DAVID",
            "journalCurrencyCode": "NGN",
            "journalTransactionDate": "2025-04-30",
            "journalValueDate": "2025-04-30"
        },
        {
            "journalId": 1218081,
            "journalCustomerId": "CUS0000089343",
            "journalAccountNumber": "**********",
            "journalDrCr": "DR",
            "journalContractReference": "20250504FT0000000*********5189",
            "journalAmount": "100.00",
            "journalNarration": "FT********** **********",
            "journalMaker": "607880526582_OMNICHANNEL API KEY FOR MOBILE APP AN",
            "journalChecker": "607880526582_OMNICHANNEL API KEY FOR MOBILE APP AN",
            "journalStatus": "ACTIVE",
            "journalCreatedAt": "2025-05-04T13:34:19.120",
            "journalUpdatedAt": "2025-05-04T13:34:19.120",
            "journalAccountName": "OLASOJI DAVID AKOGUN OLASOJI DAVID",
            "journalCurrencyCode": "NGN",
            "journalTransactionDate": "2025-05-01",
            "journalValueDate": "2025-05-01"
        }
    ]
}
"""