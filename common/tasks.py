import logging

import xlsxwriter
from dfa.models import Merchant
from logic_omnichannel.celery import app
from logic_omnichannel import settings
from customer.models import CustomerProfile
from bank.models import Bank
from .services import send_email
from pathlib import Path
import os
# from logic_omnichannel import settings
from django.conf import settings
from datetime import datetime, timedelta


@app.task(name="send_app_update_notification")
def send_app_update_notification(app_code="chmfb"):
    """
    batch sending of email notification for app update,
    from customer profile, we verify if customer is yet to recieve an update email
    then we send the customer an update email
    """
    log = logging.getLogger("django")
    try:
        customer_profiles = CustomerProfile.objects.filter(
            is_email_verified=True,
            has_recieved_app_update_notification=False,
            customer__app_code=app_code,
        )
        bank = Bank.objects.get(alias=app_code)
        profile_count = len(customer_profiles)
        if profile_count > 80:
            customer_profiles = customer_profiles[:80]
        log.info(
            f"About to send {profile_count} customers app update notification mail"
        )
        failed_send = 0
        for profile in customer_profiles:
            email_args = {
                "name": profile.customer.first_name,
                "img_url": f"{bank.email_img_url}",
                "bg_col": bank.email_foreground_color,
                "email_from": bank.email,
                "to": profile.customer.email,
            }
            resp = send_email(email_args, "APP_UPDATE_NOTIFICATION")
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                profile.has_recieved_app_update_notification = True
                profile.save()
            else:
                failed_send += 1
                log.info(
                    f"Unable to send notification mail to {profile.customer.email} because -> {resp}"
                )
        log.info(
            f"App Update notification mail successfully sent for {profile_count - failed_send} customers"
        )
        log.info(
            f"Failed to send app update notification mail for {failed_send} customers"
        )
    except Exception as e:
        log.info(f"Error running app update notification -> {e}")


@app.task(name="notification_test")
def notification_test():
    bank = Bank.objects.get(alias="chmfb")
    email_args = {
        "name": "Oladapo",
        "img_url": f"{bank.email_img_url}",
        "bg_col": bank.email_foreground_color,
        "email_from": bank.email,
        "to": "<EMAIL>",
    }
    send_email(email_args, "APP_UPDATE_NOTIFICATION")


@app.task(name="send_merchant_report")
def generate_report():
    """
    return generic merchant report for all merchant
    """
    date = datetime.now()
    file_name = f"MerchantReport-{datetime.strftime(date, '%Y-%m-%d')}.xlsx"
    workbook = xlsxwriter.Workbook(file_name)

    worksheet = workbook.add_worksheet("MerchantReport")

    columns = [
        "Name",
        "Email",
        "Account Number",
        "Account Name",
        "Bank",
        "Merchant Number",
        "Onboarding Agent Name",
        "Organization Name",
        "Date",
        "Day",
        "Status",
        "Reason",
    ]

    row = 0

    for col, elem in enumerate(columns):
        worksheet.write(row, col, elem)

    _date = date.date() -  timedelta(days=1)
    merchants = (
        Merchant.objects.all()
        .order_by("agent", "agent__created_by")
        .filter(date_created__gte=_date)
    )

    for merchant in merchants:
        row += 1
        for col, elem in enumerate(columns):
            try:
                if elem == "Name":
                    worksheet.write(
                        row, col, f"{merchant.last_name} {merchant.first_name}"
                    )
                elif elem == "Email":
                    worksheet.write(row, col, merchant.email)
                elif elem == "Account Number":
                    worksheet.write(row, col, merchant.account_number)
                elif elem == "Account Name":
                    worksheet.write(row, col, merchant.account_name)
                elif elem == "Bank":
                    worksheet.write(row, col, merchant.bank.name)
                elif elem == "Merchant Number":
                    worksheet.write(row, col, merchant.merchant_number)
                elif elem == "Onboarding Agent Name":
                    worksheet.write(
                        row,
                        col,
                        f"{merchant.agent.user.last_name} {merchant.agent.user.first_name}",
                    )
                elif elem == "Organization Name":
                    worksheet.write(
                        row,
                        col,
                        (merchant.agent.created_by.organization_name)
                        if merchant.agent.created_by
                        else "",
                    )
                elif elem == "Date":
                    worksheet.write(
                        row, col, merchant.date_created.strftime("%Y-%m-%d")
                    )
                elif elem == "Day":
                    worksheet.write(row, col, merchant.date_created.strftime("%A"))
                elif elem == "Status":
                    if merchant.merchant_qr_data:
                        status = "SUCCESS"
                    elif not merchant.merchant_number:
                        status = "FAILED"
                    else:
                        status = "PENDING"
                    worksheet.write(row, col, status)
                elif elem == "Reason":
                    if merchant.merchant_qr_data:
                        reason = "Merchant Qr Successfully Generated"
                    elif not merchant.merchant_number:
                        if not merchant.account_number:
                            reason = "Unable to Retrieve Account Name, Possible Invalid Merchant Account Number"
                        else:
                            reason = (
                                "Possible BVN mismatch or Unable to Create NQR Merchant"
                            )
                    else:
                        reason = "Unable to create Merchant QR data"
                    worksheet.write(row, col, reason)
            except Exception as e:
                print(
                    f"error generating merchant with merchant id {merchant.id}, error -> {e}"
                )
                continue

    workbook.close()

    BASE_DIR = Path(__file__).resolve().parent.parent

    sheet_path = BASE_DIR / file_name
    time = date.strftime("%I:%M:%S %p")
    date = date.strftime("%b %d, %Y")

    message = f"Kindly find attached your omni channel merchant report generated at {time}, (GMT+1) {date}. Kindly review."
    email_args = {
        "email_from": settings.DEFAULT_FROM_EMAIL,
        "to": "<EMAIL>",
        "subject": "OMNI CHANNEL DAILY MERCHANT REPORT",
        "file_path": f"{sheet_path}",
        "file_name": f"{file_name}",
        "file_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "admin_name": "Gbenga".upper(),
        "message": message,
        "img_url": settings.EMAIL_DEFAULT_IMAGE_URL,
        "foreground": settings.EMAIL_DEFAULT_FOREGROUND,
        "background": settings.EMAIL_DEFAULT_BACKGROUND,
    }

    send_email(email_args, "REPORTING")

    os.remove(sheet_path)
