import base64
from typing import Dict, Optional

from rest_framework.response import Response
import logging
from dfa.models import AgentAccount
from mfs.models import Countries, CurrencyRate
import os
from copy import deepcopy
import string
import random

import xlwt
from billing.models import Bills, Configurations, Fee
from decimal import Decimal
from num2words import num2words
from bill_payment.models import Biller
import requests
from datetime import datetime, timedelta

from django.core.exceptions import ObjectDoesNotExist
from django.core.cache import cache
from django.core.files import File
from django.db import models
from django.db.models import query, Sum
from django.contrib.auth.models import User
from django.contrib.auth.hashers import check_password
from django.db.models import Q

import logging
from rest_framework.exceptions import AuthenticationFailed, ValidationError
from rest_framework.pagination import PageNumberPagination
from oauth2_provider.models import Application, AccessToken
from rest_framework.reverse import reverse

# from logic_omnichannel import settings
from django.conf import settings
from customer.models import CustomerAccount, CustomerProfile
from bank.models import Bank, Beneficiary
from .models import (
    BVN,
    MobileApplicationVersion,
    Platform,
    SendMoneyClass,
    SettlementAccount,
)


def set_bvn_dob(bvn, dob):
    bvn_dob_dict =cache.get('bvn_dob_dict')
    if bvn_dob_dict:
        bvn_dob_dict[bvn] = dob
    else:
        bvn_dob_dict = {bvn: dob}
    cache.set('bvn_dob_dict', bvn_dob_dict)

def get_bvn_dob(bvn):
    bvn_dob_dict = cache.get('bvn_dob_dict')
    if bvn_dob_dict:
        dob = bvn_dob_dict.pop(bvn, None)
        cache.set('bvn_dob_dict', bvn_dob_dict)
        return dob
    return None


class CustomPageNumberPagination(PageNumberPagination):

    page_size_query_param = 'page_size'

    def get_paginated_response(self, data):
        resp = super().get_paginated_response(data)
        page_size = super().get_page_size(self.request)
        resp.data['page_size'] = page_size
        return resp
    
    
class CustomDictPagination(PageNumberPagination):
    page_size_query_param = 'page_size'

    def paginate_queryset(self, queryset, request, view=None):
        self.request = request
        return super().paginate_queryset(queryset, request, view)

    def get_paginated_response(self, data):
        return Response({
            'count': self.page.paginator.count,
            'page_size': self.get_page_size(self.request),
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'results': data,
        })


def get_client_credentials(request=None, token=None) -> tuple:

    if request:
        '''
            This function extracts the base64 encoded Basic auth and decodes to
            get the client id and client secret and returns them
        '''

        try:
            auth = request.headers['Authorization']
            _, client_credentials = auth.split(' ', 1) 
            client_credentials = base64.b64decode(client_credentials)
            client_credentials = client_credentials.decode(encoding='utf-8')
            client_id, client_secret = client_credentials.split(':', 1)
            return (client_id, client_secret)
        except Exception:
            raise ValidationError
    if token:
        '''
            we get the client credentials from the access token
        '''
        try:
            client = AccessToken.objects.get(token=token)
            client_app = client.application
            client_id = client_app.client_id
            client_secret = client_app.client_secret
            return (client_id, client_secret)
        except Exception:
            raise ValidationError

def get_platform(client_id=None, client_secret=None, token=None) -> str:
    '''
        This function uses either a combination client id and client secret or just the 
        token to get the platform from which the request is being sent 
    '''

    if client_id is not None and client_secret is not None:
        '''
            from the oauth2_provider.models.AbstractApplication and return the
            name of the application using client id and client secret
        '''

        try:
            client = Application.objects.get(client_id=client_id)
            return client.name
        except Exception:
            raise ObjectDoesNotExist
    
    if token is not None:
        '''
            from oauth2_provide.models.AbstractAccessToken use the token to get the instance of 
            the application and then return the name of the application
        '''
        try:
            client = AccessToken.objects.get(token=token)
            client = client.application
            return client.name
        except Exception:
            raise ObjectDoesNotExist

def get_user_token(client_id, client_secret, username=None, password=None, request_platform=None, token_endpoint=None, refresh_token=None):

    '''
        This function calls the token generation endpoint with the function args
        and returns token parameters for the user. The args are encoded and passed
        into the request header
    '''
    if username and password:
        '''
            if username and password is provided, it is requesting for a fresh token
        '''
        if request_platform.upper().startswith('BANK ADMIN'):
            payload = f'grant_type=password&username={username}&password={password}&scope=read%20write%20bank'
        else:
            payload = f'grant_type=password&username={username}&password={password}&app_code=0000'
        client_credential = f'{client_id}:{client_secret}'
        client_credential = base64.b64encode(client_credential.encode('utf-8'))
        client_credential = str(client_credential, 'utf-8')

        headers = {
            'Authorization' : f'Basic {client_credential}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    elif refresh_token:
        '''
            if refresh token, this is requesting for token after token expiration
        '''
        payload = f'grant_type=refresh_token&refresh_token={refresh_token}&client_id={client_id}&client_secret={client_secret}'
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
    elif client_id and client_secret:
        '''
            If just `client ID and client secret is provided, sent with a different grant type `client_credentials`
        '''
        payload=f'grant_type=client_credentials&client_id={client_id}&client_secret={client_secret}'
        headers = {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'grant_type': 'client_credentials',
                    'client_id': client_id,
                    'client_secret': client_secret
                }

    log = logging.getLogger("django")

    try:
    
        response = requests.request('POST', token_endpoint, headers=headers, data=payload)
        response = response.json()

        return response
    except Exception as e:
        # handle error better other errors possible at the point
        log.info(">>> Real Token Error >>" + str(e))
        raise TimeoutError

def send_email_verification(user_token, request):
    '''
        calls the send_email_verification service which is authenticated by the user_token
    '''
    url = reverse('send_user_email_verification', request=request)

    headers = {
        'Authorization' : f'Bearer {user_token}',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    try:
        resp = requests.get(url, headers=headers)
        resp.json()
        # NOTE: this is running on its own thread the calling function is not exactly expecting a response
        # so probably just log this while implementing logging feature
    except Exception as e:
        pass

def get_bank_profile(account, platform=""):
    """
    Returns a bank profile template
    """
    bank_profile = {
        "bank": {
            "id": account.bank.id,
            "name": account.bank.name,
            "code": account.bank.code,
            "alias": account.bank.alias,
            "address": account.bank.address,
            "email": account.bank.email,
            "phone_number": account.bank.phone_number,
            "website": account.bank.website,
            "bank_logo_url": account.bank.logo_img.url,
            # "bank_logo_b64_encode": "No image for now",
            # WRITE A FUNCTION TO WRITE S3 FILES TO LOCAL STORAGE
            "bank_logo_b64_encode": account.bank.get_logo_base64()
            if not platform.upper().startswith("USSD APPLICATION")
            else "",
            "belong_to_nambuit_switch": account.bank.belong_to_nambuit_switch,
            "belong_to_nibss_switch": account.bank.belong_to_nibss_switch,
            "limit_for_soft_token": account.bank.limit_for_soft_token,
            "ussd_code": account.bank.ussd_code,
        },
        "profile_account": [],
    }

    return bank_profile

def get_filtered_queryset_by_date(
    start_date: str,
    end_date: str,
    queryset: query.QuerySet,
    query_model: models.Model = None,
):

    """
    returns a list of querysets within the particular set date range
    """
    start_date = datetime.strptime(start_date, "%Y-%m-%d")
    end_date = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
    if queryset and query_model and isinstance(queryset[0], query_model):
        """
        For merged models we access each model via the content_object
        Query model is suppsosed to be Transaction, due to circular import error we can't check here
        but we are going to be quering with, we confirm particular what model instance as it could either
        be Transactions or CoreBankingTransactions
        """
        if hasattr(query_model, "core_banking_date"):
            # queryset is instance of CoreBankingTransactions
            queryset = queryset.filter(core_banking_date__range=[start_date, end_date])
        elif hasattr(query_model, "content_type"):
            # queryset is instance of Transaction
            queryset = queryset.filter(
                Q(send_money__date_created__range=[start_date, end_date])
                | Q(airtime_data__date_created__range=[start_date, end_date])
                | Q(bill_payment__date_created__range=[start_date, end_date])
            )
        else:
            # queryset is not instance of CoreBankingTransactions or Transaction
            return queryset
    elif queryset:
        queryset = queryset.filter(date_created__range=[start_date, end_date])

    return queryset

def verify_bvn_account(bvn:str, name_enq_desc:dict) -> bool:
    '''
        This function verifies the bvn against the bvn returned from the account number name enquiry
    '''
    return bvn == name_enq_desc.get('bvn')

def verify_otp(validate_otp_at, sms_otp, request) -> bool:
    '''
        pass the validate_otp_at and sms_otp params into the verify user phone number endpoint
        this end point is being reused to verify otp used in adding accounts and other functionalities
    '''
    url = reverse('verify_user_phone_number', request=request)
    payload = {
        'validate_otp_at' : validate_otp_at,
        'sms_otp' : sms_otp
    }

    headers = {
        'Authorization' : f'Bearer {request.auth.token}',
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    try:
        response = requests.request('POST', url, headers=headers, data=payload)
        response = response.json()
        if response.get('response_code') == settings.SUCCESS_RESPONSE:
            return True
        else:
            return False
    except Exception:
        raise TimeoutError

def get_nam_token(bank_alias=None):
    '''
        Get access token for connection to moneyflex api using the individual bank's credentials
    '''
    if bank_alias:
        headers = {
            'Authorization' : f'Basic {settings.PB_KEYS.get(bank_alias)}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    else:
        headers = {
            'Authorization' : f'Basic {settings.FLEX_KEY}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    
    payload = 'grant_type=client_credentials'
    url = f'{settings.MONEY_FLEX_OPEN_API_BASE_URL}/token'

    try:
        resp = requests.request('POST', url, headers=headers, data=payload)
        resp = resp.json()
        return resp.get('access_token', '')
    except Exception as e:
        return str(e)
    
    
def get_nam_test_token():

    url = "https://openapi.inlaks-nambuit.com:8248/token"

    client_id = "TG14RxI4GMFHw7f4qL0k8yeMUPca"
    client_secret = "GKIQd6BZzV7pQkqKXvx7yfTGezIa"

    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    payload = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret
    }

    try:
        resp = requests.request('POST', url, headers=headers, data=payload)
        print(f"FROM TEST TOKEN >> {resp.status_code}")
        resp = resp.json()
        return resp.get('access_token', '')
    except Exception as e:
        return str(e)

        
def send_otp(request):
    '''
        from the request, call the get endpoint for send sms otp and return the validate_otp_at
        string as response
    '''
    url = reverse('send_user_sms_otp', request=request)

    headers = {
        'Authorization' : f'Bearer {request.auth.token}',
        'Content-Type': 'application/x-www-form-urlencoded'
    }
    try:
        response = requests.request('GET', url, headers=headers)
        response = response.json()
        
        return response
    except Exception:
        raise TimeoutError

def set_response_data(code, description, detail) -> dict:
    '''
        Returns a structured response payload
    '''
    return {
        'response_code' : code,
        'response_description' : description,
        'response_detail' : detail
    }

def logout_user(access_token:str, client_id:str, client_secret:str, token_endpoint:str):
    '''
        revoke the access token by passing the token to the revoke token url
    '''
    payload = f'token={access_token}&client_id={client_id}&client_secret={client_secret}'
    headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    
    try:
        response = requests.request('POST', token_endpoint, headers=headers, data=payload)
        if response.status_code == 200:
            return 'Access Token Revoked'
        return 'Access Token Not Revoked'
    except Exception:
        raise TimeoutError

def otp_rand():
    validate_otp_at = str(datetime.now())
    bad_char = ('-', ':', ' ', '.')
    validate_otp_at = ''.join(filter(lambda x: x not in bad_char, validate_otp_at))
    validate_otp_at = validate_otp_at[10:20]
    return int(validate_otp_at)

def get_email_token(token_endpoint: str):
    '''
        This function generates a machine to machine token using the Email Verification
        Service model's client Id and client secret. Note The Oauth Model must be 
        explictly named "Email Verification Service".
    '''
    try:
        app = Application.objects.get(name='Email Verification Service')
        credential = f'{app.client_id}:{app.client_secret}'
        credential = base64.b64encode(credential.encode('utf-8'))
        credential = str(credential, 'utf-8')

        headers = {
            'Authorization' : f'Basic {credential}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }

        payload ='grant_type=client_credentials'

        response = requests.request('POST', token_endpoint, headers=headers, data=payload)
        response = response.json()

        return response.get('access_token')
    except ObjectDoesNotExist:
        data = {
            'response_code' : settings.FAILED_RESPONSE,
            'response_description' : 'Email Verification Service Does Not Exist',
            'response_detail' : 'Email Verification Service Does Not Exist, Contact Admin'
        }
        return data

def create_bvn_record(
    bvn,
    phone_number,
    first_name,
    last_name,
    dob,
    formatted_dob,
    gender,
    residential_address,
):
    """
    stores bvn record in BVN Table on database
    """
    try:
        bvn = BVN.objects.create(
            bvn=bvn,
            phone_number=phone_number,
            first_name=first_name,
            last_name=last_name,
            dob=dob,
            formatted_dob=formatted_dob,
            gender=gender,
            residential_address=residential_address,
        )
        return bvn
    except Exception:
        pass

def get_transact_query_type(sender_bank, destination_bank):
    '''
        Returns if send money is Intra bank or Inter bank, taking into consideration if 
        bank belongs is connected to nambuit switch or nibss switch
    '''
    
    if destination_bank.nip_code == sender_bank.nip_code:
        return 'INTRA'
    return 'INTER'

def get_channel_object(channel_name:str, status:bool) -> dict:
    return {
        'name' : channel_name,
        'enabled' : status
    }

def get_transactions_data(transactions):
    '''
        Returns a details regarding the particular type of transactions queryset if queryset is not empty
        else return structured dict with all arguments set to 0
    '''
    if transactions:
        transactions_count = transactions.count()
        transactions_success = transactions.filter(status='S')
        transactions_failed = transactions.exclude(status="S")
        transactions_success_count = transactions_success.count()
        transactions_failed_count = transactions_failed.count()
        transactions_value = transactions.aggregate(value=Sum('amount')).get('value')
        transactions_success_value = transactions_success.aggregate(value=Sum('amount')).get('value')
        transactions_failed_value = transactions_failed.aggregate(value=Sum('amount')).get('value')

        return {
            'count' : transactions_count if transactions_count else 0,
            'success_count': transactions_success_count if transactions_success_count else 0,
            'failed_count': transactions_failed_count if transactions_failed_count else 0,
            'value': transactions_value if transactions_value else 0,
            'success_value': transactions_success_value if transactions_success_value else 0,
            'failed_value': transactions_failed_value if transactions_failed_value else 0
        }
    return {
        'count' : 0,
        'success_count': 0,
        'failed_count': 0,
        'value': 0,
        'success_value': 0,
        'failed_value': 0
    }

def clean_transaction_records(core_banking_transactions, omni_channel_transactions) -> list:
    '''
        Gets the transaction record from the core banking and update it with more details for transactions
        originating from omni channel
    '''
    transactions = []
    if len(core_banking_transactions) > 0:
        for transaction in core_banking_transactions:
            if _transact := omni_channel_transactions.filter(
                Q(send_money__core_banking_ref=transaction.get('transactionReference')) | Q(airtime_data__core_banking_ref=transaction.get('transactionReference')) | 
                Q(bill_payment__core_banking_ref=transaction.get('transactionReference'))
            ):
                omni_transaction = _transact[0]
                reciept = {
                    'transaction_details' : {
                        'transaction_date': omni_transaction.content_object.date_created,
                        'transaction_class': 'CREDIT' if transaction.get('type') == 'Credit' else 'DEBIT',
                        'transaction_type': omni_transaction.content_object.transaction_type.name,
                        'amount': transaction.get('amount'),
                        'amount_in_words' : get_amount_in_words(f'{transaction.get("amount")}'),
                        'transaction_description': transaction.get('description'),
                        'transaction_narration': transaction.get('narration'),
                        'customer_reference': omni_transaction.content_object.user_ref,
                        'channel': omni_transaction.content_object.channel
                    },
                    'beneficiary_details': {},
                    'sender_details': {
                        'sender_account_number': omni_transaction.content_object.sender_account.account_number,
                        'sender_name': omni_transaction.content_object.sender_account.profile.customer.get_full_name(),
                        'sender_bank': omni_transaction.content_object.sender_account.bank.name
                    }
                }
                if omni_transaction.content_object:
                    beneficiary_details = {
                        'beneficiary_account_number': omni_transaction.content_object.destination_account_number,
                        'beneficiary_name': omni_transaction.content_object.destination_account_name,
                        'beneficiary_bank': omni_transaction
                    }
                    reciept['beneficiary_details'] = beneficiary_details
                elif omni_transaction.content_object:
                    beneficiary_details = {
                        'biller': omni_transaction.content_object.biller.biller_name,
                        'credited_phone_number' : omni_transaction.content_object.phone_number
                    }
                    reciept['beneficiary_details'] = beneficiary_details
            else:
                reciept = {
                    'transaction_details': {
                        'transaction_date': transaction.get('date'),
                        'transaction_class': 'CREDIT' if transaction.get('type') == 'Credit' else 'DEBIT',
                        'transaction_type': '',
                        'amount': transaction.get('amount'),
                        'amount_in_words' : get_amount_in_words(f'{transaction.get("amount")}'),
                        'transaction_description': transaction.get('description'),
                        'transaction_narration': transaction.get('narration'),
                        'customer_reference': '',
                        'channel': ''
                    },
                    'beneficiary_details': {},
                    'sender_details': {}
                }
            transactions.append(reciept)
        transactions.reverse() # transaction from the core banking comes in order of start date to end date, we want it reversed
        return transactions
    else:
        return transactions

def clean_dob(dob: str, core_banking: bool = True) -> tuple:
    """
    returns a cleaned version of the dob in a tuple format
    """
    if dob and core_banking:
        # for the bvn service from core banking dob comes in the format yyyymmdd so we have to clean it up to yyyy-mm-dd
        formatted_dob = f"{dob[:4]}-{dob[4:6]}-{dob[6:]}"
        date = datetime.strptime(formatted_dob, "%Y-%m-%d")
        dob = datetime.strftime(date, "%d-%b-%y")
        return (dob, formatted_dob)
    elif dob:
        # this bvn service comes from nibss and comes in format dd-mm-yyyy so we have to clean it up to have uniform data
        dob: list = dob.split("-")
        dob.reverse()
        formatted_dob = "-".join(dob)
        date = datetime.strptime(formatted_dob, "%Y-%b-%d")
        dob = datetime.strftime(date, "%d-%b-%y")
        formatted_dob = datetime.strftime(date, "%Y-%m-%d")
        return (dob, formatted_dob)
    return ("", "")
    
def get_amount_in_words(amount:str):
    amount_list = amount.split('.')
    if len(amount_list) == 2:
        first_part = num2words(amount_list[0])
        second_part = '' if num2words(amount_list[1]).lower() == 'zero' else num2words(amount_list[1])
        if second_part:
            return f'{first_part} naira, {second_part} kobo'.title()
        else:
            return f'{first_part} naira'.title()
    elif len(amount_list) == 1:
        return f'{num2words(amount_list[0])} naira'.title()
    else:
        return ''

def get_bank_name_by_code(code):
    '''
        This code could either be nambuit code or nip code, so we query both to get the bank
    '''
    bank = Bank.objects.filter(Q(nip_code=code) | Q(code=code))
    if bank:
        return bank[0].name
    else:
        return ''



def get_reference():
    ref = str(datetime.now())
    bad_char = ('-', ':', ' ', '.')
    ref = ''.join(filter(lambda x: x not in bad_char, ref))
    ref = ref[:20]
    return int(ref)

def get_fee(amount, transaction_type, partner_bank):
    '''
        From the Billing agreement with partner bank we get the fee attached to the amount based on the type of
        transaction
    '''
    try:
        partner_bank_config = Configurations.objects.get(partner_bank=partner_bank, transaction_type=transaction_type)
        fee_list = Fee.objects.filter(partner_bank_config=partner_bank_config)
        if fee_list:
            for fee in fee_list:
                if fee.custom_charge == 'N':
                    # if it ever enters this condition the fee list contains just one element
                    return ((amount * fee.percent_commission) / Decimal('100.00'))
                if fee.is_bounded == 'Y':
                    if amount >= fee.lower_bound and amount <= fee.upper_bound:
                        return fee.fee
                else:
                    # fee not bounded means there is a lower bound but no upper bound
                    if amount >= fee.lower_bound:
                        return fee.fee
            raise Exception(f'Fee configuration for amount: {amount} not set for {partner_bank.name}')
        else:
            raise Exception(f'Fee configuration not set for partner bank {partner_bank.name}')

    except Exception as e:
        # we don't want to break the code so we just return a fee of 0.00 which is safe 
        return Decimal('0.00')

def resolve_billed_transactions(transactions:query.QuerySet, bill_ref:str):
    '''
        update all billed transactions with the bill reference
    '''
    transactions.update(bill_ref=bill_ref, is_resolved=True)

def generate_bill_reports(transactions:query.QuerySet, bill:Bills, status:str, bill_ref:str, transaction_type:str, send_email_func):

    '''
        Generate an Excel report and a PDF report based on the billed transactions
    '''
    log = logging.getLogger('django')

    wb = xlwt.Workbook(encoding='utf-8')
    ws = wb.add_sheet('BILLED TRANSACTIONS')
    row_num = 0
    font_style = xlwt.XFStyle()
    font_style.font.bold = True
    
    if transaction_type == 'send_money':
        # CAREFUL WHEN ADJUSTING
        columns = [
            'user_ref', 'transaction_ref', 'sender', 'amount', 'fee', 'transaction_type', 'channel', 'currency', 
            'sender_account', 'destination_account_number', 'destination_institution_code', 'destination_account_name', 
            'core_banking_ref', 'response_code', 'response_description', 'remark', 'date_created', 'nam_payment_reference', 
            'is_transaction_chargable', 'status'
        ]

        # CAREFUL WHEN ADJUSTING
        rows = transactions.values_list('user_ref', 'transaction_ref', 'sender__customer__username', 'amount', 'fee', 'transaction_type', 'channel', 'currency__name', 
            'sender_account', 'destination_account_number', 'destination_institution_code', 'destination_account_name', 
            'core_banking_ref', 'response_code', 'response_description', 'remark', 'date_created', 'nam_payment_reference', 
            'is_transaction_chargable', 'status')
    elif transaction_type == 'airtime_data':
        # CAREFUL WHEN ADJUSTING
        columns = [
            'user_ref', 'transaction_ref', 'sender', 'amount', 'fee', 'transaction_type', 'channel', 'currency', 
            'sender_account', 'phone_number', 'biller', 'payment_code', 
            'core_banking_ref', 'response_code', 'response_description', 'remark', 'date_created', 'nam_payment_reference', 
            'is_transaction_chargable', 'status'
        ]

        # CAREFUL WHEN ADJUSTING
        rows = transactions.values_list('user_ref', 'transaction_ref', 'sender__customer__username', 'amount', 'fee', 'transaction_type', 'channel', 'currency__name', 
            'sender_account', 'phone_number', 'biller__biller_name', 'payment_code',
            'core_banking_ref', 'response_code', 'response_description', 'remark', 'date_created', 'nam_payment_reference', 
            'is_transaction_chargable', 'status')
    elif transaction_type == 'bill_payment':
        # CAREFUL WHEN ADJUSTING
        columns = [
            'user_ref', 'transaction_ref', 'sender', 'amount', 'fee', 'transaction_type', 'channel', 'currency', 
            'sender_account', 'customer_field_1', 'customer_field_2', 'biller_category', 'biller_item', 'payment_code', 
            'core_banking_ref', 'response_code', 'response_description', 'remark', 'date_created', 'nam_payment_reference',
            'is_transaction_chargable', 'status'
        ]

        # CAREFUL WHEN ADJUSTING
        rows = transactions.values_list('user_ref', 'transaction_ref', 'sender__customer__username', 'amount', 'fee', 'transaction_type', 'channel', 'currency__name', 
            'sender_account', 'customer_field_1', 'customer_field_2', 'biller_category__name', 'biller_item__biller_name', 'payment_code', 
            'core_banking_ref', 'response_code', 'response_description', 'remark', 'date_created', 'nam_payment_reference',
            'is_transaction_chargable', 'status')

    for column_num, elem in enumerate(columns):
        ws.write(row_num, column_num, elem, font_style)

    font_style = xlwt.XFStyle()
    
    for row in rows:
        row_num += 1
        for column_num, elem in enumerate(row):
            if column_num == 10:
                # this is the destination_institution_code we want to convert it to get the destination institution name which is more useful
                destination_bank_name = get_bank_name_by_code(elem)
                ws.write(row_num, column_num, str(destination_bank_name), font_style)
            else:
                ws.write(row_num, column_num, str(elem), font_style)
    
    # create a temp file to store the excel sheet
    
    time =bill.date_created.strftime('%I-%M-%S%p')
    date = bill.date_created.strftime('%b%d%Y')
    file_name = f'{bill.partner_bank_config.transaction_type}_bill_{date}_{time}.xlsx'

    if status == 'success':
        '''
            updating all transactions with bill_ref and is_resolved set to True. Doing this update here
            because the update method here will make the QuerySet Empty because the conditions of the 
            query is updated. The QuerySet is needed to complete the previous steps hence the reason 
            update is done at this point
        '''
        log.info('FROM GENERATING BILL REPORT: Trying to update all transactions with bill ref and is_resolved set to True')
        transactions.update(bill_ref=bill_ref, is_resolved=True)
        log.info('FROM GENERATING BILL REPORT: All Transactions updated successfully')

    try:
        # save into the tmp file
        wb.save(file_name)
        # try to save this file into the bill
        with open(file_name, 'rb') as f:
            bill.reciept_excel.save(file_name, File(f))
        bill.save()
        # Delete the temporary file
        os.remove(file_name)

        log.info('FROM GENERATING BILL REPORT: Bill Report <Excel> GENERATED Successfully')
    except Exception as e:
        log.error(f'ERROR FROM GENERATING BILL REPORT: {e}')
        return
    
    try:
        # try to send an email to the partner bank with the report
        time =bill.date_created.strftime('%I:%M:%S %p')
        date = bill.date_created.strftime('%b %d, %Y')
        if status == 'success':
            message = f'Please be informed that your omni channel bill with bill reference {bill_ref} was successfully deducted at {time}, (GMT+1)  {date}. Attached to this mail is spreadsheet containing list of resolved transactions'
            status_color = '#036bfc'
        else:
            message = f'Please be informed that at {time}, (GMT+1)  {date} we were unable to deduct fees attached to your Omni Channel Bill. Kindly help confirm funds availability in the prefund account: {bill.partner_bank_config.prefund_account}. \nAttached to this mail is spreadsheet containing list of transactions yet to be resolved'
            status_color = '#fc4103'
        email_args = {
            'email_from': settings.DEFAULT_FROM_EMAIL,
            'to': bill.partner_bank_config.partner_bank.email,
            'subject': 'OMNI CHANNEL BILLING',
            'file_path': f'{bill.reciept_excel.path}',
            'file_name': f'{bill.reciept_excel.name.split("/")[-1]}',
            'file_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'customer_first_name': f'{bill.partner_bank_config.partner_bank.name}'.upper(),
            'message': message,
            'img_url': settings.EMAIL_DEFAULT_IMAGE_URL,
            'foreground': settings.EMAIL_DEFAULT_FOREGROUND,
            'background': settings.EMAIL_DEFAULT_BACKGROUND,
            'total_amount': str(bill.total_amount),
            'total_amount_words': get_amount_in_words(str(bill.total_amount)),
            'no_of_transactions': str(bill.no_of_transactions),
            'status': 'RESOLVED' if status == 'success' else 'NOT RESOLVED',
            'status_color': status_color
        }
        send_email_func(email_args, 'BILLING')
        log.info('FROM GENERATING BILL REPORT: Bill Report Sent Successfully')
    except Exception as e:
        log.error(f'ERROR FROM GENERATING BILL REPORT: {e}')
        return

def is_pin_valid(pin, customer_profile) -> bool:
    '''
        Return True or False if customer pin is valid
    '''
    return check_password(pin, customer_profile.pin)

def is_bank_admin_username_valid(authenticated_admin_username:str, admin_username:str) -> bool:
    '''
        validate that the user about to created is valid and is on the same mail server as the
        super admin
    '''
    try:
        auth_split = authenticated_admin_username.split('@')
        admin_split = admin_username.split('@')
        if auth_split[-1] == admin_split[-1]:
            return True
        return False
    except Exception:
        return False

def get_onboarding_status(user: User) -> str:
    '''
        Get the Onboarding status of user by checking which set conditions
        such as bvn phone verification and adding of bank account
    '''
    customer_profile = user.customerprofile
    bvn_phone_verification_status = customer_profile.is_phone_number_verified
    if not bvn_phone_verification_status:
        return 'PENDING_BVN_PHONE_NUMBER_VERIFICATION'
    # if customer_profile.status == 'I':
    #     return 'CUSTOMER_PROFILE_SET_TO_INACTIVE'
    if customer_profile.no_of_customer_account == 0:
        return 'PENDING_BANK_ACCOUNT_CREATION'
    # if not customer_profile.is_email_verified:
    #     return 'PENDING_EMAIL_VERIFICATION'
    return 'ONBOARDING_COMPLETE'

def update_beneficiary(account_name:str, account_number:str, user:User, beneficiary_bank_code:str) -> None:
    '''
        It get's or create beneficiary and the update the transaction count to describe how frequent
        the beneficiary is being transacted with.
    '''
    if account_name and account_number and user and beneficiary_bank_code:
        try:
            beneficiary_bank = Bank.objects.filter(Q(nip_code=beneficiary_bank_code) | Q(code=beneficiary_bank_code))
            if beneficiary_bank:
                beneficiary_bank = beneficiary_bank[0]
            else:
                raise ObjectDoesNotExist
        except ObjectDoesNotExist:
            raise Exception('Beneficiary Bank/Institution Code Invalid')
        beneficiary, created = Beneficiary.objects.get_or_create(
            account_number=account_number,
            user=user,
        )
        if created:
            beneficiary.account_name = account_name
            beneficiary.bank = beneficiary_bank
        beneficiary.transactions_count = beneficiary.transactions_count + 1
        beneficiary.save()

def account_has_platform_access(platform: str, account: CustomerAccount) -> bool:
    platform = platform.upper()
    if platform.startswith("MOBILE") and account.mobile_channel_access == True:
        return True
    elif platform.startswith("BROWSER") and account.web_channel_access == True:
        return True
    elif platform.startswith("USSD") and account.ussd_channel_access == True:
        return True
    elif platform.startswith("DFA"):
        return True
    else:
        return False

def is_sender_verifed(sender:CustomerProfile) -> bool:
    if sender.is_email_verified == True and sender.is_phone_number_verified == True:
        return True
    return False

def get_serializer_key_error(errors_dict: dict):
    '''
        from serializer.error, we get the very first error from the error dictionary and return
    '''
    try:
        key = list(errors_dict)[0]
        error = errors_dict.get(key)
        return f'{key} -> {error[0]}'
    except Exception:
        return ''

def clean_login_log_data(data):
    '''
        Login response data for logs returns a base 64 encode for bank logo which makes the log very difficult
        to read so this function cleans the returned login log
    '''
    _data = deepcopy(data)
    _customer_bank_profiles = _data['response_detail']['customer_bank_profiles']
    for _bank_profile in _customer_bank_profiles:
        _bank = _bank_profile.get('bank')
        if _bank:
            _bank.pop('bank_logo_b64_encode')
    
    return _data

def clean_package_options(package_options: list) -> list:
    '''
        retrieves the package options list and cleans the packages into simple and standard naming convention used
        through out the project
    '''
    _package_options = []
    if len(package_options) > 0:
        for package_option in package_options:
            _package_option = {
                'payment_item_name': package_option.get('paymentitemname'),
                'amount': package_option.get('amount'),
                'payment_code': package_option.get('paymentCode')
            }
            _package_options.append(_package_option)
        _package_options.sort(key=sort_by_amount)
    return _package_options

def sort_by_amount(elem):
    '''
        Custom function made for sorting package options by amount
    '''
    return int(elem.get('amount', '0'))

def fund_settlement_account(
    sender_account, amount, settlement_account, app_code, send_money, narration=None
):
    """
    For Airtime and Bill payment transaction it is required to credit a settlement account
    before continuing with the transaction
    """
    log = logging.getLogger('omni_send_money')
    # intra transaction so both sender and settlement account share the same institution_code
    institution_code = (
        settlement_account.bank.code
        if settlement_account.bank.code
        else settlement_account.bank.nip_code
    )

    bank = Bank.objects.filter(Q(nip_code=institution_code) | Q(code=institution_code)).first()
    sender = CustomerAccount.objects.filter(account_number=sender_account, bank=bank).first()
    sender_name = sender.profile.customer.get_full_name()
    if not sender:
        sender = sender = AgentAccount.objects.filter(account_number=sender_account, bank=bank).first()
        sender_name = sender.agent.get_full_name()
    payload_class = SendMoneyClass()

    payload_class.send_money_type = "INTRA"
    payload_class.sender_account = (
        sender_account if type(sender_account) == str else sender_account.account_number
    )
    payload_class.destination_account_name = settlement_account.account_name
    payload_class.sender_account_name = sender_name
    payload_class.bank_name = bank.name
    payload_class.sender_institution_code = institution_code
    payload_class.destination_account = settlement_account.account_number
    payload_class.destination_institution_code = institution_code
    payload_class.amount = amount
    payload_class.narration = (
        narration
        if narration
        else f"FT{payload_class.sender_account} {settlement_account.account_number}"
    )
    payload_class.transaction_ref = get_reference()

    resp = send_money(bank, payload_class, app_code)
    log.info(f"Response from funding settlement account {resp}")
    if resp.get("response_code") == settings.SUCCESS_RESPONSE:
        response_detail = resp.get("response_detail", {})
        return {
            "response_code": settings.SUCCESS_RESPONSE,
            "core_banking_ref": payload_class.transaction_ref,
            "nam_payment_ref": response_detail.get("nam_payment_reference"),
        }
    return {
        "response_code": settings.FAILED_RESPONSE,
        "response_description": resp.get("response_description")
        or "Unable to Complete Transaction",
        "response_detail": resp.get("response_detail")
        or "Unable to Complete Transaction",
        "core_banking_ref": payload_class.transaction_ref,
    }


def account_settlement_reversal(
    sender_account,
    amount,
    settlement_account,
    app_code,
    transaction,
    send_money,
    narration=None,
):
    """
    For failed transactions at the switch level which we have already debited customer account
    for we'd need to reverse the amount
    """
    # we can easily re use the fund settlement account function by just switching the sender and reciever this time around
    log = logging.getLogger("django")
    log.info(
        f"Trying to do account reversal for {sender_account.account_number}  of amount {amount} from settlement account"
    )
    resp = fund_settlement_account(
        sender_account=settlement_account,
        amount=amount,
        settlement_account=sender_account,
        app_code=app_code,
        send_money=send_money,
        narration=narration,
    )
    transaction.response_description = (
        "Reversal Successful"
        if resp.get("response_code") == settings.SUCCESS_RESPONSE
        else "Reversal Failed"
    )
    transaction.response_code = settings.FAILED_RESPONSE

    if resp.get("response_code") == settings.SUCCESS_RESPONSE:
        transaction.status = "R"
        log.info(
            f"REVERSAL of amount {amount} from settlement account to {sender_account.account_number} was SUCCESSFUL"
        )
    else:
        transaction.status = "P"
        log.info(
            f"REVERSAL of amount {amount} from settlement account to {sender_account.account_number} FAILED"
        )
    transaction.save()

def get_daily_limit(account_type, bank):
    '''
        returns the daily limit attached to the particular account type for a particular bank
    '''
    if account_type.code == 'savings':
        return bank.savings_account_limit
    elif account_type.code == 'current':
        return bank.current_account_limit
    return Decimal('0.00')

def within_daily_limit(amount, account) -> bool:
    '''
        return True or False if adding the amount to the cummulative amount is going to be within
        the daily limit
    '''
    if (amount + account.daily_cummulative) <=  account.daily_limit:
        return True
    return False

def update_daily_cummulative(amount, account):
    """
    adds the amount to the daily cummulative
    """
    cummulative = account.daily_cummulative + amount
    if cummulative < 0:
        cummulative = Decimal("0.00")
    account.daily_cummulative = cummulative
    account.save()

def send_ticket_creation_notification(partner_bank:Bank, ticket:dict, send_email_func):
    '''
        Notifies partner bank via mail to the escalation mail provided by partner bank
    '''
    message = f'Hello {partner_bank.name}\n \n \n \nNEW TICKET CREATED\n \n \nCreated By: \n \t{ticket.get("created_by")}\n \nSubject :\n \t{ticket.get("subject")}\n \nBody:\n \t{ticket.get("body")}'

    email_args = {
        'email_from': settings.DEFAULT_FROM_EMAIL,
        'to': partner_bank.email,
        'subject': 'OMNI CHANNEL TICKET NOTIFICATION',
        'message': message
    }

    send_email_func(email_args, 'INTERNAL_NOTIFICATION')

def get_week_of_month(date):

    week_num = date.isocalendar()[1] - date.replace(day=1).isocalendar()[1] + 1
    return week_num
    

def ussd_authentication(phone_number: str, app_code: str) -> User:
    """
    Returns a user based on user phone number and app_code else raise authentication failed
    error
    """
    log = logging.getLogger("django")
    try:
        # query is filter because it is possible to have multiple profile which translates to a single user having accounts in different banks
        profiles = CustomerProfile.objects.filter(phone_number=phone_number)
       
        profile_exists = False

        if profiles:
            for profile in profiles:
                #log.info("env_app_code" + profile.customer.app_code)
                if profile.customer.app_code == app_code:
                    #log.info("app_code :" + app_code)   
                    #log.info("env_app_code :" + profile.customer.app_code)
                    # at this point we have gotten the correct profile
                    profile_exists = True
                    customer = profile.customer
                    #log.info("after get customer")
                    break
        else:
            # profile does not exist means customer does not exist
            raise Exception

        if profile_exists:
            return customer
        raise Exception
    except Exception:
        raise AuthenticationFailed("Invalid User Credentials")


def get_latest_app_version():
    """
    Returns latest version for mobile app on the different platform it
    exists
    """
    try:
        platforms = Platform.objects.all()
        app_version = {}
        for platform in platforms:
            try:
                latest_platform_app_version = MobileApplicationVersion.objects.filter(
                    platform=platform, date_created__isnull=False
                ).latest("date_created")
                app_version[latest_platform_app_version.platform.code] = {
                    "version": latest_platform_app_version.version_id,
                    "platform": latest_platform_app_version.platform.name,
                    "description": latest_platform_app_version.description,
                    "date_created": latest_platform_app_version.date_created,
                }
            except MobileApplicationVersion.DoesNotExist:
                continue
        return app_version
    except Exception:
        return {}


def get_reciever_details(sender_account: str) -> dict:
    """
    pops reciever details from internal nameenquiry dynamic memory
    """
    name_enq_dict = cache.get("name_enq_dict")
    if name_enq_dict:
        reciever_details = name_enq_dict.pop(sender_account, {})
        cache.set("name_enq_dict", name_enq_dict)
        return reciever_details
    return {}


def retrieve_reciever_details(sender_account: str) -> dict:
    """
    retrieves reciever details from internal name enquiry dynamic memory without deleting it
    """
    name_enq_dict = cache.get("name_enq_dict")
    if name_enq_dict:
        reciever_details = name_enq_dict.get(sender_account, {})
        return reciever_details
    return {}


def add_reciever_details(sender_account: str, details: dict):
    name_enq_dict = cache.get("name_enq_dict")
    if name_enq_dict:
        name_enq_dict[sender_account] = details
    else:
        name_enq_dict = {sender_account: details}
    cache.set("name_enq_dict", name_enq_dict)


def reciever_details_available(sender_account: str) -> bool:
    name_enq_dict = cache.get("name_enq_dict")
    if name_enq_dict:
        if sender_account in name_enq_dict.keys():
            return True
    return False


def run_settlement(
    bill_config: Configurations,
    bill_id: int,
    transaction_params: Dict[str, any],
    app_code: str,
    send_money_func,
    name_enquiry_func,
):
    """
    this function runs settlement billing for airtime/data and bill payment,
    based on agreement with the bank, it settlesthe bank for its own profit and
    funds the switch account for its own capital.
    """
    log = logging.getLogger("django")

    transaction_type = bill_config.transaction_type
    partner_bank = bill_config.partner_bank.name
    sender_account = bill_config.prefund_account
    sender_institution_code = bill_config.partner_bank.code
    switch_account = bill_config.switch_settlement_account
    switch_institution_code = bill_config.switch_settlement_bank.nip_code

    try:
        # run settlement for bank
        log.info(f"running {transaction_type} settlement for {partner_bank}")
        payload_class = SendMoneyClass()

        payload_class.send_money_type = "INTRA"
        payload_class.sender_account = sender_account
        payload_class.sender_institution_code = sender_institution_code
        payload_class.destination_account = bill_config.fee_settlement_account
        payload_class.destination_institution_code = sender_institution_code
        payload_class.amount = transaction_params.get("partner_bank_settlement_amount")
        payload_class.narration = (
            f"FT{sender_account} {bill_config.fee_settlement_account}"
        )
        payload_class.transaction_ref = get_reference()

        log.info(
            f"{transaction_type} settlement for {partner_bank}, calling send money function"
        )
        resp = send_money_func(payload_class, app_code)
        log.info(
            f"{transaction_type} settlement for {partner_bank}, response from send money {resp}"
        )

        if resp.get("response_code") == settings.SUCCESS_RESPONSE:
            # update bill
            Bills.objects.filter(id=bill_id).update(
                is_partner_bank_settlement_reconciled=True
            )
            log.info(f"{transaction_type} settlement for {partner_bank} SUCCESSFUL")
        else:
            log.info(f"{transaction_type} settlement for {partner_bank} FAILED")
    except Exception as e:
        log.info(
            f"Error while running {transaction_type} settlement for {partner_bank}: ERROR -> {e}"
        )

    try:
        switch_settlement(
            bill_config,
            bill_id,
            transaction_params,
            app_code,
            send_money_func,
            name_enquiry_func,
        )
    except Exception as e:
        log.info(
            f"Error while running {transaction_type} settlement for {partner_bank}: ERROR -> {e}"
        )


def switch_settlement(
    bill_config: Configurations,
    bill_id: int,
    transaction_params: Dict[str, any],
    app_code: str,
    send_money_func,
    name_enquiry_func,
):

    transaction_type = bill_config.transaction_type
    partner_bank = bill_config.partner_bank.name
    sender_account = bill_config.prefund_account
    sender_institution_code = bill_config.partner_bank.code
    switch_account = bill_config.switch_settlement_account
    switch_institution_code = bill_config.switch_settlement_bank.nip_code

    # run settlement for switch
    log = logging.getLogger("django")
    log.info(
        f"running switch settlement for {partner_bank} {transaction_type} transactions"
    )

    send_money_type = (
        "INTRA"
        if bill_config.partner_bank == bill_config.switch_settlement_bank
        else "INTER"
    )

    if send_money_type == "INTER":
        if (
            not reciever_details_available(sender_account)
            or retrieve_reciever_details(sender_account).get("reciever_account")
            != switch_account
        ):
            # check if reciever details is not stored locally or if the currently stored details is invalid
            # we run name enquiry again to populate our local dynamic memory
            # NOTE: RUN THIS ASYNCRONOUSLY in future
            name_enquiry_func(
                account_number=switch_account,
                institution_code=switch_institution_code,
                sender_account_number=sender_account,
                sender_institution_code=sender_institution_code,
                type="INTER",
                app_code=app_code,
            )

    payload_class = SendMoneyClass()
    payload_class.send_money_type = send_money_type
    payload_class.sender_account = sender_account
    payload_class.sender_institution_code = sender_institution_code
    payload_class.destination_account = switch_account
    payload_class.destination_institution_code = switch_institution_code
    payload_class.amount = transaction_params.get("switch_settlement_amount")
    payload_class.narration = f"FT{sender_account} {switch_account}"
    payload_class.transaction_ref = get_reference()

    log.info(
        f"{partner_bank} {transaction_type} switch settlement, calling send money function"
    )
    resp = send_money_func(payload_class, app_code)
    log.info(
        f"{partner_bank} {transaction_type} switch settlement, response from send money {resp}"
    )

    if resp.get("response_code") == settings.SUCCESS_RESPONSE:
        # update bill
        Bills.objects.filter(id=bill_id).update(is_switch_settlement_reconciled=True)
        log.info(f"{partner_bank} {transaction_type} switch settlement SUCCESSFUL")
    else:
        log.info(f"{partner_bank} {transaction_type} switch settlement FAILED")


def clean_mfs_banks(banks: list) -> list:
    """
    returns a cleaned bank list
    """
    _banks = []
    if banks:
        for bank in banks:
            bank_limit = bank.get("bankLimit", {})
            _bank = {
                "bank_name": bank.get("bankName"),
                "country_code": bank.get("countryCode"),
                "currency_code": bank.get("currencyCode"),
                "mfs_bank_code": bank.get("mfsBankCode"),
                "bank_limit": {
                    "max_daily_value": bank_limit.get("maxDailyValue"),
                    "max_weekly_value": bank_limit.get("maxWeeklyValue"),
                    "max_monthly_value": bank_limit.get("maxMonthlyValue"),
                    "max_per_tx_limit": bank_limit.get("maxPerTxLimit"),
                    "min_per_tx_limit": bank_limit.get("minPerTxLimit"),
                },
            }
            _banks.append(_bank)
    return _banks


def get_international_send_money_transaction_params(
    amount: Decimal, exchange_rate: dict
) -> dict:
    """
    Retrieve exchange rate config from cache, then we calculate the reciever amount in reciever
    currency and the exchange rate add on. We return this two details in a dictionary.
    """
    omni_reciever_amount = amount * exchange_rate.get("omni_exchange_rate")
    real_reciever_amount = amount * exchange_rate.get("exchange_rate")
    add_on = real_reciever_amount - omni_reciever_amount
    exchange_rate_add_on = add_on / exchange_rate.get("omni_exchange_rate")

    return {
        "real_reciever_amount": real_reciever_amount.quantize(Decimal("1.00")),
        "exchange_rate_add_on": exchange_rate_add_on.quantize(Decimal("1.00")),
        "omni_reciever_amount": omni_reciever_amount.quantize(Decimal("1.00")),
    }



def get_international_send_money_transaction_fee_list(
    partner_bank: Bank, country: Countries
) -> list:
    """
    generates a list of objects describing fee structure for transactions within a particular range,
    gets the fixed omni channel fee for transaction range and multiplies it against individual country
    percent config
    """
    try:
        config = Configurations.objects.get(
            partner_bank=partner_bank, transaction_type="i_send_money"
        )
        fee_configurations = Fee.objects.filter(partner_bank_config=config)
        transaction_list = []
        if fee_configurations:
            for fee_config in fee_configurations:
                fee_object = {
                    "lower_bound": fee_config.lower_bound,
                    "upper_bound": fee_config.upper_bound
                    if fee_config.upper_bound
                    else Decimal("*************.99"),
                    "fee": (
                        fee_config.fee
                        * (country.percent_transaction_fee / Decimal("100.00"))
                    ).quantize(Decimal("1.00")),
                }
                transaction_list.append(fee_object)
        else:
            transaction_list = [
                {
                    "lower_bound": 0.00,
                    "upper_bound": *************.99,
                    "fee": 0.00,
                }
            ]
        return transaction_list
    except Exception as e:
        log = logging.getLogger("django")
        log.info(f"Error getting international send money fee list {e}")
        return [
            {
                "lower_bound": 0.00,
                "upper_bound": *************.99,
                "fee": 0.00,
            }
        ]


def get_local_inter_bank_send_money_transaction_charge(amount: Decimal) -> Decimal:
    """
    Returns the nibss charge for inter bank transactions based on the amount
    """
    if amount <= Decimal("5000.00"):
        # for 5000 and lower the approximated nibbs transaction charge including tax
        return Decimal("11.00")
    elif amount <= Decimal("50000.00"):
        # for 50000 and greater than 5000 the approximated nibbs transaction charge including tax
        return Decimal("27.00")
    elif amount > Decimal("50000.00"):
        # for greater than 5000 the approximated nibbs transaction charge including tax
        return Decimal("54.00")


def get_transaction_status(
    transaction, transaction_type: str, send_money_func, switch_tsq_func
) -> Optional[str]:
    """
    Returns transaction status based on transaction_type
    """
    try:
        transaction = transaction.content_object
        if transaction_type in ["airtime_data", "bill_payment"]:
            transaction_status, _ = switch_tsq_func(
                transaction.core_banking_ref, transaction_type
            )
            if transaction_status == "Failed" and transaction.status != "R":
                sender_account = transaction.sender_account
                settlement_account = SettlementAccount.objects.get(
                    transaction_type__code=f"{transaction_type.upper()}",
                    bank=sender_account.bank,
                )
                amount = str(transaction.amount)
                app_code = sender_account.profile.customer.app_code
                account_settlement_reversal(
                    sender_account,
                    amount,
                    settlement_account,
                    app_code,
                    transaction,
                    send_money_func,
                    narration=f"{'AIRTIME' if transaction_type == 'airtime_data' else 'BILLPAYMENT'} REVERSAL {transaction.amount}",
                )
            elif transaction_status == "Complete" and transaction.status != "S":
                transaction.response_code = settings.SUCCESS_RESPONSE
                transaction.response_description = f"{'AIRTIME/DATA' if transaction_type == 'airtime_data' else 'BILL PAYMENT'} SUCCESSFUL"
                transaction.status = "S"
                transaction.is_resolved = False
                transaction.is_transaction_chargable = True
                transaction.save()

                update_daily_cummulative(transaction.amount, transaction.sender_account)
        elif transaction_type == "send_money":
            pass
        elif transaction_type == "international_send_money":
            pass

        return transaction.status
    except Exception as e:
        log = logging.getLogger("django")
        log.info(
            f"Error from getting transaction status for {transaction_type} transaction -> Error : {e}"
        )
        return None


def get_currency_to_usd_from_usd_to_currency(fx_rate: Decimal) -> Decimal:
    """
    returns currency convertion to USD have currency set to 1 example
    1NGN -> 0.00115 USD
    1POUND -> 1.38 USD
    """
    return 1 / fx_rate


def get_omni_channel_usd_to_naira_config() -> tuple:
    """
    returns preset 1 USD to naira config from omni channel
    """
    currency_rate = CurrencyRate.objects.get(
        base_currency_code="USD", source_currency_code="NGN"
    )
    usd_to_naira = currency_rate.source_conversion_rate
    omni_usd_to_naira = currency_rate.omni_conversion_rate

    return (usd_to_naira, omni_usd_to_naira)


def generate_rand_password() -> str:
    """
    Generates a random 7 character password from a combination of upper case letters,
    lower case letters, numbers and special characters
    """
    combination_options = (
        f"{string.ascii_lowercase}{string.ascii_uppercase}{string.digits}@$"
    )
    password = "".join(random.choice(combination_options) for i in range(7))
    return password


def get_agent_username(bank_alias, phone_number):
    """
    Generates a unique agent code based on the bank alias, last 4 digits of the mobile
    number and a randomly generated 3 character string.

    sample agent code -> CHMFB0234XYZ
    """
    last_4_digits_mobile_number = phone_number[-4:]
    rand_string = "".join(random.choice("ABCDEFGHIJKLMNOPQRSTUVWXYZ") for i in range(3))

    agent_code = bank_alias + last_4_digits_mobile_number + rand_string

    return agent_code.upper()


def mock_get_package_options(type: str):
    if type == "data":
        return {
            "response_code": "00",
            "response_description": "Get Package Options Successful",
            "response_detail": {
                "package_options": [
                    {
                        "payment_item_name": "75MB 1 day plan for N100",
                        "amount": "100",
                        "payment_code": "********",
                    },
                    {
                        "payment_item_name": "200MB 2 days plan for N200",
                        "amount": "200",
                        "payment_code": "********",
                    },
                    {
                        "payment_item_name": "350MB 1 week plan for N300",
                        "amount": "300",
                        "payment_code": "********",
                    },
                    {
                        "payment_item_name": "1GB 1 day plan for N300",
                        "amount": "300",
                        "payment_code": "34814343",
                    },
                    {
                        "payment_item_name": "Xtratalk Weekly Bundle for N300",
                        "amount": "300",
                        "payment_code": "34814372",
                    },
                    {
                        "payment_item_name": "Xtradata 300MB Weekly Bundle for N300",
                        "amount": "300",
                        "payment_code": "34814373",
                    },
                    {
                        "payment_item_name": "1GB 1 week plan for N500",
                        "amount": "500",
                        "payment_code": "34814323",
                    },
                    {
                        "payment_item_name": "2.5GB 2 days plan for N500",
                        "amount": "500",
                        "payment_code": "34814345",
                    },
                    {
                        "payment_item_name": "750MB 2 weeks plan for N500",
                        "amount": "500",
                        "payment_code": "34814347",
                    },
                    {
                        "payment_item_name": "Xtratalk Weekly Bundle for N500",
                        "amount": "500",
                        "payment_code": "34814374",
                    },
                    {
                        "payment_item_name": "Xtradata 750MB 2 Weeks Bundle for N500",
                        "amount": "500",
                        "payment_code": "34814375",
                    },
                    {
                        "payment_item_name": "750MB 1 Week plan for N500",
                        "amount": "500",
                        "payment_code": "34814389",
                    },
                    {
                        "payment_item_name": "2GB 2 days plan for N500",
                        "amount": "500",
                        "payment_code": "34814390",
                    },
                    {
                        "payment_item_name": "1.5GB 30 days plan for N1,000 (Mobile)",
                        "amount": "1000",
                        "payment_code": "34814348",
                    },
                    {
                        "payment_item_name": "Xtratalk Monthly Bundle for N1000",
                        "amount": "1000",
                        "payment_code": "34814376",
                    },
                    {
                        "payment_item_name": "Xtradata 1.5GB Monthly Bundle for N1000",
                        "amount": "1000",
                        "payment_code": "34814377",
                    },
                    {
                        "payment_item_name": "2GB 30 days plan for N1,200",
                        "amount": "1200",
                        "payment_code": "34814349",
                    },
                    {
                        "payment_item_name": "3GB 30 days plan for N1,500",
                        "amount": "1500",
                        "payment_code": "34814309",
                    },
                    {
                        "payment_item_name": "6GB 1 week plan for N1,500",
                        "amount": "1500",
                        "payment_code": "34814338",
                    },
                    {
                        "payment_item_name": "4.5GB 30 days plan for N2,000",
                        "amount": "2000",
                        "payment_code": "34814325",
                    },
                    {
                        "payment_item_name": "Xtratalk Monthly Bundle for N2000",
                        "amount": "2000",
                        "payment_code": "34814378",
                    },
                    {
                        "payment_item_name": "Xtradata 4.5GB Monthly Bundle for N2000",
                        "amount": "2000",
                        "payment_code": "34814379",
                    },
                    {
                        "payment_item_name": "6GB 30 days plan for N2,500",
                        "amount": "2500",
                        "payment_code": "34814311",
                    },
                    {
                        "payment_item_name": "10GB+4GB YouTube Night 30 days plan for N3,000",
                        "amount": "3000",
                        "payment_code": "34814312",
                    },
                    {
                        "payment_item_name": "12GB+4GB YouTube Night 30 days plan for N3,500",
                        "amount": "3500",
                        "payment_code": "34814351",
                    },
                    {
                        "payment_item_name": "20GB+4GB YouTube Night 30 days plan for N5000",
                        "amount": "5000",
                        "payment_code": "34814352",
                    },
                    {
                        "payment_item_name": "Xtratalk Monthly Bundle for N5000",
                        "amount": "5000",
                        "payment_code": "34814380",
                    },
                    {
                        "payment_item_name": "Xtradata 15GB Monthly Bundle for N5000",
                        "amount": "5000",
                        "payment_code": "34814381",
                    },
                    {
                        "payment_item_name": "25GB 30 days plan for N6,000",
                        "amount": "6000",
                        "payment_code": "34814339",
                    },
                    {
                        "payment_item_name": "40GB 30 days plan for N10,000",
                        "amount": "10000",
                        "payment_code": "34814353",
                    },
                    {
                        "payment_item_name": "Xtratalk Monthly Bundle for N10000",
                        "amount": "10000",
                        "payment_code": "34814383",
                    },
                    {
                        "payment_item_name": "Xtradata 30GB Monthly Bundle for N10000",
                        "amount": "10000",
                        "payment_code": "34814384",
                    },
                    {
                        "payment_item_name": "30GB for N13,500 (SME Data Share Bundle)",
                        "amount": "13500",
                        "payment_code": "34814326",
                    },
                    {
                        "payment_item_name": "75GB 30 days plan for N15,000",
                        "amount": "15000",
                        "payment_code": "34814354",
                    },
                    {
                        "payment_item_name": "Xtratalk Monthly Bundle for N15000",
                        "amount": "15000",
                        "payment_code": "34814385",
                    },
                    {
                        "payment_item_name": "Xtradata 50GB Monthly Bundle for N15000",
                        "amount": "15000",
                        "payment_code": "34814386",
                    },
                    {
                        "payment_item_name": "110GB 30 days plan for N20,000",
                        "amount": "20000",
                        "payment_code": "34814332",
                    },
                    {
                        "payment_item_name": "75GB 60 days plan for N20,000",
                        "amount": "20000",
                        "payment_code": "34814355",
                    },
                    {
                        "payment_item_name": "Xtratalk Monthly Bundle for N20000",
                        "amount": "20000",
                        "payment_code": "34814387",
                    },
                    {
                        "payment_item_name": "Xtradata 70GB Monthly Bundle for N20000",
                        "amount": "20000",
                        "payment_code": "34814388",
                    },
                    {
                        "payment_item_name": "120GB 60 days plan for N30,000",
                        "amount": "30000",
                        "payment_code": "34814356",
                    },
                    {
                        "payment_item_name": "90GB for N40,000 (SME Data Share Bundle)",
                        "amount": "40000",
                        "payment_code": "34814327",
                    },
                    {
                        "payment_item_name": "150GB 90 days plan for N50,000",
                        "amount": "50000",
                        "payment_code": "34814357",
                    },
                    {
                        "payment_item_name": "150GB for N65,000 (SME Data Share Bundle)",
                        "amount": "65000",
                        "payment_code": "34814318",
                    },
                    {
                        "payment_item_name": "250GB 90 days plan for N75,000",
                        "amount": "75000",
                        "payment_code": "34814358",
                    },
                    {
                        "payment_item_name": "325GB 6 months plan for N100,000",
                        "amount": "100000",
                        "payment_code": "34814317",
                    },
                    {
                        "payment_item_name": "400GB 1 year plan for N120,000",
                        "amount": "120000",
                        "payment_code": "34814337",
                    },
                    {
                        "payment_item_name": "1000GB 1 year plan for N300,000",
                        "amount": "300000",
                        "payment_code": "34814316",
                    },
                    {
                        "payment_item_name": "1500GB 1 year plan for N450,000",
                        "amount": "450000",
                        "payment_code": "34814315",
                    },
                ]
            },
        }
    else:
        return {
            "response_code": "00",
            "response_description": "Get Package Options Successful",
            "response_detail": {
                "package_options": [
                    {
                        "payment_item_name": "Lite",
                        "amount": "410",
                        "payment_code": "459128",
                    },
                    {
                        "payment_item_name": "GOtv Smallie - monthly",
                        "amount": "800",
                        "payment_code": "459122",
                    },
                    {
                        "payment_item_name": "Lite (Quarterly)",
                        "amount": "1080",
                        "payment_code": "459130",
                    },
                    {
                        "payment_item_name": "GOtv Jinja Bouquet",
                        "amount": "1640",
                        "payment_code": "459120",
                    },
                    {
                        "payment_item_name": "GOtv Smallie - quarterly",
                        "amount": "2100",
                        "payment_code": "459123",
                    },
                    {
                        "payment_item_name": "GOtv Jolli Bouquet",
                        "amount": "2460",
                        "payment_code": "459121",
                    },
                    {
                        "payment_item_name": "GOtv Max",
                        "amount": "3600",
                        "payment_code": "459119",
                    },
                    {
                        "payment_item_name": "SUPA",
                        "amount": "5500",
                        "payment_code": "459133",
                    },
                    {
                        "payment_item_name": "GOtv Smallie - yearly",
                        "amount": "6200",
                        "payment_code": "459124",
                    },
                ]
            },
        }


def _get_bank_data():
    banks = Bank.objects.all()
    bank_list = []
    for bank in banks:
        _bank = {
            'name' : bank.name,
            'code' : bank.code,
            'nip_code' : bank.nip_code,
            'alias' : bank.alias,
            'address' : bank.address,
            'email' : bank.email,
            'phone_number' : bank.phone_number,
            'website' : bank.website,
            'belong_to_nambuit_switch' : bank.belong_to_nambuit_switch,
            'belong_to_nibss_switch' : bank.belong_to_nibss_switch,
            'status' : bank.status,
            'is_partner_bank' : bank.is_partner_bank
        }
        bank_list.append(_bank)
    return bank_list

def _populate_bank_data(data):
    # Data expected is a list
    if data:
        for _data in data:
            try:
                Bank.objects.create(**_data)
            except Exception as e:
                return f'Error Creating user with data {_data}, ERROR: {e}'
        return 'Data Populated Successfully'
    else:
        return 'Empty Data Provided'

def _get_biller_item_data():
    billers = Biller.objects.all()
    biller_list = []
    for biller in billers:
        _biller = {
            'biller_category' : biller.biller_category.id,
            'biller_name' : biller.biller_name,
            'biller_code' : biller.biller_code,
            'vendor_code' : biller.vendor_code,
            'currency_code' : biller.currency_code,
            'customer_filed_1' : biller.customer_filed_1,
            'customer_filed_2' : biller.customer_filed_2
        }
        biller_list.append(_biller)
    return biller_list

def _populate_biller_item(data:list, biller_category):
    if data:
        for _data in data:
            try:
                Biller.objects.get_or_create(biller_category=biller_category, biller_name=_data.get('billername'), biller_code=_data.get('billerid'), currency_code='1', customer_field_1=_data.get('customerfield1'), customer_field_2=_data.get('customerfield2'))
            except Exception as e:
                return f'Error Creating biller item with data {_data}, ERROR: {e}'
        return 'Data Populated Successfully'
    else:
        return 'Empty Data Provided'


def is_external_ref_unique(model_obj, reference):
    model_obj.objects.filter(external_reference=reference)
    if model_obj.DoesNotExist:
        return True
    return False
    
def generate_paystack_external_ref(bank_alias, payment_type, model_obj):
    """ 
    Generates reference using the datetime excluding the other characters, combined with
    the bank alias and payment type.
    
    Also the function is recursive, calling back itself if the generated reference is
    not unique.
    """
    datetime_num = str(datetime.now())
    bad_char = ('-', ':', ' ', '.')
    random_num = ''.join(filter(lambda x: x not in bad_char, datetime_num))
    random_num = random_num[- 10:] # positive slicing to get the last 10 digits
    
    ascii_str = "".join(random.choice(string.ascii_letters) for _ in range(15))
    ref_list = list(ascii_str + random_num) # This is a list because the shuffle requires a list
    random.shuffle(ref_list) # returns nothing, but the list `str_ref` has been shuffled at this point
    ref = "".join(ref_list) 
    
    alias = [*bank_alias][:3]
    alias = "".join(alias)
    
    full_ref = f"{alias.upper()}_{payment_type.lower()}-{random_num[- 4:]}-{ref}"
    is_unique = is_external_ref_unique(
        model_obj=model_obj, reference=full_ref
    )
    if is_unique is True:
        return full_ref
    return generate_paystack_external_ref(bank_alias, payment_type, model_obj)