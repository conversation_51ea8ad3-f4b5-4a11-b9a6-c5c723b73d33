import xlsxwriter
from dfa.models import Merchant
from common.services import nip_name_enquiry
from datetime import datetime


def generate_merchant_spreadsheet(start_count=0, end_count=100):
    workbook = xlsxwriter.Workbook("MerchantImportTemplate.xlsx")

    worksheet = workbook.add_worksheet("MerchantImportTemplate")

    columns = [
        "Merchant Name",
        "Merchant TIN",
        "Merchant Contact Name",
        "Merchant Phone Number",
        "Merchant Email",
        "Merchant Collection Bank Number",
        "Merchant Collection Account Name",
        "Merchant Collection Account Number",
        "Fee Bearer",
    ]

    row = 0

    for col, elem in enumerate(columns):
        worksheet.write(row, col, elem)

    merchants = Merchant.objects.filter(
        date_created__gte=datetime(2021, 10, 22), merchant_number=None
    )

    for merchant in merchants:
        row += 1
        for col, elem in enumerate(columns):
            try:
                if elem in ["Merchant Name", "Merchant TIN"]:
                    continue
                elif elem == "Fee Bearer":
                    worksheet.write(row, col, 0)
                elif elem == "Merchant Contact Name":
                    worksheet.write(
                        row, col, f"{merchant.last_name} {merchant.first_name}"
                    )
                elif elem == "Merchant Phone Number":
                    worksheet.write(row, col, merchant.phone_number)
                elif elem == "Merchant Email":
                    worksheet.write(row, col, merchant.email)
                elif elem == "Merchant Collection Bank Number":
                    worksheet.write(row, col, merchant.bank.nip_code)
                elif elem == "Merchant Collection Account Number":
                    worksheet.write(row, col, merchant.account_number)
                elif elem == "Merchant Collection Account Name":
                    if merchant.account_name:
                        worksheet.write(row, col, merchant.account_name)
                    # else:
                    #     # Try to retrieve the merchant account name and persist it into the merchant db
                    #     resp = nip_name_enquiry(merchant.account_number, merchant.bank)
                    #     if resp.get("response_code") == "00":
                    #         account_name = resp.get("response_description", {}).get(
                    #             "account_name"
                    #         )
                    #         bvn = resp.get("response_description", {}).get("bvn")
                    #         merchant.account_name = account_name
                    #         if bvn != merchant.bvn:
                    #             merchant.is_approved = False
                    #         else:
                    #             worksheet.write(row, col, merchant.account_name)
                    #         merchant.save()
            except Exception as e:
                print(
                    f"error generating merchant with merchant id {merchant.id}, error -> {e}"
                )
                continue

    workbook.close()


def generate_report():
    """
    return generic merchant report for all merchant
    """

    workbook = xlsxwriter.Workbook("MerchantReport.xlsx")

    worksheet = workbook.add_worksheet("MerchantReport")

    columns = [
        "Name",
        "Email",
        "Account Number",
        "Account Name",
        "Bank",
        "Merchant Number",
        "Onboarding Agent Name",
        "Organization Name",
        "Date",
        "Day",
        "Status",
        "Reason",
    ]

    row = 0

    for col, elem in enumerate(columns):
        worksheet.write(row, col, elem)

    merchants = (
        Merchant.objects.all()
        .order_by("agent", "agent__created_by")
        .filter(date_created__gte=datetime(2021, 10, 18))
    )

    for merchant in merchants:
        row += 1
        for col, elem in enumerate(columns):
            try:
                if elem == "Name":
                    worksheet.write(
                        row, col, f"{merchant.last_name} {merchant.first_name}"
                    )
                elif elem == "Email":
                    worksheet.write(row, col, merchant.email)
                elif elem == "Account Number":
                    worksheet.write(row, col, merchant.account_number)
                elif elem == "Account Name":
                    worksheet.write(row, col, merchant.account_name)
                elif elem == "Bank":
                    worksheet.write(row, col, merchant.bank.name)
                elif elem == "Merchant Number":
                    worksheet.write(row, col, merchant.merchant_number)
                elif elem == "Onboarding Agent Name":
                    worksheet.write(
                        row,
                        col,
                        f"{merchant.agent.user.last_name} {merchant.agent.user.first_name}",
                    )
                elif elem == "Organization Name":
                    worksheet.write(
                        row,
                        col,
                        (merchant.agent.created_by.organization_name)
                        if merchant.agent.created_by
                        else "",
                    )
                elif elem == "Date":
                    worksheet.write(
                        row, col, merchant.date_created.strftime("%Y-%m-%d")
                    )
                elif elem == "Day":
                    worksheet.write(row, col, merchant.date_created.strftime("%A"))
                elif elem == "Status":
                    if merchant.merchant_qr_data:
                        status = "SUCCESS"
                    elif not merchant.merchant_number:
                        status = "FAILED"
                    else:
                        status = "PENDING"
                    worksheet.write(row, col, status)
                elif elem == "Reason":
                    if merchant.merchant_qr_data:
                        reason = "Merchant Qr Successfully Generated"
                    elif not merchant.merchant_number:
                        if not merchant.account_number:
                            reason = "Unable to Retrieve Account Name, Possible Invalid Merchant Account Number"
                        else:
                            reason = (
                                "Possible BVN mismatch or Unable to Create NQR Merchant"
                            )
                    else:
                        reason = "Unable to create Merchant QR data"
                    worksheet.write(row, col, reason)
            except Exception as e:
                print(
                    f"error generating merchant with merchant id {merchant.id}, error -> {e}"
                )
                continue

    workbook.close()
