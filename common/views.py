from decimal import Decimal
from customer.models import CustomerAccount, CustomerProfile
from bank.models import Bank
from dfa.models import AgentAccount
from user_profile.permissions import IsBankAdmin
from billing.models import BankBillDuePayments
from transactions.models import AirtimeData, BillPayment, Transaction
from ledger_manager.models import Account, AccountHistory
import random
import json
import requests
from datetime import datetime, timedelta
from threading import Thread
import logging

from django.utils import timezone
from django.utils.timezone import utc
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist
from django.db.models import Q


from drf_yasg.utils import swagger_auto_schema
from rest_framework.reverse import reverse
from oauth2_provider.contrib.rest_framework import permissions
from rest_framework.response import Response
from rest_framework import generics, viewsets, status
from rest_framework.views import APIView
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import AllowAny

from .models import (
    BVN,
    MobileApplicationVersion,
    State,
    LocalGovernment,
    SMSToken,
    TelecommunicationNetwork,
    AccessChannel,
    PersistPaystackPayments, 
    SettlementAccount,
    ManualReversals,
)
from .serializers import (
    AccountEnquirySerializer,
    BalanceEnquirySerializer,
    MakePaystackPaymentSerializer,
    MobileAppSerializer,
    StateSerializer,
    LocalGovernmentSerializer,
    TelecommunicationNetworkSerializer,
    AccessChannelSerializer,
    ValidateBVNSerializer,
    ValidateUtilitySerializer,
    VerifyPayStackSerializer,
    PersistPaystackPaymentSerializer,
    ManualReversalsSerializer,
    GetPendingTransactionSerializer
)
from dfa.models import AgentAccount
from billing.serializers import GetPaymentsReferenceSerializer
from .functions import (
    account_has_platform_access,
    create_bvn_record,
    get_platform,
    get_client_credentials,
    get_transact_query_type,
    set_bvn_dob,
    set_response_data,
    generate_paystack_external_ref,
    fund_settlement_account,
    CustomPageNumberPagination,
    update_daily_cummulative
)
from .services import bill_payment_advice_get_tsq, send_money
from ledger_manager.functions import credit_account, currency_formatter
from .permissions import IsThirdParty, MobileAccessPermission
from common import services
# from logic_omnichannel import settings
from django.conf import settings
from user_profile.permissions import IsBankAdmin


# Create your views here.
class StateViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.TokenHasReadWriteScope]
    """
    API endpoint that allows groups to be viewed or edited.
    """
    queryset = State.objects.filter(status='A')
    serializer_class = StateSerializer


class NameEnquiry(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = AccountEnquirySerializer

    def post(self, request):

        log = logging.getLogger('django')
        log.info(f'Entering NAME ENQUIRY API with request payload {request.data}')

        try:
            '''
                Validate the payload request and call the name_enquiry service
            '''
            serializer = AccountEnquirySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            user = request.auth.user

            app_code = user.app_code
            if app_code == None or app_code == "0000":
                if hasattr(user, 'agent'):
                    app_code = user.agent.bank.alias
                elif hasattr(user, "bankadmin"):
                    app_code = user.bankadmin.bank.alias
                    # change was made here.
                    # Another change for push purpose.

            account_number = serializer.validated_data['account_number']
            destination_institution_code = serializer.validated_data['institution_code']
            sender_intitution_code = serializer.validated_data['sender_institution_code']
            sender_account = serializer.validated_data['sender_account_number']
            if not sender_account or not sender_intitution_code:
                raise ValidationError({
                'sender_account_number' : 'sender account number and sender institution code must exist'
            })

            sender_bank = Bank.objects.filter(Q(nip_code=sender_intitution_code) | Q(code=sender_intitution_code))
            destination_bank =Bank.objects.filter(Q(nip_code=destination_institution_code) | Q(code=destination_institution_code))
            if sender_bank:
                sender_bank = sender_bank[0]
            else:
                raise ValidationError({
                'sender_institution_code' : 'Sender Institution Code Invalid'
            })
            if destination_bank:
                destination_bank = destination_bank[0]
            else:
                raise ValidationError({
                'institution_code' : 'Account Institution Code Invalid'
            })
            name_enq_type = get_transact_query_type(sender_bank, destination_bank)
            
            if settings.APP_ENV == "developments":
                name_enq = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": {
                        "email": "<EMAIL>",
                        "bvn": "***********",
                        "phone_number": "***********",
                        "account_name": "Adejare Tijani",
                        "name_enquiry_ref": "0000",
                    },
                }

            else:
                name_enq = services.get_name_enquiry(
                    sender_bank,
                    account_number,
                    destination_bank.nip_code,
                    sender_account,
                    sender_intitution_code,
                    type=name_enq_type,
                    app_code=app_code
                )

            if name_enq['response_code'] == settings.SUCCESS_RESPONSE:
                data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description' : "Name Enquiry Successful",
                    'response_detail' : {
                        'account_name' : name_enq['response_description']['account_name'],
                        'account_number': account_number,
                        'name_enquiry_ref': name_enq['response_description']['name_enquiry_ref'],
                        "bvn": name_enq.get("response_description", {}).get(
                            "bvn", None
                        ),
                    }
                }
                log.info(f'Exiting NAME ENQUIRY API with {data}')
                return Response(data, status=status.HTTP_200_OK)
            elif name_enq['response_code'] == settings.FAILED_RESPONSE:
                data = {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : "Name Enquiry Failed",
                    'response_detail' : name_enq['response_description']
                }
                log.info(f'Exiting NAME ENQUIRY API with {data}')
                return Response(data, status=status.HTTP_200_OK)

            raise Exception('Name Enquiry Failed')
        except ValidationError:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : 'Invalid Request Payload',
                'response_detail' : serializer.errors
            }
            log.info(f'Exiting NAME ENQUIRY API with {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : 'Switch Error',
                'response_detail' : "Core banking error, will be resolved soon."
            }
            log.info(f'Exiting NAME ENQUIRY API with <{str(e)}>')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)



# class NameEnquiry(APIView):
#     permission_classes = [permissions.TokenHasReadWriteScope]

#     def post(self, request):
#         account_number = request.data.get('account_number')
#         institution_code = request.data.get('institution_code')
#         name_enq = services.account_history(**request)
#         return Response(name_enq, status=status.HTTP_200_OK)


class BillerCategory(APIView):
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request):
        name_enq = services.get_biller_category()
        return Response(name_enq, status=status.HTTP_200_OK)


class BalanceEnquiry(generics.GenericAPIView):
    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = BalanceEnquirySerializer

    def post(self, request):
        try:
            '''
                Call the balance enquiry service for accounts that are valid on the request channels
            '''
            serializer = BalanceEnquirySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            account_number = serializer.validated_data['account_number']
            institution_code = serializer.validated_data['institution_code']

            channel = get_platform(token=request.auth.token)

            sender_account = CustomerAccount.objects.filter(account_number=account_number).filter(
                Q(bank__code=institution_code) | Q(bank__nip_code=institution_code)
            ).first()
            if not sender_account:
                data = {
                    'response_code': settings.FAILED_RESPONSE,
                    'response_description': 'Customer Account Is Not Valid'
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            bank = sender_account.bank

            if not account_has_platform_access(channel, sender_account) or sender_account.status == 'I':
                # block account from making transaction on blocked channels
                data = {
                    'response_code': settings.FAILED_RESPONSE,
                    'response_description': 'Account Is Blocked On This Channel, Contact Admin'
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            bal_enq = services.balance_enquiry(bank, account_number, bank.nip_code)
            return Response(bal_enq, status=status.HTTP_200_OK)
        except Exception as e:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_detail': str(e)
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

class GetState(APIView):
    # Get an instance of a logger
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request):
        try:
            record = State.objects.filter(status="A").order_by("name")
            if not record:
                data = {
                    "response_code": "01",
                    "response_description": "No record found",
                }
            else:
                data_serializer = StateSerializer(record, many=True).data
                data = {"response_code": "00", "record": data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {"response_code": "01", "response_description": "No record found "}
            return Response(data, status=status.HTTP_200_OK)


class GetLocalGovtByState(APIView):
    # Get an instance of a logger
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, state_id):
        try:
            record = LocalGovernment.objects.filter(state_id=state_id)
            if not record:
                data = {'response_code': '01', 'response_description': 'No record found'}
            else:
                data_serializer = LocalGovernmentSerializer(record, many=True).data
                data = {'response_code': '00', 'record': data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class GetChannels(APIView):
    # Get an instance of a logger
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request):
        try:
            record = AccessChannel.objects.filter(status=True)
            if not record:
                data = {'response_code': '01', 'response_description': 'No record found'}
            else:
                data_serializer = AccessChannelSerializer(record, many=True).data
                data = {'response_code': '00', 'record': data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class GenerateToken(APIView):
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, identifier):
        # save or update and send sms
        token = f'{random.randrange(1, 10 ** 3):06}'
        data = generate_and_token(token, identifier, identifier)

        return Response(data, status=status.HTTP_200_OK)


class ValidateToken(APIView):
    permission_classes = [permissions.TokenHasReadWriteScope]

    def post(self, request):
        token_identifier = request.data.get('token_identifier')
        claim_token = request.data.get('token')
        try:
            token = SMSToken.objects.get(token_identifier=token_identifier)

            real_token = token.token

            if check_token_expiry(token_identifier, claim_token):
                if claim_token == real_token:
                    data = {
                        'response_code': settings.SUCCESS_RESPONSE,
                        'response_description': 'Token is valid'}
                    return Response(data, status=status.HTTP_200_OK)
                else:
                    data = {
                        'response_code': '01',
                        'response_description': 'Token is invalid'}
                    return Response(data, status=status.HTTP_200_OK)
            else:
                data = {
                    'response_code': '01',
                    'response_description': 'Token has expired'}
                return Response(data, status=status.HTTP_200_OK)

        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No token created for this identifier'}
            return Response(data, status=status.HTTP_200_OK)


class ValidateBVN(generics.GenericAPIView):

    permission_classes = (AllowAny,)
    serializer_class = ValidateBVNSerializer

    def post(self, request):

        log = logging.getLogger("omni_bvn")
        log.info(f"entering VALIDATE BVN API with: {request.data}")

        try:
            """
            Validate request point of entry
            """
            client_id, client_secret = get_client_credentials(request)
            get_platform(client_id, client_secret)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Credentials",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            log.info(f"exiting BVN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Application",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            log.info(f"exiting BVN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            """
            Try to validate the request payload, check if we have local storage of bvn details
            else call the validate bvn gateway
            """
            serializer = ValidateBVNSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            bvn = serializer.validated_data["bvn"]
            # app_code = serializer.validated_data.get("app_code", "chmfb")

            try:
                bvn_enq = BVN.objects.get(bvn=bvn)
                bvn_exists_locally = True
                bvn = bvn_enq.bvn
                phone_number = bvn_enq.phone_number
                first_name = bvn_enq.first_name
                last_name = bvn_enq.last_name
                dob = bvn_enq.dob
                formatted_dob = bvn_enq.formatted_dob
                log.info(
                    f"FROM BVN API: BVN exist locally. getting BVN details from local storage"
                )
            except BVN.DoesNotExist:
                log.info(
                    f"FROM BVN API: BVN does not exist locally. Trying to call bvn verification service"
                )
                bvn_exists_locally = False
                # partner_bank = Bank.objects.get(alias=app_code)
                bvn_enq = services.validate_bvn(bvn)
                log.info(
                    f"FROM BVN API: Response from bvn verification service {bvn_enq}"
                )
                if bvn_enq["response_code"] == settings.SUCCESS_RESPONSE:
                    bvn = bvn_enq["response_description"]["bvn"]
                    phone_number = bvn_enq["response_description"]["mobile"]
                    first_name = bvn_enq["response_description"]["first_name"]
                    last_name = bvn_enq["response_description"]["last_name"]
                    dob = bvn_enq["response_description"]["dob"]
                    formatted_dob = bvn_enq["response_description"]["formatted_dob"]
                    gender = bvn_enq.get("response_description", {}).get("gender", "")
                    residential_address = bvn_enq.get("response_description", {}).get(
                        "residential_address", ""
                    )
                    log.info(
                        f"FROM BVN API: SUCCESS RESPONSE from bvn verification service"
                    )
                else:
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="Error Resolving BVN",
                        detail=bvn_enq.get("response_description"),
                    )
                    log.info(
                        f"FROM BVN API: FAILED RESPONSE from bvn verification service"
                    )
                    log.info(f"exiting BVN API with: {data}")
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "BVN is Valid",
                "response_detail": {
                    "bvn": bvn,
                    "phone_number": phone_number,
                    "first_name": first_name,
                    "last_name": last_name,
                    "dob": formatted_dob,
                },
            }
            if not bvn_exists_locally:
                log.info(
                    f"FROM BVN API: SPINNING UP THREAD to save bvn details locally"
                )
                Thread(
                    target=create_bvn_record,
                    args=(
                        bvn,
                        phone_number,
                        first_name,
                        last_name,
                        dob,
                        formatted_dob,
                        gender,
                        residential_address,
                    ),
                ).start()
            # store dob in internal local memory for customer profile creation in user creation to pick from
            set_bvn_dob(bvn, formatted_dob)
            log.info(f"exiting BVN API with: {data}")
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Request Payload",
                "response_detail": serializer.errors,
            }
            log.info(f"exiting BVN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Switch Error",
                "response_detail": str(e),
            }
            log.info(f"exiting BVN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class NetworkByCountryCode(APIView):
    # Get an instance of a logger
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, country_code):
        try:
            record = TelecommunicationNetwork.objects.filter(country=country_code)
            if not record:
                data = {'response_code': '01', 'response_description': 'No record found'}
            else:
                data_serializer = TelecommunicationNetworkSerializer(record, many=True).data
                data = {'response_code': '00', 'record': data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class ValidateAccountNumber(APIView):
    # Get an instance of a logger
    permission_classes = [permissions.TokenHasReadWriteScope]

    def post(self, request):
        try:
            account_number = request.data.get('account_number')
            institution_code = request.data.get('institution_code')
            account_enq = services.account_enquiry(account_number, institution_code)

            if account_enq:
                customer_phone_number = str(account_enq['phone_number'])
                print(customer_phone_number)
                token = f'{random.randrange(1, 10 ** 3):06}'
                data = generate_and_token(token, customer_phone_number, account_number)
            else:
                data = {'response_code': '01', 'response_description': 'unable to generate token'}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class ValidateUtility(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope | IsThirdParty]
    serializer_class = ValidateUtilitySerializer

    def post(self, request):

        try:
            '''
                Validate request payload and send to the respective gateway
            '''
            serializer = ValidateUtilitySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            customer_unique_number = serializer.validated_data['customer_unique_number']
            payment_code = serializer.validated_data['payment_code']

            resp = services.validate_customer_unique_number(customer_unique_number, payment_code)
            if resp.get('response_code') == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description=resp.get('response_description') or 'Validate Utility Successful',
                    detail={
                        'customer_unique_number': resp.get('customer_unique_number'),
                        'payment_code': resp.get('payment_code'),
                        'customer_name': resp.get('customer_name')
                    }
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get('response_description') or 'Unable to Validate Utility',
                    detail=resp.get('response_detail') or resp.get('response_description') or 'Unable to Validate Utility'
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)                
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Invalid Payload Request',
                detail=serializer.errors
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Error Processing Request',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        
class PersistPaystackLastFourDigits(generics.GenericAPIView):
    
    permission_classes = [permissions.TokenHasReadWriteScope, IsBankAdmin]
    
    def post(self, request):
        
        try:
            external_reference = request.data.get("external_reference")
            last_four_digits = request.data.get("last_four_digits")
            
            try:
                payment = PersistPaystackPayments.objects.get(
                    external_reference=external_reference
                )
            except ObjectDoesNotExist:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="The External Reference entered does not exist",
                    detail="Paystack External Reference does not exist"
                )  
                
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            if len(last_four_digits) > 4 or len(last_four_digits) < 4:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Failed to persist Last Four Digits.",
                    detail="Failed to persist data. Last Four Digits must be exactly four digits."
                )
                
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            elif payment.last_four_digits: # If there is already a value for the last four digits field
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="This Reference already has a value for last four digits",
                    detail="External reference already has a value for the last four digits"
                )
                
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            else:
                payment.last_four_digits = last_four_digits
                payment.save()
                
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Last Four Digits saved successfully",
                    detail=f"Last Four Digits has been save with to this reference `{external_reference}`"
                )
                
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Failed to persist last four digist",
                detail=f"Failed to persist data: {e}"
            )
                
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
                 

class PersistPaystackPaymentsView(generics.GenericAPIView):
    
    permission_classes = [permissions.TokenHasReadWriteScope, IsBankAdmin]
    serializer_class = PersistPaystackPaymentSerializer
    
    def post(self, request):
        
        try:
            user = self.request.auth.user
            bank = user.bankadmin.bank
            
            serializer = PersistPaystackPaymentSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            payment_type = serializer.validated_data["payment_type"]
            bill_payment_ref = serializer.validated_data["bill_payment_ref"]
            
            external_reference = generate_paystack_external_ref(
                    bank_alias=bank.alias,
                    payment_type=payment_type,
                    model_obj=PersistPaystackPayments,
                )
            
            if payment_type == "SUB":
                
                pay_references_str = []
                pay_references_objs = []

                try:
                    for bill in bill_payment_ref:
                        pay_ref = (
                            BankBillDuePayments.objects.filter(
                            billing_definition__partner_bank=bank
                            ).get(billing_reference=bill)
                        )
                        # If it returns DoesNotExist at this point, it means the reference does
                        # not belong to the bank of this bankadmin
                        if pay_ref.payment_status == "Paid":
                            data = set_response_data(
                                code=settings.FAILED_RESPONSE,
                                description="This Reference is already Paid for",
                                detail=f"Cannot Update this Reference `{bill}`, Paid Status because it is already Paid."
                            )
                            return Response(data, status=status.HTTP_400_BAD_REQUEST)
                        pay_references_str.append(bill)
                        pay_references_objs.append(pay_ref)
                except ObjectDoesNotExist:
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="Reference does not belong to your Bank",
                        detail=f"You do not have permission, because `{bill}`, reference does not exist for your bank."
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                
                amount = [pay_amt.amount for pay_amt in pay_references_objs]
                amount = sum(amount)
                
                serializer.save(
                    bank=bank,
                    bill_payment_ref=pay_references_str,
                    payment_type=payment_type,
                    amount=amount,
                    external_reference=external_reference
                )
                        
                # queryset = BankBillDuePayments.objects.filter(
                #     billing_reference__in=pay_references_str)
                # bill_serializer = GetPaymentsReferenceSerializer(
                #     queryset, many=True)
                
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Reference generated successfully and saved",
                    detail={
                        "external_reference": serializer.data["external_reference"],
                        "amount": serializer.data["amount"],
                        "email": bank.email,
                        "payment_type": payment_type,
                        "payment_verified": serializer.data["verified"],
                    }
                )
                return Response(data, status=status.HTTP_200_OK)
            
            elif payment_type == "WF":
            
                serializer.save(
                    bank=bank,
                    payment_type=payment_type,
                    amount=serializer.validated_data["amount"],
                    external_reference=external_reference,
                )
                
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Reference generated successfully and saved",
                    detail={
                        "external_reference": serializer.data["external_reference"],
                        "amount": serializer.data["amount"],
                        "email": bank.email,
                        "payment_type": payment_type,
                        "payment_verified": serializer.data["verified"],
                    }
                )
                return Response(data, status=status.HTTP_200_OK)
            
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Payment Type",
                    detail="Payment is either `WF` or `SUB`"
                ) 
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Invalid Request Payload',
                detail=serializer.errors
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Error Persisting Data',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
                    
            # ALSO CREATE A GET PAY REF ENDPOINT TO RETURN A PARTICULAR PAY REF WHICH FRONEND CAN USE AS LINK TO HELP THEM VIEW THE PAYMENT THEY JUST MADE, THIS CAN BE SENT 
            # THRU THE EMAIL AND THE URL WILL TAKE THE REF AS PATH PARAM.
            # WE THE PORTAL ADMIN SHOULD HAVE A GET REQUEST TO SEE ALL OF THE PAYSTACK PAYMENTS 
                  


class VerifyPayStackPayment(generics.GenericAPIView):
    
    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = VerifyPayStackSerializer
    
    def post(self, request):
        try:
            """ 
            Validate request payload and use them to verify paystack payment through the gateway
            """
            
            serializer = VerifyPayStackSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            reference = serializer.validated_data["reference"]
            email = serializer.validated_data["email"]
            amount = serializer.validated_data["amount"]
            last_four_digits = serializer.validated_data["last_four_digits"]
            
            resp = services.verify_paystack_payment(
                reference=reference, email=email,
                amount=amount, last_four_digits=last_four_digits, refund=False
            )
            if resp.get('response_code') == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description=resp.get('response_description'),
                    detail=resp.get("response_detail")
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get('response_description') or 'Unable to Verify Paystack Payment',
                    detail=resp.get('response_detail') or resp.get('response_description') or 'Unable to Verify Paystack Payment',
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)                
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Invalid Payload Request',
                detail=serializer.errors
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Error Processing Request',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)  


class ResetDailyLimit(generics.GenericAPIView):

    permission_classes = (AllowAny,)

    def get(self, request):

        log = logging.getLogger('django')
        log.info('Entering RESET DAILY LIMIT API')

        try:
            '''
                Validate request point of entry
            '''
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(client_id=client_id, client_secret=client_secret)
            log.info(f'FROM RESET DAILY LIMIT API :request from {request_platform}')
        except ValidationError:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Credentials',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
            log.info(f'exiting RESET DAILY LIMIT API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Application',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
            log.info(f'exiting RESET DAILY LIMIT API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            '''
                Try to reset all customer account who made transaction on their account to a daily cummulative of 0.00.
                Checking for accounts with daily cummulative of greater than 0
            '''
            accounts = CustomerAccount.objects.filter(daily_cummulative__gt=0)
            accounts_count = accounts.count()

            def update_accounts(count):
                accounts.update(daily_cummulative=Decimal('0.00'))

                email_args = {
                    'email_from': settings.DEFAULT_FROM_EMAIL,
                    'to': '<EMAIL>',
                    'subject': 'RESET DAILY LIMIT SUCCESSFUL',
                    'message': f'Reset of {count} customer account daily cummulative to 0.00 at {datetime.now()} SUCCESSFUL',
                }
                services.send_email(email_args, 'INTERNAL_NOTIFICATION')

            Thread(target=update_accounts, args=(accounts_count,)).start()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Reset Daily Limit Successfull',
                detail='Reset Daily Limit Successfull'
            )
            log.info(f'exiting RESET DAILY LIMIT API with: {data}')
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:

            email_args = {
                'email_from': settings.DEFAULT_FROM_EMAIL,
                'to': '<EMAIL>',
                'subject': 'RESET DAILY LIMIT FAILED',
                'message': f'Reset of customer account daily cummulative to 0.00 at {datetime.now()} FAILED, possible error {e}',
            }
            Thread(target=services.send_email, args=(email_args, 'INTERNAL_NOTIFICATION'))
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Unable to Complete Request',
                detail=f'Unable To Complete Request {e}'
            )
            log.info(f'exiting RESET DAILY LIMIT API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        

class ResetAgentDailyLimit(generics.GenericAPIView):

    permission_classes = (AllowAny,)

    def get(self, request):

        log = logging.getLogger('django')
        log.info('Entering RESET AGENT DAILY LIMIT API')

        try:
            '''
                Validate request point of entry
            '''
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(client_id=client_id, client_secret=client_secret)
            log.info(f'FROM RESET DAILY LIMIT API :request from {request_platform}')
        except ValidationError:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Credentials',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
            log.info(f'exiting RESET DAILY LIMIT API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Application',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
            log.info(f'exiting RESET AGENT DAILY LIMIT API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            '''
                Try to reset all customer account who made transaction on their account to a daily cummulative of 0.00.
                Checking for accounts with daily cummulative of greater than 0
            '''
            accounts = AgentAccount.objects.filter(daily_cummulative__gt=0)
            accounts_count = accounts.count()

            def update_accounts(count):
                accounts.update(daily_cummulative=Decimal('0.00'))

                email_args = {
                    'email_from': settings.DEFAULT_FROM_EMAIL,
                    'to': '<EMAIL>',
                    'subject': 'RESET AGENT DAILY LIMIT SUCCESSFUL',
                    'message': f'Reset of {count} customer account daily cummulative to 0.00 at {datetime.now()} SUCCESSFUL',
                }
                services.send_email(email_args, 'INTERNAL_NOTIFICATION')

            Thread(target=update_accounts, args=(accounts_count,)).start()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Reset Agent Daily Limit Successfull',
                detail='Reset Agent Daily Limit Successfull'
            )
            log.info(f'exiting RESET AGENT DAILY LIMIT API with: {data}')
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:

            email_args = {
                'email_from': settings.DEFAULT_FROM_EMAIL,
                'to': '<EMAIL>',
                'subject': 'RESET AGENT DAILY LIMIT FAILED',
                'message': f'Reset of agent account daily cummulative to 0.00 at {datetime.now()} FAILED, possible error {e}',
            }
            Thread(target=services.send_email, args=(email_args, 'INTERNAL_NOTIFICATION'))
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Unable to Complete Request',
                detail=f'Unable To Complete Request {e}'
            )
            log.info(f'exiting RESET AGENT DAILY LIMIT API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

class MobileAppVersionViewset(viewsets.ModelViewSet):

    permission_classes = [MobileAccessPermission]
    """
    API endpoint that allows groups to be viewed or edited.
    """
    queryset = MobileApplicationVersion.objects.all().order_by("-date_created")
    serializer_class = MobileAppSerializer


def check_token_expiry(token_identifier, claim_token):
    token = SMSToken.objects.get(token_identifier=token_identifier, token=claim_token)

    current_date = datetime.now().replace(tzinfo=utc)
    expiry_date = token.expiry_date.replace(tzinfo=utc)
    if expiry_date > current_date:
        return True
    else:
        return False


def generate_and_token(token, phone_number, identifier):
    date_created = timezone.now() + timedelta(minutes=60)
    expiry_date = date_created + timedelta(minutes=settings.TOKEN_EXPIRY)

    otp, created = SMSToken.objects.update_or_create(

        token_identifier=identifier,

        defaults={
            'token_identifier': identifier,
            'token': token,
            'date_created': date_created,
            'expiry_date': expiry_date
        }
    )

    if otp or created:
        send_sms_response = services.send_sms(phone_number, token)
        if send_sms_response['response_code'] == settings.SUCCESS_RESPONSE:

            data = {
                'response_code': settings.SUCCESS_RESPONSE,
                'expiry': settings.TOKEN_EXPIRY,
                'token': token,
                'response_description': 'token generated successfully and expires in ' + str(settings.TOKEN_EXPIRY)
                                        + ' minutes'
            }
        else:
            data = send_sms_response
    else:
        data = {
            'response_code': settings.FAILED_RESPONSE,
            'response_description': 'unable to generate/save/update token in database'
        }

    return data

class RunPaystackPaymentsUpdateScheduler(generics.GenericAPIView):
    
    def post(self, request):
        
        try:
            token = request.auth
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {token}"}
            url = reverse("verify_paystack_payment", request=request)
            
            
            payments = PersistPaystackPayments.objects.filter(
                verified=False, last_four_digits__isnull=False
            ) 
            # I am filtering by checking if the `last_four_digits` is not null, cause the scheduler might run
            # at a time the `last_four_digits` has not been saved, and if it calls the verify endpoint without it,
            # an error will be returned
            for payment in payments:
                reference = payment.external_reference
                amount = payment.amount
                email = payment.bank.email
                last_four_digits = payment.last_four_digits
                
                data = {
                    "reference": reference,
                    "amount": int(amount),
                    "email": email,
                    "last_four_digits": last_four_digits
                }
                payload = json.dumps(data)
                
                resp = requests.request("POST", url, headers=headers, data=payload)
                resp = resp.json()
                if resp.get("response_code") == "00":
                    payment.verified = True
                    payment.save()
                    
                    if payment.payment_type == "SUB": 
                        bill_ref = payment.bill_payment_ref.split(",")
                        for bill in bill_ref:
                            bill_obj = BankBillDuePayments.objects.get(
                                billing_reference=bill
                            )
                            bill_obj.payment_status = "Paid"
                            bill_obj.save()
                            self.send_alert_email(
                                payment.bank, "PAYMENT_UPDATED", bill
                            )
                            
                        bank_payments = Bank.objects.filter(
                            name=payment.bank.name,
                            bankbilldefinitions__bankbillduepayments__payment_status="Not Paid"
                        )
                        
                        if not bank_payments.exists():
                            if payment.bank.is_active == False:
                                payment.bank.is_active = True
                                payment.bank.save()
                                self.send_alert_email(payment.bank, "BANK_UNBLOCKED")
                    
                    elif payment.payment_type == "WF":
                        
                        wallet_account = Account.objects.get(account_number=payment.bank.utility_account)
                        credit_account(wallet_account, payment.amount)
                        wallet_account.save()
                        
                        AccountHistory.objects.create(
                                account = wallet_account,
                                posting_type = "CREDIT",
                                amount =  payment.amount,
                                balance = wallet_account.actual_balance,
                                narration = f"CardTrans/{payment.external_reference}/Credit"
                            )
                        
                        format_amount = currency_formatter(payment.amount)
                        self.send_alert_email(payment.bank, "WALLET_FUNDING", format_amount)
                        # Add a logger to log payments that came back with an unsuccessful response_code and log them as 
                        # unverified payments
            return Response("", status=status.HTTP_200_OK)
        
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Error Processing Request',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST) 
    
    def send_alert_email(obj, bank=None, message_type=None, *args):
        
        if message_type == "PAYMENT_UPDATED":
            message = f"Your payment for Billing Reference `{args[0]}` was received and successful. The payment has been updated accordingly."
            
        elif message_type == "BANK_UNBLOCKED":
            message = "Your payment was received and you have been unblocked and your priviledges restored. Continue to enjoy our platform."
            
        elif message_type == "WALLET_FUNDING":
            message = f"Your payment of `{args[0]}` was received and your wallet account has been updated accordingly."
        
        email_args = {
            "email_from": settings.DEFAULT_FROM_EMAIL,
            "to": bank.email,
            "subject": "Billing Status",
            "message": message,
            "bank_name": bank.name,
            "background": "FFFFFF",
            "foreground": "0C6ED1",
            "img_url": settings.OMNI_CHANNNEL_IMG_URL
        }
        services.send_email(email_args, "BILLING_SHUTDOWN_ALERTS")   



class ConfirmReversals(generics.GenericAPIView):
    
    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    
    @swagger_auto_schema(request_body=ManualReversalsSerializer)
    def post(self, request):
        
        try:
            serializer = ManualReversalsSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            user = request.auth.user
            bank = user.bankadmin.bank
            
            user_ref = serializer.validated_data["user_ref"]
            transaction_type = serializer.validated_data["transaction_type"]
            
            if transaction_type == "AIRTIME/DATA":
                transaction = AirtimeData.objects.get(user_ref=user_ref)            
            elif transaction_type == "BILL PAYMENT":
                transaction = BillPayment.objects.get(user_ref=user_ref)

            if transaction.sender_account.bank != bank:
                raise Exception("User Reference not found for Bank")
            
            if transaction.status == "S" or transaction.status == "R":
                raise Exception(
                    "Transaction Already Successful, cannot confirm" if transaction.status == "S"
                    else "Transaction Already Reversed, cannot confirm"
                )  
            if transaction.status == "F":
                raise Exception(
                    "Transaction has failed status, cannot reverse."
                )
            response_code, recharge_pin = bill_payment_advice_get_tsq(
                transaction.core_banking_ref, 
                "airtime_data" if transaction_type == "AIRTIME/DATA" else "bill_payment"
            )
            if response_code == settings.SUCCESS_RESPONSE:
                transaction.response_code = settings.SUCCESS_RESPONSE
                transaction.response_description ="AIRTIME/DATA SUCCESSFUL" if transaction_type == "AIRTIME/DATA" else "BILL PAYMENT SUCCESSFUL" 
                transaction.status = "S"
                transaction.is_resolved = False
                transaction.is_transaction_chargable = True
                transaction.recharge_pin = recharge_pin if recharge_pin else ""
                transaction.save()

                update_daily_cummulative(
                        transaction.amount, transaction.sender_account
                )
                # At this point we deny them permission to make a reversal.
                data = set_response_data(
                    code="05",
                    description="TRANSACTION CONFIRMED AS SUCCESSFUL",
                    detail="The transaction was successful, updated accordingly and cannot be reversed."
                )
                return Response(data, status=status.HTTP_200_OK)
            
            elif (
                    response_code != None
                    and response_code != ""
                    and response_code not in ["21", "29", "57", "61", "17"]
                ):
                # At this point we grant them permission to make a reversal.
                data = set_response_data(
                    code="00",
                    description="TRANSACTION WAS UNSUCCESSFUL",
                    detail="The Transaction has been confirmed as unsuccessful, and should be reversed."
                )
                return Response(data, status=status.HTTP_200_OK)
            
            else:
                data = set_response_data(
                    code=settings.PENDING_RESPONSE,
                    description="TRANSACTION IS UNRESOLVED.",
                    detail="The transaction has not been resolved, and left as pending."
                )
                return Response(data, status=status.HTTP_200_OK)
           
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="INVALID REQEUST PAYLOAD",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except AirtimeData.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="AIRTIME TRANSACTION DOES NOT EXIST",
                detail="Airtime/Data Transaction Does Not Exist for the User Reference",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)   
        except BillPayment.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="BILL PAYMENT TRANSACTION DOES NOT EXIST",
                detail="Bill Payment Transaction Does Not Exist for the User Reference",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)  
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='FAILED TO CONFIRM TRANSACTION',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class PerformManualReversals(generics.GenericAPIView):
    
    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    
    @swagger_auto_schema(request_body=ManualReversalsSerializer)
    def post(self, request):
        
        try:
            serializer = ManualReversalsSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            
            user = request.auth.user
            bank = user.bankadmin.bank
            bank_admin = user.bankadmin
            
            user_ref = serializer.validated_data["user_ref"]
            transaction_type = serializer.validated_data["transaction_type"]

            if transaction_type == "AIRTIME/DATA":
                transaction = AirtimeData.objects.get(user_ref=user_ref)            
            elif transaction_type == "BILL PAYMENT":
                transaction = BillPayment.objects.get(user_ref=user_ref)

            if transaction.sender_account.bank != bank:
                raise Exception("User Reference not found for Bank")
            
            if transaction.status == "S" or transaction.status == "R":
                raise Exception(
                    "Transaction Already Successful, cannot confirm" if transaction.status == "S"
                    else "Transaction Already Reversed, cannot confirm"
                )  
            if transaction.status == "F":
                raise Exception(
                    "Transaction has failed status, cannot reverse."
                )
            settlement_account = SettlementAccount.objects.get(
                bank=bank, transaction_type__name=transaction_type)
            
            # I'm supposed to use the `account_settlement_reversal` function though, but that returns None and wouldn't want that
            fund_reversal = fund_settlement_account(
                sender_account=settlement_account,
                amount=transaction.amount,
                settlement_account=transaction.sender_account,
                app_code=bank.alias,
                send_money=send_money,
            )
            
            if fund_reversal.get("response_code") == settings.SUCCESS_RESPONSE:
                transaction.status = "R"
                transaction.save()

                wallet_account = Account.objects.get(account_number=bank.utility_account)
                credit_account(wallet_account, transaction.amount)
                wallet_account.save()
                        
                AccountHistory.objects.create(
                    account = wallet_account,
                    posting_type = "CREDIT",
                    amount = transaction.amount,
                    balance = wallet_account.actual_balance,
                    narration = f"Manual Reversal"
                )
                ManualReversals.objects.create(
                    user_ref=user_ref,
                    transaction_type=transaction_type,
                    core_banking_ref=fund_reversal.get("core_banking_ref"),
                    bank_admin=bank_admin,
                    status="S"
                )
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="REVERSAL SUCCESSFUL.",
                    detail={
                        "user_ref": transaction.user_ref,
                        "transaction_type": transaction.transaction_type.name,
                        "core_banking_ref": fund_reversal.get("core_banking_ref"),
                        "reversal_details": {
                            "amount": transaction.amount,
                            "performed_by": bank_admin.user.get_full_name(),
                            "reversal_status": "Successful",
                        }
                    }
                )
                return Response(data, status=status.HTTP_200_OK)

            else:
                ManualReversals.objects.create(
                    user_ref=user_ref,
                    transaction_type=transaction_type,
                    core_banking_ref=fund_reversal.get("core_banking_ref"),
                    bank_admin=bank_admin,
                    status="F"
                )
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="REVERSAL FAILED.",
                    detail={
                        "user_ref": transaction.user_ref,
                        "transaction_type": transaction.transaction_type.name,
                        "core_banking_ref": fund_reversal.get("core_banking_ref"),
                        "reversal_details": {
                            "amount": transaction.amount,
                            "performed_by": bank_admin.user.get_full_name(),
                            "reversal_status": "Failed",
                        }
                    }
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="INVALID REQEUST PAYLOAD",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except AirtimeData.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="AIRTIME TRANSACTION DOES NOT EXIST",
                detail="Airtime/Data Transaction Does Not Exist for the User Reference",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)   
        except BillPayment.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="BILL PAYMENT TRANSACTION DOES NOT EXIST",
                detail="Bill Payment Transaction Does Not Exist for the User Reference",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)  
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='FAILED TO CONFIRM TRANSACTION',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        

class GetPendingTransactions(viewsets.GenericViewSet):
    
    pagination_class = CustomPageNumberPagination
    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    def get_queryset(self, request):

        user = request.auth.user
        bank = user.bankadmin.bank

        transaction = Transaction.objects.filter(
            Q(bill_payment__status="P") | Q(airtime_data__status="P")
        )
        transactions = transaction.filter(
            Q(bill_payment__sender_account__bank=bank) | 
            Q(airtime_data__sender_account__bank=bank) |
            Q(airtime_data__agent_account__bank=bank)
        ).order_by("-date_created")
        return transactions 

    def list(self, request):
        try:

            queryset = self.get_queryset(request=request) 
            queryset = self.paginate_queryset(queryset)
            serializer = GetPendingTransactionSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)
            
            if queryset:
                    data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description='GET ALL TRANSACATIONS SUCCESSFUL.',
                        detail=paginated_resp.data
                )
            else:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='NO TRANSACTION FOUND.',
                    detail="No transaction found."
                )
            return Response(data, status=status.HTTP_200_OK)
        
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='GET TRANSACTION REQUEST FAILED',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        

class OneTimeResetUSSDAccessForAll(generics.GenericAPIView):

    permission_classes = (AllowAny,)

    def get(self, request):

        log = logging.getLogger('django')
        log.info('Entering ONE TIME RESET USSD ACCESS FOR ALL USERS')

        try:
            '''
                Validate request point of entry
            '''
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(client_id=client_id, client_secret=client_secret)
            log.info(f'FROM ONE TIME RESET USSD ACCESS FOR ALL USERS :request from {request_platform}')
        except ValidationError:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Credentials',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
            log.info(f'exiting ONE TIME RESET USSD ACCESS FOR ALL USERS: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Application',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
            log.info(f'exiting ONE TIME RESET USSD ACCESS FOR ALL USERS: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            '''
                Try to reset all customer profile originally created with USSD set to True by default.
            '''
            profiles = CustomerProfile.objects.all()
            for profile in profiles:
                profile.ussd_channel_access = False
                profile.save()

            total = profiles.count()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Reset USSD Access Successful.',
                detail=f'Successfully Reset `{total}` Profiles'
            )
            log.info(f'exiting ONE TIME RESET USSD ACCESS FOR ALL USERS: {data}')
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Unable to Complete Request',
                detail=f'Unable To Complete Request {e}'
            )
            log.info(f'exiting ONE TIME RESET USSD ACCESS FOR ALL USERS: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        

class LoadUsersUSSDUsersToCache(generics.GenericAPIView):

    permission_classes = (AllowAny,)

    def get(self, request):

        log = logging.getLogger('django')
        log.info('Entering LOAD USSD USERS TO CACHE.')

        try:
            '''
                Validate request point of entry
            '''
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(client_id=client_id, client_secret=client_secret)
            log.info(f'FROM LOAD USSD USERS TO CACHE :request from {request_platform}')
        except ValidationError:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Credentials',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
            log.info(f'exiting LOAD USSD USERS TO CACHE: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Application',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
            log.info(f'exiting LOAD USSD USERS TO CACHES: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            '''
                Try to Load all Customers with USSD Access to Cache.
            '''
            initial_count = cache.get("ussd_profiles")
            if initial_count is None:
                initial_count = "Not Retrieved"
            else:
                initial_count = len(initial_count)
            ussd_profiles = []
            profiles = CustomerProfile.objects.filter(ussd_channel_access=True)
            for profile in profiles:
                accounts = CustomerAccount.objects.filter(profile=profile)
                if accounts.count() > 0:
                    ussd_profile = {
                        profile.phone_number: {
                            "username": profile.customer.username,
                            "app_code": profile.customer.app_code,
                            "pin": profile.pin,
                            "name": profile.customer.get_full_name(),
                            "bank_code": accounts[0].bank.code,
                            "accounts": [account.account_number for account in accounts],
                        }
                    }
                    ussd_profiles.append(ussd_profile)
                else:
                    continue
            print(f"CACHE USSD USERS ::  {ussd_profiles}")
            cache.set("ussd_profiles", ussd_profiles, timeout=None)

            final_count = cache.get("ussd_profiles")
            final_count = len(final_count)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Load USSD Users to Cache Successful.',
                detail={
                    "intial_count": initial_count,
                    "final_count": final_count
                }
            )
            log.info(f'exiting LOAD USSD USERS TO CACHES: {data}')
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Unable to Complete Request',
                detail=f'Unable To Complete Request {e}'
            )
            log.info(f'exiting LOAD USSD USERS TO CACHES: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
