import logging
import requests
import json
import random

from django.conf import settings

from common.models import BVN
from dfa.models import AgentOnboardedAccount

# tins = ["30099", "30011", "20011"]
# nins = ["***********", "***********", "***********"]
# sectors = ["Education", "Technology", "Marketing"]
# marital_status = ["single", "married"]
log = logging.getLogger('omni_send_money')

class NIBSSIntegration:
    
    BASE_URL = settings.NIBSS_BASE_URL
    QA_BASE_URL = settings.NIBSS_QA_BASE_URL
    AUTH_HEADERS = {
        "Authorization": settings.NIBSS_SECRET_KEY,
        'Content-Type': 'application/json'
    }

    @classmethod    
    def details_enquiry(cls, account_number):
        url = f"{cls.BASE_URL}/account/read-by-account-number/{account_number}"
        request = cls._handle_request("GET", url)
        log.info(f"NIBSS INTEGRATIONS: Account Enquiry Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"NIBSS INTEGRATIONS: Account Enquiry Response >> {response}")
        return response
    
    @classmethod
    def nibss_name_enq(cls, account_number, bank_nip_code):
        url = f"{cls.BASE_URL}/transaction/name-enquiry"
        payload = {
            "accountNumber": account_number,
            "bankCode": bank_nip_code,
            "enquiryProduct": "NIP"
        }
      
        request = cls._handle_request("POST", url, payload=payload)
        log.info(f"NIBSS INTEGRATIONS: Name Enquriy Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"NIBSS INTEGRATIONS: Name Enquiry Response >> {response}")
        return response

    
    @classmethod
    def account_opening(
        cls,
        bvn: BVN,
        account: AgentOnboardedAccount,
        other_details_dict: dict = None,

    ):
        url = f"{cls.BASE_URL}/secondary-customer/create-customer-account"
        payload = {
            "customerAccountClass": "TIER_3",
            "customerAccountOfficerId": account.agent.get_full_name(),
            "customerAddress": account.address,
            "customerBranchCode": "HQ001",
            "customerBvn": account.bvn,
            "customerChecker": "",
            "customerCountryCode": "234",
            "customerCurrencyCode": "NGN",
            "customerDob": account.dob,
            "customerEmail": account.email,
            "customerFirstName": account.first_name,
            "customerLastName": account.last_name,
            "customerMaker": "",
            "customerMiddleName": account.middle_name,
            "customerPhoneNumber": account.phone_number,
            "customerPhoneNumber2": "",
            "customerTin": other_details_dict.get("tin", ""), # 22323
            "customerNin": other_details_dict.get("nin", ""),
            "customerProfileFile": "(binary)",
            "customerMandateFile": "(binary)",
            "customerGender": bvn.gender.lower(),
            "customerTitle": "Mr" if account.gender == "Male" else "Mrs",
            "customerSector": other_details_dict.get("sector", ""),
            "customerMaritalStatus": other_details_dict.get("marital_status", ""), # single or married,
            "customerNextOfKinAddress": other_details_dict.get("next_kin_address", ""),
            "customerNextOfKinName": other_details_dict.get("next_kin_name", ""),
            "customerNextOfKinRelationship": other_details_dict.get("next_kin_relationship", ""),
            "customerNextOfKinPhoneNumber": other_details_dict.get("next_kin_phone", ""),
            "customerNextOfKinEmail": other_details_dict.get("next_kin_email", ""),
            "customerGlLevel2": "100101"
        }
        request = cls._handle_request("POST", url, payload=payload)
        log.info(f"NIBSS INTEGRATIONS: Account Opening Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"NIBSS INTEGRATIONS: Account Send Opening Response >> {response}")
        return response

    @classmethod
    def transfer_internal(
        cls,
        paylaod_class,
    ):
        url = f"{cls.BASE_URL}/transaction/fund-transfer"
        payload = { 
            "amount": str(paylaod_class.amount),
            "branchCode": "HQ001", 
            "checker": "SYSTEM",
            "counterPartyBankCode": paylaod_class.sender_institution_code,
            "counterPartyBankName": paylaod_class.bank_name, 
            "creditAccountName": paylaod_class.destination_account_name,
            "creditAccountNumber": paylaod_class.destination_account,
            "debitAccountName": paylaod_class.sender_account_name, 
            "debitAccountNumber": paylaod_class.sender_account, 
            "ftProductCode": "",
            "location": "1004", 
            "narration": paylaod_class.narration, 
            "reference": paylaod_class.transaction_ref,
            "source": "CBA", 
            "switch": paylaod_class.bank_name,
            "transactionType": "INTERNAL" 
        }

        request = cls._handle_request("POST", url, payload=payload)
        log.info(f"NIBSS INTEGRATIONS: Intra Send Money Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"NIBSS INTEGRATIONS: Intra Send Money Response >> {response}")
        return response
    
    @classmethod
    def transfer_external(
        cls,
        paylaod_class,
    ):
        url = f"{cls.BASE_URL}/transaction/fund-transfer-outward"
        payload = { 
            "amount": str(paylaod_class.amount),
            "branchCode": "HQ001",
            "counterPartyBankCode": paylaod_class.sender_institution_code,
            "counterPartyBankName": paylaod_class.bank_name,
            "creditAccountName": paylaod_class.destination_account_name,
            "creditAccountNumber": paylaod_class.destination_account,
            "creditBankCode": paylaod_class.destination_institution_code,
            "creditCurrencyCode": "NGN",
            "debitAccountName": paylaod_class.sender_account_name,
            "debitAccountNumber": paylaod_class.sender_account,
            "debitBankCode": paylaod_class.sender_institution_code,
            "debitCurrencyCode": "NGN",
            "checker": "SYSTEM",
            "fundTransferOutwardType": "NIP",
            "location": "1004",
            "maker": paylaod_class.sender_email,
            "mandateReferenceNumber": "",
            "narration": paylaod_class.narration,
            "reference": paylaod_class.transaction_ref,
            "sessionId": paylaod_class.session_id,
            "source": "CBA",
            "switch": paylaod_class.bank_name,
            "transactionId": "",
            "transactionType": "OUTWARD"
        }

        log.info(f"NIBSS INTEGRATIONS: Inter Send Money Payload >> {payload}")

        request = cls._handle_request("POST", url, payload=payload)
        log.info(f"NIBSS INTEGRATIONS: Inter Send Money Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"NIBSS INTEGRATIONS: Inter Send Money Response >> {response}")
        return response
    
    @classmethod
    def ft_reversal(cls, reference):

        url = f"{cls.BASE_URL}/transaction/ft-reversal"
        payload = {
            "reference": reference
        }

        request = cls._handle_request("POST", url, payload=payload)
        response = cls._handle_response(request)
        return response
    
    # @classmethod
    # def account_transactions(cls, account_number, page_number):
    #     url = f"{cls.BASE_URL}/journal/read-by-journal-account-number/{account_number}?pageNo={page_number}"
        
    #     request = cls._handle_request("GET", url)
    #     response = cls._handle_response(request)
    #     return response
    
    @classmethod
    def account_transactions(cls, account_number, page_number, start_date=None, end_date=None):
        url = f"{cls.BASE_URL}/journal/read-by-journal-account-number/{account_number}?pageNo={page_number}"
        
        if start_date:
            url += f"&startDate={start_date}"
        if end_date:
            url += f"&endDate={end_date}"
            
        print(f"NIBSS TRANSACTION URL >> {url}")

        request = cls._handle_request("GET", url)
        print(f"NIBBSS ACCOUNT STATEMENT REQUEST >>> {request}")
        response = cls._handle_response(request)
        print(f"NIBSS ACCOUNT TRANSACTIONS RESPONSE >> {response}")
        return response


    @classmethod
    def get_all_loans(
        cls,
        page_no: int = 1,
        page_size: int = 50,
        read_all: bool = False,
        start_date: str = None,
        end_date: str = None,
    ):
        url = f"{cls.BASE_URL}/loan/read?pageNo={page_no}&pageSize={page_size}"
        if start_date and end_date:
            url += f"&startDate={start_date}&endDate={end_date}"
        if read_all:
            url += "&readAll=true"
        request = cls._handle_request("GET", url)
        response = cls._handle_response(request)
        return response

    @classmethod
    def get_loan_product(
        cls,
        page_no: int = 1,
        page_size: int = 50,
        read_all: bool = False,
        start_date: str = None,
        end_date: str = None,
    ):
        url = f"{cls.QA_BASE_URL}/loan-product/read?pageNo={page_no}&pageSize={page_size}"
        if start_date and end_date:
            url += f"&startDate={start_date}&endDate={end_date}"
        if read_all:    
            url += "&readAll=true"
        request = cls._handle_request("GET", url)
        response = cls._handle_response(request)
        return response
    
    @classmethod
    def get_loan_schedules(
        cls,
        page_no: int = 1,
        page_size: int = 50,
        read_all: bool = False,
        start_date: str = None,
        end_date: str = None,
    ):
        url = f"{cls.BASE_URL}/loan/read-loan-schedule?pageNo={page_no}&pageSize={page_size}"
        if start_date and end_date:
            url += f"&startDate={start_date}&endDate={end_date}"
        if read_all:
            url += "&readAll=true"
        request = cls._handle_request("GET", url)
        response = cls._handle_response(request)
        return response
    
    @classmethod
    def get_loan_product_by_id(cls, product_id):
        url = f"{cls.BASE_URL}/lo-product/read-by-loan-product-id/{product_id}"
        request = cls._handle_request("GET", url)
        response = cls._handle_response(request)
        return response

    @classmethod
    def retrieve_loan_schedule(cls, loan_id):
        url = f"{cls.BASE_URL}/loan/read-loan-schedule-by-loan-id/{loan_id}"
        request = cls._handle_request("GET", url)
        response = cls._handle_response(request)
        return response

    @classmethod
    def _handle_request(
        cls,
        method: str,
        url: str,
        payload: dict = {}
    ):
        """
        Helper method to handle API requests.
        """
        try:
            payload = json.dumps(payload)
            response = requests.request(method=method, url=url, data=payload, headers=cls.AUTH_HEADERS)
            data = {
                "response_body": response.json(),
                "status_code": response.status_code,
                "status": "success"
            }
            return data

        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:
            data = {
                "response_body": response.text,
                "status_code": response.status_code,
                "status": "failed",
                "error": str(err)
            }
            return data
    
    @classmethod
    def _handle_response(cls, response):
        status_code = response.get("status_code")
        status = response.get("status")
        response_code = response.get("response_body", {}).get("responseCode", "")
        if status == "success" and status_code == 200 and response_code == "00":
            resp = response.get("response_body", {}).get("data", None)
            if resp == None:
                resp = response.get("response_body", {})
            data = {
                "status": "success",
                "message": "Successful",
                "data": resp
            }
        elif status == "success":
            message = response.get("response_body", {}).get("responseMessage", {})
            data = {
                "status": "failed",
                "message": message,
            }
        else:
            message = response.get("response_body", "")
            error = response.get("error", "")
            data = {
                "status": "error",
                "message": message,
                "error": error
            }  
        return data
    

class NIBSSResource:
    
    @staticmethod
    def _handle_failed_response(response, resp_status):
        if resp_status == "failed":
            return {
                "status": "failed",
                "message": response.get("message")
            }
        else:
            return {
                "status": "error",
                "message": response.get("message"),
                "error": response.get("error")
            }
        
    @staticmethod
    def name_enq(account_number, bank_nip_code):
        response = NIBSSIntegration.nibss_name_enq(account_number, bank_nip_code)
        resp_status = response.get("status")
        if resp_status == "success":
            resp_data = response.get("data")
            data = {
                "status": "success",
                "account_number": resp_data.get("accountNumber"),
                "account_name": resp_data.get("accountName"),
                "nip_session_id": resp_data.get("sessionId")
            }

        else:
            data = NIBSSResource._handle_failed_response(response, resp_status)

        return data

    @staticmethod
    def account_details_enquiry(account_number):
        """
        This resource can be used to fetch a host of data.
        Relevant to:
        - Name Enquiry API
        - Balance Enquiry API
        - Account Details API

        Params: account_number
        Returns a dict of:
        - account_number
        - account_name
        - account_id
        - available_balance
        """

        response = NIBSSIntegration.details_enquiry(account_number)
        resp_status = response.get("status")
        if resp_status == "success":
            if len(response.get("data")) == 0:
                data = {"status": "empty"}
                return data
            resp_data = response.get("data")[0]
            data = {
                "status": "success",
                "account_number":  resp_data.get("accountNumber"),
                "account_name": resp_data.get("accountName"),
                "account_balance": resp_data.get("accountAvailableBalance"),
                "account_customer_id": resp_data.get("accountCustomerId"),
                "account_id": resp_data.get("accountId"),
                "account_email": resp_data.get("accountChecker"),
                "book_balance": resp_data.get("accountBalance")
            }
        
        elif resp_status == "failed":
            data = {
                "status": "failed",
                "message": response.get("message")
            }

        else:
            data = {
                "status": "error",
                "message": response.get("message"),
                "error": response.get("error")
            }

        return data
    
    @staticmethod
    def customer_account_opening(
        bvn: BVN,
        account: AgentOnboardedAccount,
        other_details_dict: dict = None
    ):
        """
        This resource allows for the creation of customer accounts and returns an account number.

        Returns:
        - Account Number
        - Customer Account ID
        """
        response = NIBSSIntegration.account_opening(bvn=bvn, account=account, other_details_dict=other_details_dict)
        resp_status = response.get("status")
        if resp_status == "success":
            data = {
                "status": "success",
                "account_number": response.get("data")["accountNumber"],
                "customer_number": response.get("data")["customerId"]
            }
        elif resp_status == "failed":
            data = {
                "status": "failed",
                "message": response.get("message")
            }

        else:
            data = {
                "status": "error",
                "message": response.get("message"),
                "error": response.get("error")
            }

        return data
    
    @staticmethod
    def fund_transfer_internal(
            payload_class
    ):
        response = NIBSSIntegration.transfer_internal(payload_class)
        resp_status = response.get("status")
        if resp_status == "success":
            resp_data = response.get("data")
            data = {
                "status": "success",
                "cba_reference": response.get("data", {}).get("cbaReference")
            }

        elif resp_status == "failed":
            data = {
                "status": "failed",
                "message": response.get("message")
            }

        else:
            data = {
                "status": "error",
                "message": response.get("message"),
                "error": response.get("error")
            }

        return data
    
    @staticmethod
    def get_account_transactions(account_number, page_number, start_date=None, end_date=None):
        response = NIBSSIntegration.account_transactions(account_number, page_number, start_date, end_date)
        resp_status = response.get("status")

        if resp_status == "success":
            data = {
                "status": "success",
                "transactions": response.get("data")
            }
        else:
            data = NIBSSResource._handle_failed_response(response, resp_status)
        
        return data

    @staticmethod
    def fund_transfer_outward(payload_class):
        response = NIBSSIntegration.transfer_external(payload_class)
        resp_status = response.get("status")
        if resp_status == "success":
            data = {
                "status": "success",
                "cba_reference": response.get("data", {}).get("contractReference")
            }

        else:
            data = NIBSSResource._handle_failed_response(response, resp_status)
        return data
        
    @staticmethod
    def reverse_ft(reference):
        response = NIBSSIntegration.ft_reversal(reference)
        resp_status = response.get("status")
        if resp_status == "success":
            data = {
                "status": "success",
                "message": response.get("message")
            }

        else:
            data = NIBSSResource._handle_failed_response(response, resp_status)
            
        return data
    
    # @staticmethod
    # def get_account_transactions(account_number, page_number):
    #     response = NIBSSIntegration.account_transactions(account_number, page_number)
    #     resp_status = response.get("status")
    #     if resp_status == "success":
    #         data = {
    #             "status": "success",
    #             "message": response.get("message")
    #         }

    #     else:
    #         data = NIBSSResource._handle_failed_response(response, resp_status)
            
        # return data
    
    @staticmethod
    def get_all_loans(
        page_no: int = 1,
        page_size: int = 50,
        read_all: bool = False,
        start_date: str = None,
        end_date: str = None,
    ):
        response = NIBSSIntegration.get_all_loans(
            page_no=page_no,
            page_size=page_size,
            read_all=read_all,
            start_date=start_date,
            end_date=end_date
        )
        resp_status = response.get("status")
        if resp_status == "success":
            data = {
                "status": "success",
                "loans": response.get("data")
            }
             # FOR NOW I AM RETRIEVING ALL THE DATA, UNTIL I KNOW EXACTLY WHAT I NEED
        else:
            data = NIBSSResource._handle_failed_response(response, resp_status)
            

        return data
    
    @staticmethod
    def get_all_loan_product(
        page_no: int = 1,
        page_size: int = 50,
        read_all: bool = False,
        start_date: str = None,
        end_date: str = None,
    ):
        response = NIBSSIntegration.get_loan_product(
            page_no=page_no,
            page_size=page_size,
            read_all=read_all,
            start_date=start_date,
            end_date=end_date
        )
        resp_status = response.get("status")
        if resp_status == "success":
            data = {
                "status": "success",
                "loan_products": response.get("data")
            }
        else:
            data = NIBSSResource._handle_failed_response(response, resp_status)

        return data
    
    @staticmethod
    def get_loan_product_by_id(product_id):
        response = NIBSSIntegration.get_loan_product_by_id(product_id)
        resp_status = response.get("status")
        if resp_status == "success":
            data = {
                "status": "success",
                "loan_product": response.get("data")
            }
        else:
            data = NIBSSResource._handle_failed_response(response, resp_status)
    
        return data
    
    @staticmethod
    def get_loan_schedules(
        page_no: int = 1,
        page_size: int = 50,
        read_all: bool = False,
        start_date: str = None,
        end_date: str = None,
    ):
        response = NIBSSIntegration.get_loan_schedules(
            page_no=page_no,
            page_size=page_size,
            read_all=read_all,
            start_date=start_date,
            end_date=end_date
        )
        resp_status = response.get("status")
        if resp_status == "success":
            data = {
                "status": "success",
                "loan_schedules": response.get("data")
            }
        else:
            data = NIBSSResource._handle_failed_response(response, resp_status)

        return data
    
    @staticmethod
    def retrieve_loan_schedule(loan_id):
        response = NIBSSIntegration.retrieve_loan_schedule(loan_id)
        resp_status = response.get("status")
        if resp_status == "success":
            data = {
                "status": "success",
                "loan_schedule": response.get("data")
            }
        else:
            data = NIBSSResource._handle_failed_response(response, resp_status)

        return data
    
    