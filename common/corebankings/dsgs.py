import json
import requests
import logging

from django.conf import settings

log = logging.getLogger('django')

class DSGSIntegration:

    BASE_URL = settings.DSGS_BASE_URL
    AUTH_HEADERS = {
        "content-type": "application/json",
        "accept": "application/json",
    }

    @classmethod
    def name_enquiry(cls, account_number):
        """
        Get account name enquiry from DSGS API.

        Args:
            account_number: The account number to query

        Returns:
            The API response with account name data
        """
        url = f"{cls.BASE_URL}/MyBank/webresources/getClientDetails/AccountNameEnq"

        payload = {
            "account_number": account_number
        }

        log.info(f"DSGS INTEGRATION: Name Enquiry Payload >> {payload}")
        request = cls._handle_request("POST", url, payload)
        log.info(f"DSGS INTEGRATION: Name Enquiry Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"DSGS INTEGRATION: Name Enquiry Response >> {response}")
        return response

    @classmethod
    def balance_enquiry(cls, account_number):
        """
        Get account balance enquiry from DSGS API.

        Args:
            account_number: The account number to query

        Returns:
            The API response with account balance data
        """
        url = f"{cls.BASE_URL}/MyBank/webresources/getAccountBalance/AccountBalance"

        payload = {
            "account_number": account_number
        }

        log.info(f"DSGS INTEGRATION: Balance Enquiry Payload >> {payload}")
        request = cls._handle_request("POST", url, payload)
        log.info(f"DSGS INTEGRATION: Balance Enquiry Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"DSGS INTEGRATION: Balance Enquiry Response >> {response}")
        return response


    @classmethod
    def _handle_request(
        cls,
        method: str,
        url: str,
        payload: dict = {}
    ):
        """
        Helper method to handle API requests.
        """
        try:
            payload = json.dumps(payload)
            response = requests.request(method=method, url=url, data=payload, headers=cls.AUTH_HEADERS)
            data = {
                "response_body": response.json(),
                "status_code": response.status_code,
                "status": "success"
            }
            return data

        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:
            data = {
                "response_body": response.text,
                "status_code": response.status_code,
                "status": "failed",
                "error": str(err)
            }
            return data

    @classmethod
    def _handle_response(cls, response):
        status_code = response.get("status_code")
        status = response.get("status")
        response_code = response.get("response_body", {}).get("response_code", "")
        if status == "success" and status_code == 200 and response_code == "00":
            resp = response.get("response_body", {}).get("data", None)
            if resp == None:
                resp = response.get("response_body", {})
            data = {
                "status": "success",
                "message": "Successful",
                "data": resp
            }
        elif status == "success":
            message = response.get("response_body", {}).get("response_description", {})
            data = {
                "status": "failed",
                "message": message,
            }
        else:
            message = response.get("response_body", "")
            error = response.get("error", "")
            data = {
                "status": "error",
                "message": message,
                "error": error
            }
        return data


class DSGSResource:

    @staticmethod
    def _handle_failed_response(response, resp_status):
        if resp_status == "failed":
            return {
                "status": "failed",
                "message": response.get("message")
            }
        else:
            return {
                "status": "error",
                "message": response.get("message"),
                "error": response.get("error")
            }

    @staticmethod
    def get_name_enquiry(account_number):
        """
        Get name enquiry from DSGS API.

        Args:
            account_number: The account number to query

        Returns:
            A dictionary with status and account information
        """
        response = DSGSIntegration.name_enquiry(account_number)
        resp_status = response.get("status")

        if resp_status == "success":
            resp_data = response.get("data", {}).get("response_detail", {})
            # Extract account name from the response based on the DSGS API structure
            # Adjust the field names according to the actual DSGS API response
            data = {
                "status": "success",
                "account_number": account_number,
                "account_name": resp_data.get("account_name"),
                "name_enquiry_ref": resp_data.get("name_enquiry_ref")
            }
            print(f"DATA ENQ> >> {data}")
            return data
        else:
            return DSGSResource._handle_failed_response(response, resp_status)

    @staticmethod
    def get_account_enquiry(account_number):
        """
        Get account balance enquiry from DSGS API.

        Args:
            account_number: The account number to query

        Returns:
            A dictionary with status and account balance information
        """
        response = DSGSIntegration.balance_enquiry(account_number)
        resp_status = response.get("status")

        if resp_status == "success":
            resp_data = response.get("data", {}).get("response_detail", {})
            # Extract balance information from the response based on the DSGS API structure
            # Adjust the field names according to the actual DSGS API response
            data = {
                "status": "success",
                "account_balance": resp_data.get("available_balance"),
                "book_balance": resp_data.get("book_balance"),
            }
            print(f"DATA BAL> >> {data}")
            return data
        else:
            return DSGSResource._handle_failed_response(response, resp_status)
