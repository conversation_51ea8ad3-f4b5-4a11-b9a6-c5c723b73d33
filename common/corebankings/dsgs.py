import json
import requests
import logging

from django.conf import settings

log = logging.getLogger('django')

class DSGSIntegration:

    BASE_URL = settings.DSGS_BASE_URL
    AUTH_HEADERS = {
        "content-type": "application/json",
        "accept": "application/json",
    }

    @classmethod
    def name_enquiry(cls, account_number):
        """
        Get account name enquiry from DSGS API.

        Args:
            account_number: The account number to query

        Returns:
            The API response with account name data
        """
        url = f"{cls.BASE_URL}/MyBank/webresources/getClientDetails/AccountNameEnq"

        payload = {
            "account_number": account_number
        }

        log.info(f"DSGS INTEGRATION: Name Enquiry Payload >> {payload}")
        request = cls._handle_request("POST", url, payload)
        log.info(f"DSGS INTEGRATION: Name Enquiry Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"DSGS INTEGRATION: Name Enquiry Response >> {response}")
        return response

    @classmethod
    def balance_enquiry(cls, account_number):
        """
        Get account balance enquiry from DSGS API.

        Args:
            account_number: The account number to query

        Returns:
            The API response with account balance data
        """
        url = f"{cls.BASE_URL}/MyBank/webresources/getAccountBalance/AccountBalance"

        payload = {
            "account_number": account_number
        }

        log.info(f"DSGS INTEGRATION: Balance Enquiry Payload >> {payload}")
        request = cls._handle_request("POST", url, payload)
        log.info(f"DSGS INTEGRATION: Balance Enquiry Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"DSGS INTEGRATION: Balance Enquiry Response >> {response}")
        return response

    @classmethod
    def send_money(cls, payload_class):
        """
        Send money transfer via DSGS API.

        Args:
            payload_class: Object containing transfer details with attributes:
                - amount: The amount to transfer
                - sender_account: The sender's account number
                - sender_institution_code: The sender's institution code
                - destination_account: The destination account number
                - destination_institution_code: The destination institution code
                - destination_account_name: The destination account name
                - remark: Transfer remark/description

        Returns:
            The API response with money transfer data
        """
        url = f"{cls.BASE_URL}/MyBank/webresources/getAccountPostings/SendMoney"

        payload = {
            "amount": str(payload_class.amount),
            "sender_account": payload_class.sender_account,
            "sender_institution_code": payload_class.sender_institution_code,
            "destination_account": payload_class.destination_account,
            "destination_institution_code": payload_class.destination_institution_code,
            "destination_account_name": payload_class.destination_account_name,
            "remark": payload_class.remark
        }

        log.info(f"DSGS INTEGRATION: Send Money Payload >> {payload}")
        request = cls._handle_request("POST", url, payload)
        log.info(f"DSGS INTEGRATION: Send Money Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"DSGS INTEGRATION: Send Money Response >> {response}")
        return response

    @classmethod
    def account_statement(cls, bank_code, account_number, start_date=None, end_date=None):
        """
        Get account statement from DSGS API.

        Args:
            bank_code: The bank code
            account_number: The account number to query
            start_date: Start date for statement (format: YYYYMMDD)
            end_date: End date for statement (format: YYYYMMDD)

        Returns:
            The API response with account statement data
        """
        url = f"{cls.BASE_URL}/MyBank/webresources/getAccountStatement/Statement?bankCode={bank_code}&accountNumber={account_number}"

        if start_date:
            url += f"&startDate={start_date}"
        if end_date:
            url += f"&endDate={end_date}"

        log.info(f"DSGS INTEGRATION: Account Statement URL >> {url}")

        request = cls._handle_request("GET", url)
        log.info(f"DSGS INTEGRATION: Account Statement Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"DSGS INTEGRATION: Account Statement Response >> {response}")
        return response


    @classmethod
    def _handle_request(
        cls,
        method: str,
        url: str,
        payload: dict = {}
    ):
        """
        Helper method to handle API requests.
        """
        try:
            payload = json.dumps(payload)
            response = requests.request(method=method, url=url, data=payload, headers=cls.AUTH_HEADERS)
            data = {
                "response_body": response.json(),
                "status_code": response.status_code,
                "status": "success"
            }
            return data

        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:
            data = {
                "response_body": response.text,
                "status_code": response.status_code,
                "status": "failed",
                "error": str(err)
            }
            return data

    @classmethod
    def _handle_response(cls, response):
        status_code = response.get("status_code")
        status = response.get("status")
        response_code = response.get("response_body", {}).get("response_code", "")
        if status == "success" and status_code == 200 and response_code == "00":
            resp = response.get("response_body", {}).get("data", None)
            if resp == None:
                resp = response.get("response_body", {})
            data = {
                "status": "success",
                "message": "Successful",
                "data": resp
            }
        elif status == "success":
            message = response.get("response_body", {}).get("response_description", {})
            data = {
                "status": "failed",
                "message": message,
            }
        else:
            message = response.get("response_body", "")
            error = response.get("error", "")
            data = {
                "status": "error",
                "message": message,
                "error": error
            }
        return data


class DSGSResource:

    @staticmethod
    def _handle_failed_response(response, resp_status):
        if resp_status == "failed":
            return {
                "status": "failed",
                "message": response.get("message")
            }
        else:
            return {
                "status": "error",
                "message": response.get("message"),
                "error": response.get("error")
            }

    @staticmethod
    def get_name_enquiry(account_number):
        """
        Get name enquiry from DSGS API.

        Args:
            account_number: The account number to query

        Returns:
            A dictionary with status and account information
        """
        response = DSGSIntegration.name_enquiry(account_number)
        resp_status = response.get("status")

        if resp_status == "success":
            resp_data = response.get("data", {}).get("response_detail", {})
            # Extract account name from the response based on the DSGS API structure
            # Adjust the field names according to the actual DSGS API response
            data = {
                "status": "success",
                "account_number": account_number,
                "account_name": resp_data.get("account_name"),
                "name_enquiry_ref": resp_data.get("name_enquiry_ref")
            }
            print(f"DATA ENQ> >> {data}")
            return data
        else:
            return DSGSResource._handle_failed_response(response, resp_status)

    @staticmethod
    def get_account_enquiry(account_number):
        """
        Get account balance enquiry from DSGS API.

        Args:
            account_number: The account number to query

        Returns:
            A dictionary with status and account balance information
        """
        response = DSGSIntegration.balance_enquiry(account_number)
        resp_status = response.get("status")

        if resp_status == "success":
            resp_data = response.get("data", {}).get("response_detail", {})
            # Extract balance information from the response based on the DSGS API structure
            # Adjust the field names according to the actual DSGS API response
            data = {
                "status": "success",
                "account_balance": resp_data.get("available_balance"),
                "book_balance": resp_data.get("book_balance"),
            }
            print(f"DATA BAL> >> {data}")
            return data
        else:
            return DSGSResource._handle_failed_response(response, resp_status)

    @staticmethod
    def send_money_transfer(payload_class):
        """
        Send money transfer via DSGS API.

        Args:
            payload_class: Object containing transfer details with attributes:
                - amount: The amount to transfer
                - sender_account: The sender's account number
                - sender_institution_code: The sender's institution code
                - destination_account: The destination account number
                - destination_institution_code: The destination institution code
                - destination_account_name: The destination account name
                - remark: Transfer remark/description

        Returns:
            A dictionary with status and transfer information
        """
        response = DSGSIntegration.send_money(payload_class)
        resp_status = response.get("status")

        if resp_status == "success":
            resp_data = response.get("data", {}).get("response_detail", {})
            # Extract transfer information from the response based on the DSGS API structure
            # Adjust the field names according to the actual DSGS API response
            data = {
                "status": "success",
                "transaction_reference": resp_data.get("transaction_reference"),
                "transfer_status": resp_data.get("transfer_status"),
                "amount": payload_class.amount,
                "sender_account": payload_class.sender_account,
                "destination_account": payload_class.destination_account,
                "destination_account_name": payload_class.destination_account_name
            }
            print(f"DATA TRANSFER> >> {data}")
            return data
        else:
            return DSGSResource._handle_failed_response(response, resp_status)
