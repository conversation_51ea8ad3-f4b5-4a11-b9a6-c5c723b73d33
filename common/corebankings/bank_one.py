from decimal import ROUND_DOWN, Decimal
import logging
import requests
import json

from django.conf import settings
from django.db.models import Q

from bank.models import Bank
from common.functions import get_reference
from common.models import BVN
from dfa.models import AgentOnboardedAccount

log = logging.getLogger('omni_send_money')

class BankOneIntegration:

    BASE_URL = settings.BANK_ONE_API_URL
    AUTH_TOKEN = settings.BANK_ONE_API_KEY

    AUTH_HEADERS = {
        "content-type": "application/json",
        "accept": "application/json",
    }

    @classmethod
    def name_enquiry(cls, account_number, bank_code):
        url = f"{cls.BASE_URL}/thirdpartyapiservice/apiservice/Transfer/NameEnquiry"
        payload = {

            "AccountNumber": account_number,
            "BankCode": bank_code,
            "Token": cls.AUTH_TOKEN,
        }
        log.info(f"BANK ONE INTEGRATION: Name Enquiry Payoad >> {payload}")
        request = cls._handle_request("POST", url, payload)
        log.info(f"BANK ONE INTEGRATION: Account Enquiry Request >> {payload}")
        response = cls._handle_response(request)
        log.info(f"BANK ONE INTEGRATION: Account Enquiry Response >> {payload}")
        return response

    @classmethod
    def account_enquiry(cls, account_number):
        url = f"{cls.BASE_URL}/thirdpartyapiservice/apiservice/Account/AccountEnquiry"
        payload = {
            "AccountNo": account_number,
            "AuthenticationCode": cls.AUTH_TOKEN,
        }
        log.info(f"BANK ONE INTEGRATION: Account Enquiry Payoad >> {payload}")
        request = cls._handle_request("POST", url, payload)
        log.info(f"BANK ONE INTEGRATION: Account Enquiry Request >> {request}")
        response = cls._handle_response(request)
        log.info(f"BANK ONE INTEGRATION: Account Enquiry Response >> {payload}")
        return response

    @classmethod
    def create_customer(
        cls,
        bvn: BVN,
        account: AgentOnboardedAccount,
        other_details_dict: dict = None,
    ):
        url = f"{cls.BASE_URL}/BankOneWebAPI/api/Customer/CreateCustomer/2?authToken={cls.AUTH_TOKEN}"
        payload = {
            "HasCompleteDocumentatiOn": True,
            "LastName": account.last_name,
            "OtherNames": f"{account.first_name} {account.middle_name or ''}",
            "City": account.city,
            "Address": account.address,
            "Gender": '0' if bvn.gender.lower() == "male" else '1',
            "DateOfBirth": account.dob,
            "PhoneNo": account.phone_number,
            "PlaceOfBirth": other_details_dict.get("place_of_birth", ""),
            "NationalIdentityNo": other_details_dict.get("nin"),
            "BankVerificationNumber": account.bvn,
            "Email": account.email if not None else "",
            "AccountOfficerCode": "003", # To be dynamically retrieved later
            "NextOfKinPhoneNo": other_details_dict.get("next_kin_phone", ""),
            "NextOfKinName": other_details_dict.get("next_kin_name", ""),
            # "ReferralPhoneNo": '',
            # "ReferralName": '',
        }

        request = cls._handle_request("POST", url, payload=payload)
        response = cls._handle_response(request)
        return response

    @classmethod
    def create_account(
        cls,
        bvn: BVN,
        account_type: str,
        customer_id: str,
        account: AgentOnboardedAccount,
        other_details_dict: dict = None
    ):
        url = f"{cls.BASE_URL}/BankOneWebAPI/api/Account/CreateAccountQuick/2?authToken={cls.AUTH_TOKEN}"
        payload = {
            "TransactionTrackingRef": get_reference(),
            "AccountOpeningTrackingRef": get_reference(),
            "ProductCode": "101" if account_type == "savings" else "102" ,
            "CustomerID": customer_id,
            "LastName": account.last_name,
            "OtherNames": f"{account.first_name} {account.middle_name or ''}",
            "BVN": account.bvn,
            "PhoneNo": account.phone_number,
            "Gender": '0' if bvn.gender.lower() == "male" else '1',
            "PlaceOfBirth": other_details_dict.get("place_of_birth", ""),
            "DateOfBirth": account.dob,
            "Address": account.address,
            "AccountOfficerCode": "003",
            "Email": account.email if not None else "",
            "NotificationPreference": 0,
            "TransactionPermission": "0",
            "AccountTier": "1"
            }

        request = cls._handle_request("POST", url, payload=payload)
        response = cls._handle_response(request)
        return response

    @classmethod
    def transfer_internal(
        cls,
        payload_class
    ):
        amount_in_kobo = (payload_class.amount * Decimal('100')).quantize(Decimal('1'), rounding=ROUND_DOWN)
        url = f"{cls.BASE_URL}/thirdpartyapiservice/apiservice/CoreTransactions/LocalFundsTransfer"
        payload = {
            "AuthenticationKey": cls.AUTH_TOKEN,
            "Narration": payload_class.narration,
            "RetrievalReference": str(payload_class.transaction_ref),
            "FromAccountNumber": payload_class.sender_account,
            "ToAccountNumber": payload_class.destination_account,
            "Amount": str(amount_in_kobo)
        }
        log.info(f"BANK ONE INTEGRATION: Intra Send Money Payload >> {payload}")

        request = cls._handle_request("POST", url, payload=payload)
        response = cls._handle_response(request)
        return response

    @classmethod
    def transfer_external(
        cls,
        payload_class
    ):
        amount_in_kobo = (payload_class.amount * Decimal('100')).quantize(Decimal('1'), rounding=ROUND_DOWN)
        dest_bank = Bank.objects.filter(Q(nip_code=payload_class.destination_institution_code) | Q(code=payload_class.destination_institution_code)).first()
        url = f"{cls.BASE_URL}/thirdpartyapiservice/apiservice/Transfer/InterBankTransfer"
        payload = {
            "Amount": str(amount_in_kobo),
            "AppzoneAccount": "",
            "Payer": payload_class.sender_account_name,
            "PayerAccountNumber": payload_class.sender_account,
            "ReceiverAccountNumber": payload_class.destination_account,
            "ReceiverAccountType": "",
            "ReceiverBankCode": dest_bank.code,
            "ReceiverPhoneNumber": "",
            "ReceiverName": payload_class.destination_account_name,
            "ReceiverBVN": "",
            "ReceiverKYC": "",
            "Narration": payload_class.narration,
            "TransactionReference": str(payload_class.transaction_ref),
            "NIPSessionID": str(payload_class.session_id),
            "Token": cls.AUTH_TOKEN
        }

        log.info(f"BANK ONE INTEGRATION: Inter Send Money Payload >> {payload}")

        request = cls._handle_request("POST", url, payload=payload)
        log.info(f"BANK ONE INTEGRATION: Inter Send Money Request >> {request}")
        response = cls._handle_response(request)
        return response

    @classmethod
    def get_transactions(
        cls,
        account_number,
        from_date=None,
        to_date=None,
    ):
        """Get account transactions from Bank One API.

        Args:
            account_number: The account number to get transactions for
            from_date: Optional start date in format YYYY-MM-DD
            to_date: Optional end date in format YYYY-MM-DD
            number_of_items: Number of transactions to return (default: 10)

        Returns:
            The API response with transaction data
        """

        url = f"{cls.BASE_URL}/BankOneWebAPI/api/Account/GetTransactions/2?authtoken={cls.AUTH_TOKEN}&accountNumber={account_number}"

        if from_date and to_date:
            url += f"&fromDate={from_date}&toDate={to_date}"

        request = cls._handle_request("GET", url)
        log.info(f"BANKONE GET TRANSACTIONS REQUEST >> {request}")
        response = cls._handle_response(request)
        log.info(f"BANKONE GET TRANSACTIONS RESPONSE >> {response}")
        return response

    @classmethod
    def get_customer_loans(
        cls,
        customer_id,
        institution_code
    ):
        """Get loans by customer ID from Bank One API.

        Args:
            customer_id: The customer ID to get loans for
            institution_code: The institution code (bank code)

        Returns:
            The API response with customer loan data
        """

        url = f"{cls.BASE_URL}/BankOneWebAPI/api/Loan/GetLoansByCustomerId/2?authToken={cls.AUTH_TOKEN}&institutionCode={institution_code}&CustomerId={customer_id}"

        request = cls._handle_request("GET", url)
        response = cls._handle_response(request)
        return response

    @classmethod
    def get_loan_repayment_schedule(
        cls,
        loan_account_number
    ):
        """Get loan repayment schedule from Bank One API.

        Args:
            loan_account_number: The loan account number to get repayment schedule for

        Returns:
            The API response with loan repayment schedule data
        """

        url = f"{cls.BASE_URL}/BankOneWebAPI/api/Loan/GetLoanRepaymentSchedule/2?authToken={cls.AUTH_TOKEN}&loanAccountNumber={loan_account_number}"

        request = cls._handle_request("GET", url)
        response = cls._handle_response(request)
        return response

    @classmethod
    def get_loan_account_statement(
        cls,
        account_number,
        institution_code,
        number_of_items,
        from_date=None,
        to_date=None
    ):
        """Get loan account statement from Bank One API.

        Args:
            account_number: The loan account number to get statement for
            institution_code: The institution code (bank code)
            from_date: Optional start date in format YYYY-MM-DD
            to_date: Optional end date in format YYYY-MM-DD
            number_of_items: Number of items to return (default: 10)

        Returns:
            The API response with loan account statement data
        """

        url = f"{cls.BASE_URL}/BankOneWebAPI/api/LoanAccount/LoanAccountStatement/2?authToken={cls.AUTH_TOKEN}&accountNumber={account_number}&institutionCode={institution_code}&numberofItems={number_of_items}"

        # Add date parameters if provided
        if from_date:
            url += f"&fromDate={from_date}"
        if to_date:
            url += f"&toDate={to_date}"

        request = cls._handle_request("GET", url)
        response = cls._handle_response(request)
        return response

    @classmethod
    def get_loan_account_balance(
        cls,
        customer_id
    ):
        """Get loan account balance from Bank One API.

        Args:
            customer_id: The customer ID to get loan account balance for

        Returns:
            The API response with loan account balance data
        """

        url = f"{cls.BASE_URL}/BankOneWebAPI/api/LoanAccount/LoanAccountBalance2/2?authToken={cls.AUTH_TOKEN}&customerIDInString={customer_id}"

        request = cls._handle_request("GET", url)
        response = cls._handle_response(request)
        return response

    @classmethod
    def repay_loan(
        cls,
        account_number,
        repayment_amount,
        principal_narration,
        interest_narration,
        is_termination=False
    ):
        """Repay loan through Bank One API.

        Args:
            account_number: The loan account number
            repayment_amount: The amount to repay
            principal_narration: Narration for principal payment
            interest_narration: Narration for interest payment
            is_termination: Whether this is a loan termination (default: False)

        Returns:
            The API response with loan repayment data
        """

        url = f"{cls.BASE_URL}/BankOneWebAPI/api/LoanAccount/RepayLoan/2?authtoken={cls.AUTH_TOKEN}&accountNumber={account_number}&repaymentAmount={repayment_amount}&principalNarration={principal_narration}&interestNarration={interest_narration}&isTermination={str(is_termination).lower()}"

        request = cls._handle_request("POST", url)
        response = cls._handle_response(request)
        return response

    @classmethod
    def _handle_request(
        cls,
        method: str,
        url: str,
        payload: dict = {}
    ):
        """
        Helper method to handle API requests.
        """
        try:
            payload = json.dumps(payload)
            response = requests.request(method=method, url=url, data=payload, headers=cls.AUTH_HEADERS)
            data = {
                "response_body": response.json(),
                "status_code": response.status_code,
                "status": "success"
            }
            return data

        except (
            requests.exceptions.RequestException,
            Exception,
            json.decoder.JSONDecodeError,
        ) as err:
            data = {
                "response_body": response.text,
                "status_code": response.status_code,
                "status": "failed",
                "error": str(err)
            }
            return data


    @classmethod
    def _handle_response(cls, response):
        status_code = response.get("status_code")
        status = response.get("status")
        try:
            response_status = response.get("response_body", {}).get("IsSuccessful", None)
            if response_status == None:
                response_status = response.get("response_body", {}).get("ResponseStatus", None)
                if response_status is not None:
                    response_status = False if response_status == "Failed" else True
            # elif response_status == None:
            #     response_status = response.get("response_body", {}).get("RequestStatus", None)
        except AttributeError:
            response = response.get("response_body", {})[0]
            response = {"response_body": response}

        if status == "success" and status_code == 200:
            customer_id = response.get("response_body", {}).get("CustomerID", None)
            # This is cause the request to create a customer does not return `IsSuccessful`, so this is the only way
            # to determine this request was successful
            if response_status is not None:
                if response_status == True:
                    resp_data = response.get("response_body", {})
                    data = {
                        "status": "success",
                        "message": "Successful",
                        "data": resp_data
                    }
                    return data
                else:
                    message = response.get("response_body", {}).get("Message", "")
                    if message == "":
                        message = response.get("response_body", {}).get("ResponseMessage", None)
                    data = {
                        "status": "failed",
                        "message": message,
                    }
                    return data

            elif customer_id:
                resp_data = response.get("response_body", {})
                data = {
                        "status": "success",
                        "message": "Successful",
                        "data": resp_data
                    }
                return data
            else:
                data = {
                        "status": "failed",
                        "message": "No Response Quantifier",
                    }
                return data

        elif status == "success" and status_code == 400:
            message = response.get("response_body", {}).get("error-Message", None)
            data = {
                "status": "failed",
                "message": message or "Unble to retrieve error message",
            }
            return data

        elif status == "success":
            message = response.get("response_body", {}).get("ResponseDescription", None)
            if message is None:
                message = response.get("response_body", {}).get("ResponseMessage", {})
            data = {
                "status": "failed",
                "message": message,
            }
            return data
        else:
            message = response.get("response_body", "")
            error = response.get("error", "")
            data = {
                "status": "error",
                "message": message,
                "error": error
            }
            return data


class BankOneResource:

    @staticmethod
    def _handle_failed_response(response, resp_status):
        if resp_status == "failed":
            message = response.get("data", {}).get("ResponseMessage", "")
            return {
                "status": "failed",
                "message": response.get("message") if message == "" or message is None else message
            }
        else:
            return {
                "status": "error",
                "message": response.get("data", {}).get("ResponseMessage", ""),
                "error": response.get("error")
            }

    @staticmethod
    def get_name_enquiry(account_number, bank_code):
        """
        Get name enquiry from Bank One API.
        """
        response = BankOneIntegration.name_enquiry(account_number, bank_code)
        resp_status = response.get("status")
        if resp_status == "success":
            resp_data = response.get("data")
            data = {
                "status": "success",
                "account_number": account_number,
                "account_name": resp_data.get("Name"),
                "nip_session_id": resp_data.get("SessionID")

            }
            return data
        else:
            return BankOneResource._handle_failed_response(response, resp_status)

    @staticmethod
    def get_account_enquiry(account_number):
        """
        Get account enquiry from Bank One API.
        """
        response = BankOneIntegration.account_enquiry(account_number)
        resp_status = response.get("status")
        if resp_status == "success":
            resp_data = response.get("data")
            data = {
                "status": "success",
                "account_name": resp_data.get("Name"),
                "account_balance": resp_data.get("AvailableBalance"),
                "book_balance": resp_data.get("LedgerBalance"),
            }
            return data
        else:
            return BankOneResource._handle_failed_response(response, resp_status)

    @staticmethod
    def create_customer_and_account(
        bvn: BVN,
        account_type: str,
        account: AgentOnboardedAccount,
        other_details_dict: dict = None
    ):

        """
        Create customer and account in Bank One API.
        """
        customer_response = BankOneIntegration.create_customer(
            bvn=bvn,
            account=account,
            other_details_dict=other_details_dict
        )
        customer_status = customer_response.get("status")
        if customer_status == "success":
            customer_id = customer_response.get("data").get("CustomerID")
            account_response = BankOneIntegration.create_account(
                bvn=bvn,
                account_type=account_type,
                customer_id=customer_id,
                account=account,
                other_details_dict=other_details_dict
            )
            account_status = account_response.get("status")
            if account_status == "success":
                resp_data = account_response.get("data").get("Message")
                data = {
                    "status": "success",
                    "account_number": resp_data.get("AccountNumber"),
                    "customer_number": customer_id
                }
                return data
            else:
                return BankOneResource._handle_failed_response(account_response, account_status)

        else:
            return BankOneResource._handle_failed_response(customer_response, customer_status)

    @staticmethod
    def fund_transfer_internal(payload_class):
        response = BankOneIntegration.transfer_internal(payload_class)
        resp_status = response.get("status")
        if resp_status == "success" and response.get("data", {}).get("ResponseCode") == "00":
            data = {
                "status": "success",
                "cba_reference": response.get("data", {}).get("Reference")
            }

        else:
            resp_status = "failed" if resp_status == "success" else "error"
            data = BankOneResource._handle_failed_response(response, resp_status)
        return data

    @staticmethod
    def fund_transfer_external(payload_class):
        response = BankOneIntegration.transfer_external(payload_class)
        resp_status = response.get("status")
        if resp_status == "success" and response.get("data", {}).get("ResponseCode") == "00":
            return {
                "status": "success",
                "cba_reference": response.get("data", {}).get("Reference")
            }

        else:
            if resp_status == "failed":
                return {
                    "status": "failed",
                    "message": response.get("data", {}).get("ResponseMessage", "")
            }
            else:
                return {
                    "status": "error",
                    "message": response.get("data", {}).get("ResponseMessage", ""),
                    "error": response.get("error")
                }

    @staticmethod
    def get_account_transactions(account_number, from_date=None, to_date=None):
        """
        Get account transactions from Bank One API.

        Args:
            account_number: The account number to get transactions for
            from_date: Optional start date in format YYYY-MM-DD
            to_date: Optional end date in format YYYY-MM-DD
            number_of_items: Number of transactions to return (default: 10)

        Returns:
            Dictionary containing transaction data with standardized field names
        """
        response = BankOneIntegration.get_transactions(
            account_number=account_number, from_date=from_date, to_date=to_date
            )
        resp_status = response.get("status")
        if resp_status == "success":
            resp_data = response.get("data")
            raw_transactions = resp_data.get("Message", [])
            data = {
                "status": "success",
                "transactions": raw_transactions,
                "is_successful": resp_data.get("IsSuccessful", False)
            }
            return data
        else:
            return BankOneResource._handle_failed_response(response, resp_status)

    @staticmethod
    def get_customer_loans(customer_id, institution_code):
        """
        Get customer loans from Bank One API.

        Args:
            customer_id: The customer ID to get loans for
            institution_code: The institution code (bank code)

        Returns:
            Dictionary containing customer loan data with standardized field names
        """
        response = BankOneIntegration.get_customer_loans(customer_id, institution_code)
        resp_status = response.get("status")
        if resp_status == "success":

            loans = response.get("Message", [])
            data = {
                "status": "success",
                "loans": loans,
            }
            return data
        else:
            return BankOneResource._handle_failed_response(response, resp_status)

    @staticmethod
    def get_loan_repayment_schedule(loan_account_number):
        """
        Get loan repayment schedule from Bank One API.

        Args:
            loan_account_number: The loan account number to get repayment schedule for

        Returns:
            Dictionary containing loan repayment schedule data
        """
        response = BankOneIntegration.get_loan_repayment_schedule(loan_account_number)
        resp_status = response.get("status")
        if resp_status == "success":
            schedules = response.get("data", {}).get("Message", [])
            data = {
                "status": "success",
                "schedules": schedules,
            }
            return data
        else:
            data = BankOneResource._handle_failed_response(response, resp_status)
            return data

    @staticmethod
    def get_loan_account_statement(account_number, institution_code, from_date=None, to_date=None, number_of_items=10):
        """
        Get loan account statement from Bank One API.

        Args:
            account_number: The loan account number to get statement for
            institution_code: The institution code (bank code)
            from_date: Optional start date in format YYYY-MM-DD
            to_date: Optional end date in format YYYY-MM-DD
            number_of_items: Number of items to return (default: 10)

        Returns:
            Dictionary containing loan account statement data
        """
        response = BankOneIntegration.get_loan_account_statement(
            account_number,
            institution_code,
            from_date,
            to_date,
            number_of_items
        )
        resp_status = response.get("status")
        if resp_status == "success":
            statements = response.get("data", {}).get("Message", [])
            data = {
                "status": "success",
                "statements": statements,
            }
            return data
        else:
            data = BankOneResource._handle_failed_response(response, resp_status)
            return data

    @staticmethod
    def get_loan_account_balance(customer_id):
        """
        Get loan account balance from Bank One API.

        Args:
            customer_id: The customer ID to get loan account balance for

        Returns:
            Dictionary containing loan account balance data
        """
        response = BankOneIntegration.get_loan_account_balance(customer_id)
        resp_status = response.get("status")
        if resp_status == "success":
            balances = response.get("data", {}).get("Message", [])
            data = {
                "status": "success",
                "balances": balances,
            }
            return data
        else:
            data = BankOneResource._handle_failed_response(response, resp_status)
            return data

    @staticmethod
    def repay_loan(account_number, repayment_amount, principal_narration, interest_narration, is_termination=False):
        """
        Repay loan through Bank One API.

        Args:
            account_number: The loan account number
            repayment_amount: The amount to repay
            principal_narration: Narration for principal payment
            interest_narration: Narration for interest payment
            is_termination: Whether this is a loan termination (default: False)

        Returns:
            Dictionary containing loan repayment result
        """
        response = BankOneIntegration.repay_loan(
            account_number,
            repayment_amount,
            principal_narration,
            interest_narration,
            is_termination
        )
    
        resp_status = response.get("status")
        if resp_status == "success":
            repayment_result = response.get("data", {})
            data = {
                "status": "success",
                "repayment_result": repayment_result,
            }
            return data
        else:
            data = BankOneResource._handle_failed_response(response, resp_status)
            return data

