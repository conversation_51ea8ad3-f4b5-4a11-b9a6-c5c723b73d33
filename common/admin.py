from typing import Optional
from django.contrib import admin
from django.http.request import HttpRequest
from .models import (
    AuthenticationType,
    BVN,
    MobileApplicationVersion,
    Platform,
    SettlementAccount,
    State,
    LocalGovernment,
    TransactionType,
    AccessChannel,
    TelecommunicationNetwork,
    CurrencyType,
    PersistPaystackPayments,
    ManualReversals
)


class AuthenticationTypeAdmin(admin.ModelAdmin):
    search_fields = ["name"]
    list_filter = ["name"]
    list_display = ("name", "status")


class CurrencyTypeAdmin(admin.ModelAdmin):
    search_fields = ["name"]
    list_filter = ["name"]
    list_display = ("name", "status")


class StateAdmin(admin.ModelAdmin):
    search_fields = ["name"]
    list_filter = ["name"]
    list_display = ("name", "status")


class LocalGovernmentAdmin(admin.ModelAdmin):
    search_fields = ["name"]
    list_filter = ["name"]
    list_display = ("name", "state", "status")


class TransactionTypeAdmin(admin.ModelAdmin):
    search_fields = ["name", "code"]
    list_filter = ["code"]
    list_display = ("name", "code", "status")


class AccessChannelAdmin(admin.ModelAdmin):
    search_fields = ["name", "code"]
    list_filter = ["code"]
    list_display = ("name", "code", "status")


class TelecommunicationNetworkAdmin(admin.ModelAdmin):
    search_fields = ["name", "code", "country"]
    list_filter = ["code"]
    list_display = ("name", "code", "country", "status")


class CurrencyTypeAdmin(admin.ModelAdmin):
    search_fields = ["name", "symbol", "status"]
    list_filter = ["name", "symbol", "status"]
    list_display = ("name", "symbol", "status")


class SettlementAccountAdmin(admin.ModelAdmin):
    search_fields = ["account_name", "account_number"]
    list_filter = ["transaction_type"]
    list_display = [
        "account_name",
        "bank",
        "account_number",
        "transaction_type",
        "date_created",
    ]


class BVNAdmin(admin.ModelAdmin):
    search_fields = ["bvn", "first_name", "last_name"]
    list_display = ["first_name", "last_name", "bvn", "phone_number"]


class PlatformAdmin(admin.ModelAdmin):
    search_fields = ["name", "code"]
    list_display = ["name", "code", "date_created"]


class AppVersionAdmin(admin.ModelAdmin):
    search_fields = ["version_id", "platform__name", "platform__code"]
    list_display = ["platform", "version_id", "date_created"]

    def has_change_permission(self, request, obj=None) -> bool:
        return False

    def has_add_permission(self, request, obj=None) -> bool:
        return False


class PersisPaystackAdmin(admin.ModelAdmin):
    list_per_page = 20
    search_fields = ["reference_id", "external_reference", "bill_payment_ref", "payment_type", "verified"]
    list_display = [
        "bank",
        "reference_id",
        "external_reference",
        "bill_payment_ref",
        "ledger_account_ref",
        "amount",
        "last_four_digits",
        "payment_type",
        "verified",
        "created"
    ]
    
    def has_change_permission(self, request, obj=None) -> bool:
        return False

    def has_add_permission(self, request, obj=None) -> bool:
        return False
    
class ManualReversalAdmin(admin.ModelAdmin):
    search_fields = ["user_ref", "transaction_type", "bank_admin", "status"]
    list_filter = ["transaction_type"]
    list_display = [
        "reversal_transaction_ref",
        "user_ref",
        "transaction_type",
        "core_banking_ref",
        "bank_admin",
        "status",
        "date"
    ]


admin.site.register(ManualReversals, ManualReversalAdmin)
admin.site.register(PersistPaystackPayments, PersisPaystackAdmin)
admin.site.register(AuthenticationType, AuthenticationTypeAdmin)
admin.site.register(State, StateAdmin)
admin.site.register(LocalGovernment, LocalGovernmentAdmin)
admin.site.register(TransactionType, TransactionTypeAdmin)
admin.site.register(AccessChannel, AccessChannelAdmin)
admin.site.register(TelecommunicationNetwork, TelecommunicationNetworkAdmin)
admin.site.register(CurrencyType, CurrencyTypeAdmin)
admin.site.register(SettlementAccount, SettlementAccountAdmin)
admin.site.register(BVN, BVNAdmin)
admin.site.register(Platform, PlatformAdmin)
admin.site.register(MobileApplicationVersion, AppVersionAdmin)
