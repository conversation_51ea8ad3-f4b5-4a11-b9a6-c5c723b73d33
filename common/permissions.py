from rest_framework.permissions import Base<PERSON>ermission

from .functions import get_client_credentials, get_platform


class MobileAccessPermission(BasePermission):
    """
    Permission to determine if basic auth credentials is mapped for mobile app clients
    """

    message = "Access denied, API for mobile only."

    def has_permission(self, request, view):

        try:
            """
            Validate request point of entry
            """
            try:
                client_id, client_secret = get_client_credentials(
                    token=request.auth.token
                )
            except Exception:
                client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
            if request_platform.upper().startswith("MOBILE APPLICATION"):
                return True
            return False
        except Exception:
            return False


class LambdaAccessPermission(BasePermission):
    """
    Permission to determine if basic auth credentials is mapped for LAMBDA APPLICATION
    """

    message = "Access denied, API for lambda only."

    def has_permission(self, request, view):

        try:
            """
            Validate request point of entry
            """
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
            if request_platform.upper().startswith("AWS LAMBDA"):
                return True
            return False
        except Exception:
            return False


class BankPortalAccessPermission(BasePermission):
    """
    Permission to determine if basic auth credentials is mapped for bank admin portal
    """

    message = "Access denied, API for bank portal only."

    def has_permission(self, request, view):

        try:
            """
            Validate request point of entry
            """
            try:
                client_id, client_secret = get_client_credentials(
                    token=request.auth.token
                )
            except Exception:
                client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
            if request_platform.upper().startswith("BANK ADMIN PORTAL"):
                return True
            return False
        except Exception:
            return False


class DFAAccessPermission(BasePermission):
    """
    Permission to determine if basic auth credentials is mapped for mobile app clients
    """

    message = "Access denied, API for DFA Agent App only."

    def has_permission(self, request, view):

        try:
            """
            Validate request point of entry
            """
            try:
                client_id, client_secret = get_client_credentials(
                    token=request.auth.token
                )
            except Exception:
                client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
            if request_platform.upper().startswith("DFA"):
                return True
            return False
        except Exception:
            return False


class VerdantAccessPermission(BasePermission):
    """
    Permission to determine if basic auth credentials is mapped for mobile app clients
    """

    message = "Access denied, API for DFA Agent App only."

    def has_permission(self, request, view):

        try:
            """
            Validate request point of entry
            """
            try:
                client_id, client_secret = get_client_credentials(
                    token=request.auth.token
                )
            except Exception:
                client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )

            if request_platform.upper().startswith("VERDANT"):
                return True
            return False
        except Exception:
            return False


class IsThirdParty(BasePermission):
    """
    Permissions for third party channels connected to the omnichannel engine
    """

    message = "Access denied, API for valid third party channels."

    def has_permission(self, request, view):
        try:
            """
            Validate Third Party Channel
            """
            client_id, client_secret = get_client_credentials(request)

            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )

            if request_platform.upper().startswith("3RD-PARTY"):
                return True
            return False
        except Exception as e:
            return False
