from django.urls import path, include
from rest_framework import routers
from common import views
from common.views import (
    GetState,
    GetLocalGovtByState,
    #MakePaystackPayment,
    ResetDailyLimit,
    ValidateToken,
    NetworkByCountryCode,
    GenerateToken,
    NameEnquiry,
    BalanceEnquiry,
    GetChannels,
    ValidateBVN,
    ValidateAccountNumber,
    ValidateUtility,
    VerifyPayStackPayment,
    PersistPaystackPaymentsView,
    PersistPaystackLastFourDigits,
    RunPaystackPaymentsUpdateScheduler,
    ConfirmReversals,
    PerformManualReversals,
    GetPendingTransactions,
    ResetAgentDailyLimit,
    OneTimeResetUSSDAccessForAll,
    LoadUsersUSSDUsersToCache
)

router = routers.DefaultRouter()
router.register(r"mobile_app_version", views.MobileAppVersionViewset)
# router.register(r"state", views.StateViewSet)

urlpatterns = [
    path("", include(router.urls)),
    path("name_enquiry/", NameEnquiry.as_view(), name="name_enquiry"),
    #     path("account_history/", AccountHistory.as_view(), name="name_enquiry"),
    path("biller_category/", NameEnquiry.as_view(), name="biller_category"),
    path("balance_enquiry/", BalanceEnquiry.as_view(), name="balance_enquiry"),
    path(
        "validate_account_number/",
        ValidateAccountNumber.as_view(),
        name="validate_account_number",
    ),
    path("get_channels/", GetChannels.as_view(), name="get_channels"),
    path("get_state/", GetState.as_view(), name="get_state"),
    path(
        "get_local_government_by_state/<int:state_id>/",
        GetLocalGovtByState.as_view(),
        name="get_local_government_by_state",
    ),
    path("validate_token/", ValidateToken.as_view(), name="validate_token"),
    path(
        "network_by_country_code/country_code/<str:country_code>/",
        NetworkByCountryCode.as_view(),
        name="network_by_country_code",
    ),
    path(
        "generate_token/<str:identifier>/",
        GenerateToken.as_view(),
        name="generate_token",
    ),
    path(
        "validate_bvn/", 
        ValidateBVN.as_view(), 
        name="validate_bvn"
        ),
    path(
        "validate_utility/",
        ValidateUtility.as_view(),
        name="validate_utility"
        ),
    path(
        "reset_daily_limit/",
        ResetDailyLimit.as_view(),
        name="reset_daily_limit"
        ),
     path(
        "reset_agent_daily_limit/",
        ResetAgentDailyLimit.as_view(),
        name="reset_agent_daily_limit"
        ),
    path(
        "verify_paystack_payment/",
        VerifyPayStackPayment.as_view(),
        name="verify_paystack_payment"
        ),
    path(
        "persist_paystacks/",
        PersistPaystackPaymentsView.as_view(),
        name="persist_paystack_payments"
    ),
    path(
        "run_paystack_updates/",
        RunPaystackPaymentsUpdateScheduler.as_view(),
        name="run_paystack_updates"
    ),
    path(
        "persist_paystack_last_four_digits/",
        PersistPaystackLastFourDigits.as_view(),
        name="persist_paystack_last_four_digits"
    ),
    path(
        "perform_manual_reversal/",
        PerformManualReversals.as_view(),
        name="perform_manual_reversal"
    ),
    path(
        "confirm_reversals/",
        ConfirmReversals.as_view(),
        name="confirm_reversals"
    ),
    path(
        "get_pending_transactions/",
        GetPendingTransactions.as_view({"get": "list"}),
        name="get_unsuccessful_transactions"
    ),
    path(
        "one_time_reset_all_ussd_access/",
        OneTimeResetUSSDAccessForAll.as_view(),
        name="one_time_reset_all_ussd_access"
    ),
    path(
        "load_ussd_users/",
        LoadUsersUSSDUsersToCache.as_view(),
        name="load_ussd_users"
    )
    # path(
    #     "retrieve_unsuccessful_transaction/<str:user_ref>/<str:transaction_type>",
    #     GetUnsuccessfulTransactions.as_view({"get": "retrieve"}),
    #     name="retrieve_unsuccessful_transaction"
    # )
    
]
