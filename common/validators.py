# from django.core.exceptions import ValidationError
from django.utils.translation import ugettext_lazy as _
from rest_framework.exceptions import ValidationError


def validate_name(name:str):
    if not name.isalpha():
        raise ValidationError(_(f'{name} should contain only alphabets'))

def validate_phone_number(phone_number: str):

    try:
        int(phone_number)
    except Exception:
        raise ValidationError(_(f'Invalid phone number {phone_number}'))

    if phone_number.startswith('0') and len(phone_number) == 11:
        # omni channel hack for nigerian phone numbers
        phone_number = f'+234{phone_number[1:]}'
    
    elif phone_number.startswith('234') and len(phone_number) == 13:
        # omni channel hack for nigerian phone numbers
        phone_number = f'+{phone_number}'

    if not phone_number.startswith('+'):
        raise ValidationError(_(f'Invalid phone number {phone_number}. Confirm phone number format of +<CountryCode><PhoneNumber>'))

    if len(phone_number) > 16:
        raise ValidationError(_(f'Invalid phone number {phone_number}. Length greater than approved length'))

    # African phone number validation
    if phone_number.startswith('+2'):
        country_code = ['213', '244', '229', '267', '226', '257', '237', '238', '236', '269', '243', '253', '240', '291', '251', '241', '220', '233', '224', '245', '225', '254', '266', '231', '218', '261', '265', '223', '222', '230', '212', '258', '264', '227', '234', '242', '262', '250', '290', '239', '221', '248', '232', '252', '211', '249', '268', '255', '228', '216', '256', '212', '260', '263']
        if phone_number[1:4] not in country_code:
            if phone_number[1:3] not in ['20', '27']:
                raise ValidationError(_(f'Invalid phone number --> Invalid country code'))
        return phone_number
    
    # COMPLETE FOR OTHER CONTINENTS
    raise ValidationError(f'Invalid phone number {phone_number}')
        
def validate_numbers(numbers:str):
    if not numbers.isnumeric():
        raise ValidationError(_(f'{numbers} should contain only valid digits from 0 -> 9'))
    
def validate_percentage(percent):
    if percent < 0 or percent > 100:
        raise ValidationError(f'Invalid percent')