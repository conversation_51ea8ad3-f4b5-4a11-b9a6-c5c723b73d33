from rest_framework import serializers
from django.core.validators import EmailValidator

from billing.models import BankBillDuePayments
from common.models import (
    MobileApplicationVersion,
    State,
    LocalGovernment,
    TelecommunicationNetwork,
    AccessChannel,
    PersistPaystackPayments,
)
from .validators import validate_numbers


# from .models import NonTransactionError


class StateSerializer(serializers.ModelSerializer):
    class Meta:
        model = State
        fields = "__all__"


class AccessChannelSerializer(serializers.ModelSerializer):
    class Meta:
        model = AccessChannel
        fields = "__all__"


class LocalGovernmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = LocalGovernment
        fields = "__all__"


class TelecommunicationNetworkSerializer(serializers.ModelSerializer):
    class Meta:
        model = TelecommunicationNetwork
        fields = "__all__"


class AccountEnquirySerializer(serializers.Serializer):

    account_number = serializers.CharField(
        min_length=10, max_length=10, required=True, validators=[validate_numbers]
    )
    institution_code = serializers.Char<PERSON><PERSON>(min_length=3, max_length=15, required=True)
    sender_account_number = serializers.Char<PERSON>ield(
        min_length=10, max_length=10, validators=[validate_numbers], required=True
    )
    sender_institution_code = serializers.CharField(
        min_length=3, max_length=15, required=True
    )

    class Meta:
        fields = [
            "account_number",
            "institution_code",
            "sender_account_number",
            "sender_institution_code",
        ]

class MakePaystackPaymentSerializer(serializers.Serializer):

    email = serializers.CharField(validators=[EmailValidator(message='Please provide a valid email address.')])
    amount = serializers.CharField(min_length=1, required=True)
    
    class Meta:
        fields = [
            "email",
            "amount"
        ]

class BalanceEnquirySerializer(serializers.Serializer):
    account_number = serializers.CharField(
        min_length=10, max_length=10, required=True, validators=[validate_numbers]
    )
    institution_code = serializers.CharField(min_length=3, max_length=15, required=True)

    class Meta:
        fields = ["account_number", "institution_code"]


class ValidateBVNSerializer(serializers.Serializer):

    bvn = serializers.CharField(
        min_length=11, max_length=11, required=True, validators=[validate_numbers]
    )
    app_code = serializers.CharField(
        max_length=15, required=False, allow_null=True, allow_blank=True
    )

    class Meta:
        fields = ["bvn", "app_code"]


class ValidateUtilitySerializer(serializers.Serializer):

    customer_unique_number = serializers.CharField(min_length=1, required=True)
    biller_code = serializers.CharField(min_length=1, required=True)
    payment_code = serializers.CharField(min_length=1, required=True)

    class Meta:
        fields = ["customer_unique_number", "biller_code", "payment_code"]
        
class VerifyPayStackSerializer(serializers.Serializer):

    reference = serializers.CharField(min_length=1, required=True) 
    email = serializers.EmailField(required=True)
    amount = serializers.IntegerField(required=True)
    last_four_digits = serializers.CharField(max_length=4)
    class Meta:
        fields = ["reference", "email", "amount", "last_four_digits"]


class MobileAppSerializer(serializers.ModelSerializer):
    class Meta:
        model = MobileApplicationVersion
        fields = "__all__"
        
        
class PersistPaystackPaymentSerializer(serializers.ModelSerializer):
    
    bill_payment_ref = serializers.ListField(
        child=serializers.CharField(min_length=1), 
        min_length=1, required=False
    ) 
    payment_type = serializers.CharField(max_length=3)
    # last_four_digits = serializers.CharField(max_length=4, min_length=4)
    
    class Meta:
        model = PersistPaystackPayments
        fields = [
            "reference_id",
            "bank",
            "bill_payment_ref",
            "ledger_account_ref",
            "amount",
            "payment_type",
            "verified",
            "external_reference"
        ]
        
    def validate(self, attrs):
        """ 
        Validate the the payment reference for Bill payment exists and that 
        payment reference for Bill payment is not supplied if the payment type is Wallet Funding.
        """
        validated_data = super().validate(attrs)
        payment_type = validated_data.get("payment_type")
        bill_payment_ref = validated_data.get("bill_payment_ref")
        
        try:
            list_validated_bill = []
            
            if payment_type == "SUB":
                for bill in bill_payment_ref:
                    bill_obj = BankBillDuePayments.objects.get(
                        billing_reference=bill
                    )
                    if bill_obj:
                        list_validated_bill.append(bill)
                    elif BankBillDuePayments.DoesNotExist:
                        raise serializers.ValidationError(
                            f"Bill Reference `{bill}` Does Not Exists"
                        )
                    
            elif payment_type == "WF" and bill_payment_ref is not None:
                raise serializers.ValidationError(
                    "Selected Payment Type cannot have a value for `bill_payment_ref` field"
                    )
                
        except Exception as e:
            raise serializers.ValidationError(str(e))
        
        validated_data["bill_payment_ref"] = list_validated_bill
        return validated_data
    
    def create(self, validated_data):
        validated_data["bill_payment_ref"] = ",".join(validated_data["bill_payment_ref"])
        return super().create(validated_data)
    

class ManualReversalsSerializer(serializers.Serializer):

    user_ref = serializers.CharField(max_length=30, required=True)
    transaction_type = serializers.CharField(max_length=30, required=True)

    class Meta:
        fields = ["user_ref", "transaction_type"]
        
    def validate(self, attrs):
        validated_data = super().validate(attrs)
        transaction_type = validated_data.get("transaction_type")
        
        valid_transaction_types = ["AIRTIME/DATA", "BILL PAYMENT"]
        if transaction_type not in valid_transaction_types:
            raise serializers.ValidationError(
                "Invalid Transaction Type supplied. Should be one of; `AIRTIME/DATA`, `BILL PAYMENT`."
            )
        
        return validated_data

class GetPendingTransactionSerializer(serializers.Serializer):
   
    user_ref = serializers.SerializerMethodField("get_user_ref")
    transaction_type = serializers.SerializerMethodField("get_transaction_type")
    sender = serializers.SerializerMethodField("get_sender")
    sender_account = serializers.SerializerMethodField("get_sender_account")
    status = serializers.SerializerMethodField("get_status")
    amount = serializers.SerializerMethodField("get_amount")
    response_code = serializers.SerializerMethodField("get_response_code")
    response_description = serializers.SerializerMethodField("get_response_description")
    core_banking_ref = serializers.SerializerMethodField("get_core_banking_ref")
    agent = serializers.SerializerMethodField("get_agent")
    agent_account = serializers.SerializerMethodField("get_agent_account")
    date_created = serializers.SerializerMethodField("get_date_created")
    
    class Meta:
        fields = [
            "user_ref", "transaction_type", "sender", "sender_account", "status", "amount", "response_code", 
            "response_description", "agent", "agent_account", "core_banking_ref", "date_created"
        ]

    def get_user_ref(self, obj):
        return obj.content_object.user_ref

    def get_transaction_type(self, obj):
        return obj.content_object.transaction_type.name

    def get_sender(self, obj):
        if obj.content_object.transaction_type.name == "AIRTIME/DATA" and obj.content_object.sender == None:
            return None
        return obj.content_object.sender.customer.get_full_name()
    
    def get_sender_account(self, obj):
        if obj.content_object.transaction_type.name == "AIRTIME/DATA" and obj.content_object.sender_account == None:
            return None
        return obj.content_object.sender_account.account_number

    def get_status(self, obj):
        return obj.content_object.status

    def get_amount(self, obj):
        return obj.content_object.amount

    def get_response_code(self, obj):
        return obj.content_object.response_code

    def get_response_description(self, obj):
        return obj.content_object.response_description

    def get_core_banking_ref(self, obj):
        return obj.content_object.core_banking_ref

    def get_agent(self, obj):
        if obj.content_object.transaction_type.name == "BILL PAYMENT" or  obj.content_object.agent is None:
            return None
        return obj.content_object.agent.get_full_name()

    def get_agent_account(self, obj):
        if obj.content_object.transaction_type.name == "BILL PAYMENT" or obj.content_object.agent_account is None:
            return None
        return obj.content_object.agent_account

    def get_date_created(self, obj):
        return obj.content_object.date_created
    