from decimal import Decimal
import logging

from threading import Thread
from rest_framework.response import Response
from rest_framework import generics, status
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import AllowAny
from .models import ChannelAirtimeData, TerminalData
from transactions.models import SendMoney, Transaction
from transactions.utils import get_transaction_details
from transactions.serializers import TransactionSummarySerializer
from channel_services.serializers import (
    ChannelAirtimeDataSerializer,
    ChannelPayBillsSerializer,
    ChannelSendMoneySerializer,
    ChannelSendSMSSerialializer,
    RetrieveBalanceEnquirySerializer,
)
from .functions import process_transaction
from common import services
from common.functions import (
    account_settlement_reversal,
    fund_settlement_account,
    get_fee,
    get_reference,
    get_serializer_key_error,
    get_transact_query_type,
    reciever_details_available,
    retrieve_reciever_details,
    set_response_data,
    set_bvn_dob,
    create_bvn_record,
    get_platform,
    get_client_credentials,
    get_transaction_status,
)
from bank.models import Bank
from common.models import BillsPaymentAdviceClass, SendMoneyClass, SettlementAccount, BVN, CurrencyType

from common.permissions import IsThirdParty
from common.serializers import AccountEnquirySerializer
# from logic_omnichannel import settings
from django.conf import settings

from django.db.models import Q

# Create your views here.

class RetrieveBillPaymentItems(generics.GenericAPIView):

    permission_classes = (IsThirdParty,)

    def get(self, request, biller_code):
        try:
            # get package option for chosen bill provider
            package_options = services.get_package_options(biller_code)
            if package_options.get("response_code") == settings.SUCCESS_RESPONSE:
                package_options = package_options["response_detail"]["package_options"]
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Bill Payment Items Retrieved Successfully",
                    detail=package_options,
                )
                return Response(data, status=status.HTTP_200_OK)

            return Response(package_options, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Complete Retrieve Bill Payment Items",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class RetrieveBalanceEnquiry(generics.GenericAPIView):

    permission_classes = (IsThirdParty,)
    serializer_class = RetrieveBalanceEnquirySerializer

    def post(self, request):
        try:
            """
            try to get balance enquiry for third party services from the core banking
            this could be any kind of core banking the omni channel is connected to
            """
            serializer = RetrieveBalanceEnquirySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            account_number = serializer.validated_data.get("account_number")
            code = serializer.validated_data.get("code")
            core_code = serializer.validated_data.get("core_code")

            if core_code:
                # TODO get the core banking engine this engine is trying to talk to
                pass

            # default core banking is nambuit core so we call that if core_code is not provided
            bank = Bank.objects.get(code=code)
            resp = services.get_balance_enquiry(
                bank, account_number, code, app_code=bank.alias
            )
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Balance Enquiry Request Successful",
                    detail=resp.get("response_description"),
                )
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Error Calling the Balance Enquiry Service",
                    detail=resp.get("response_description"),
                )
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Bank.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Code, Unable to retieve corresponding Bank",
                detail="Bank does not exist for code",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Retrieving Balance Enquiry, Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class RetrieveNameEnquiry(generics.GenericAPIView):

    permission_classes = (IsThirdParty,)
    serializer_class = AccountEnquirySerializer

    def post(self, request):
        log = logging.getLogger("django")

        try:
            serializer = AccountEnquirySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            account_number = serializer.validated_data["account_number"]
            destination_institution_code = serializer.validated_data["institution_code"]
            sender_intitution_code = serializer.validated_data[
                "sender_institution_code"
            ]
            sender_account = serializer.validated_data["sender_account_number"]

            sender_bank = Bank.objects.filter(
                Q(nip_code=sender_intitution_code) | Q(code=sender_intitution_code)
            )
            destination_bank = Bank.objects.filter(
                Q(nip_code=destination_institution_code)
                | Q(code=destination_institution_code)
            )
            if sender_bank:
                sender_bank = sender_bank[0]
            else:
                raise ValidationError(
                    {"sender_institution_code": "Sender Institution Code Invalid"}
                )
            if destination_bank:
                destination_bank = destination_bank[0]
            else:
                raise ValidationError(
                    {"institution_code": "Account Institution Code Invalid"}
                )

            name_enq_type = get_transact_query_type(sender_bank, destination_bank)

            if sender_bank.belong_to_mifos_wallet_service:
                name_enq = services.get_wallet_name_enquiry(
                    account_number, destination_bank, name_enq_type
                )
            else:
                name_enq = services.get_name_enquiry(
                    account_number,
                    destination_institution_code,
                    sender_account,
                    sender_intitution_code,
                    type=name_enq_type,
                    app_code=sender_bank.alias,
                )

            if name_enq["response_code"] == settings.SUCCESS_RESPONSE:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Name Enquiry Successful",
                    "response_detail": {
                        "account_name": name_enq["response_description"][
                            "account_name"
                        ],
                        "account_number": account_number,
                        "name_enquiry_ref": name_enq["response_description"][
                            "name_enquiry_ref"
                        ],
                        "bvn": name_enq.get("response_description", {}).get(
                            "bvn", None
                        ),
                        "phone_number": name_enq.get("response_description", {}).get(
                            "phone_number", None
                        ),
                    },
                }
                log.info(f"Exiting NAME ENQUIRY API with {data}")
                return Response(data, status=status.HTTP_200_OK)
            elif name_enq["response_code"] == settings.FAILED_RESPONSE:
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_description": "Name Enquiry Failed",
                    "response_detail": name_enq["response_description"],
                }
                log.info(f"Exiting NAME ENQUIRY API with {data}")
                return Response(data, status=status.HTTP_200_OK)

            raise Exception("Name Enquiry Failed")
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Request Payload",
                "response_detail": serializer.errors,
            }
            log.info(f"Exiting NAME ENQUIRY API with {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Switch Error",
                "response_detail": str(e),
            }
            log.info(f"Exiting NAME ENQUIRY API with {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ChannelSendMoneyAPI(generics.GenericAPIView):

    permission_classes = (IsThirdParty,)
    serializer_class = ChannelSendMoneySerializer

    def post(self, request):
        log = logging.getLogger("django")
        

        try:
            log.info(
                f"Entering Third Party Send Money Flow with request payload {request.data}"
            )
            serializer = ChannelSendMoneySerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            client_id, client_secret = get_client_credentials(request)

            channel = get_platform(client_id=client_id, client_secret=client_secret)
            user_ref = serializer.validated_data.get("user_ref")
            amount = serializer.validated_data.get("amount")
            sender_account = serializer.validated_data.get("sender_account")
            sender_institution_code = serializer.validated_data.get("sender_institution_code")
            destination_account = serializer.validated_data.get("destination_account")
            destination_institution_code = serializer.validated_data.get("destination_institution_code")
            destination_account_name = serializer.validated_data.get("destination_account_name")
            remark = serializer.validated_data.get("destination_account_name")

            sender_bank = Bank.objects.filter(
                Q(nip_code=sender_institution_code) | Q(code=sender_institution_code)
            )
            
            if sender_bank:
                    sender_bank = sender_bank[0]
            else:
                raise Exception("Sender Bank Does Not Exist")
            
            destination_bank = Bank.objects.filter(
                Q(nip_code=destination_institution_code)
                | Q(code=destination_institution_code)
            )

            if destination_bank:
                destination_bank = destination_bank[0]
            else:
                raise Exception("Destination Bank Does Not Exist")
            
            currency = CurrencyType.objects.filter(symbol="Naira").first()
            if not currency:
                currency = None
            send_money_type = get_transact_query_type(sender_bank, destination_bank)

            if send_money_type == "INTRA":
                destination_institution_code = sender_institution_code
            elif send_money_type == "INTER":
                destination_institution_code = destination_bank.nip_code
                if (
                    not reciever_details_available(sender_account)
                    or retrieve_reciever_details(sender_account).get("reciever_account")
                    != destination_account
                ):
                    # check if reciever details is not stored locally or if the currently stored details is invalid
                    # we run name enquiry again to populate our local dynamic memory
                    # NOTE: RUN THIS ASYNCRONOUSLY in future
                    services.get_name_enquiry(
                        account_number=destination_account,
                        institution_code=destination_institution_code,
                        sender_account_number=sender_account,
                        sender_institution_code=sender_institution_code,
                        type="INTER",
                        app_code=sender_bank.alias,
                    )
                # for INTER bank transactions we calculate the fee for omni channel
                fee = get_fee(
                    amount=Decimal(amount),
                    transaction_type="send_money",
                    partner_bank=sender_bank,
                )

            payload_class = SendMoneyClass()
            payload_class.send_money_type = send_money_type
            payload_class.sender_account = sender_account
            payload_class.sender_institution_code = sender_institution_code
            payload_class.destination_account = destination_account
            payload_class.destination_institution_code = destination_institution_code
            payload_class.amount = amount
            payload_class.narration = f"CHANNEL TRF TO {destination_account}"
            payload_class.transaction_ref = get_reference()

            transaction = SendMoney.objects.create(
                user_ref=user_ref,
                amount=amount,
                channel=channel,
                currency=currency,
                sender_account_third_party=sender_account,
                destination_account_number=destination_account,
                destination_institution_code=destination_institution_code,
                destination_account_name=destination_account_name,
                core_banking_ref=payload_class.transaction_ref,
                remark=remark,
                fee=fee if send_money_type == "INTER" else Decimal("0.00"),
                is_transaction_chargable=True
                    if send_money_type == "INTER"
                    else False,
                is_resolved=False
                    if send_money_type == "INTER"
                    else True
            )

            # Call the send money service
            resp = services.send_money(payload_class, sender_bank.alias)
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                response_detail = resp.get("response_detail", {})

                transaction.nam_payment_reference = response_detail.get('nam_payment_reference')
                transaction.status = "S"
                transaction.response_code = settings.SUCCESS_RESPONSE
                transaction.response_description = 'CHANNEL SEND MONEY SUCCESSFUL'
                transaction.save()

                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='CHANNEL SEND MONEY REQUEST SUCCESSFUL',
                    detail={
                        'transaction_reference': transaction.transaction_ref,
                        'user_ref': transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code,
                        "details": get_transaction_details(transaction)
                    }
                )
                log.info(f'exiting CHANNEL SEND MONEY API with: {data}')
                return Response(data, status=status.HTTP_200_OK)
            # Unsuccessful scenario ...
            
            else:
                response_detail = resp.get("response_detail", {})
                transaction.nam_payment_reference = response_detail.get('nam_payment_reference')
                transaction.status= "P" if resp.get("response_code") == "02" else "F"
                transaction.response_code = resp.get("response_code") or settings.FAILED_RESPONSE
                transaction.response_description = resp.get("response_description")
                transaction.save()

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get("response_description")
                    or "Unable to Complete Transaction",
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                        "tsq_type": transaction.transaction_type.code
                    },
                )

                log.info(f'exiting CHANNEL SEND MONEY API with: {data}')
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='INVALID REQEUST PAYLOAD',
                detail=serializer.errors
            )
            log.info(f'exiting CHANNEL SEND MONEY API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='ERROR SENDING MONEY',
                detail=str(e)
            )
            log.info(f'exiting CHANNEL SEND MONEY API with: {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)





# class ChannelSendMoney(generics.GenericAPIView):

#     permission_classes = (IsThirdParty,)
#     serializer_class = ChannelSendMoneySerializer

#     def post(self, request):
#         log = logging.getLogger("django")
#         try:
#             """
#             Send Money action from third party service through omni channel
#             """
#             log.info(
#                 f"Entering Third Party Send Money Flow with request payload {request.data}"
#             )
#             serializer = ChannelSendMoneySerializer(data=request.data)
#             serializer.is_valid(raise_exception=True)

#             amount = serializer.validated_data.get("amount")
#             sender_account = serializer.validated_data.get("sender_account")
#             sender_institution_code = serializer.validated_data.get(
#                 "sender_institution_code"
#             )
#             destination_account = serializer.validated_data.get("destination_account")
#             destination_institution_code = serializer.validated_data.get(
#                 "destination_institution_code"
#             )

#             sender_bank = Bank.objects.filter(
#                 Q(nip_code=sender_institution_code) | Q(code=sender_institution_code)
#             )

#             if sender_bank:
#                 sender_bank = sender_bank[0]
#             else:
#                 raise Exception("Sender Bank Does Not Exist")

#             destination_bank = Bank.objects.filter(
#                 Q(nip_code=destination_institution_code)
#                 | Q(code=destination_institution_code)
#             )

#             if destination_bank:
#                 destination_bank = destination_bank[0]
#             else:
#                 raise Exception("Destination Bank Does Not Exist")

#             send_money_type = get_transact_query_type(sender_bank, destination_bank)

#             if send_money_type == "INTRA":
#                 destination_institution_code = sender_institution_code
#             elif send_money_type == "INTER":
#                 destination_institution_code = destination_bank.nip_code
#                 if (
#                     not reciever_details_available(sender_account)
#                     or retrieve_reciever_details(sender_account).get("reciever_account")
#                     != destination_account
#                 ):
#                     # check if reciever details is not stored locally or if the currently stored details is invalid
#                     # we run name enquiry again to populate our local dynamic memory
#                     # NOTE: RUN THIS ASYNCRONOUSLY in future
#                     services.get_name_enquiry(
#                         account_number=destination_account,
#                         institution_code=destination_institution_code,
#                         sender_account_number=sender_account.account_number,
#                         sender_institution_code=sender_institution_code,
#                         type="INTER",
#                         app_code=sender_bank.alias,
#                     )
#                 # for INTER bank transactions we calculate the fee for omni channel
#                 fee = get_fee(
#                     amount=Decimal(amount),
#                     transaction_type="send_money",
#                     partner_bank=sender_bank,
#                 )

#             # create send money payload class
#             payload_class = SendMoneyClass()
#             payload_class.send_money_type = send_money_type
#             payload_class.sender_account = sender_account
#             payload_class.sender_institution_code = sender_institution_code
#             payload_class.destination_account = destination_account
#             payload_class.destination_institution_code = destination_institution_code
#             payload_class.amount = amount
#             payload_class.narration = f"CHANNEL TRF TO {destination_account}"
#             payload_class.transaction_ref = get_reference()

#             # Call the send money service
#             resp = services.send_money(payload_class, sender_bank.alias)
#             if resp.get("response_code") == settings.SUCCESS_RESPONSE:
#                 response_detail = resp.get("response_detail", {})
#                 transaction = serializer.save(
#                     response_code=settings.SUCCESS_RESPONSE,
#                     response_description="SEND MONEY SUCCESSFUL",
#                     status="S",
#                     nam_payment_reference=response_detail.get("nam_payment_reference"),
#                     send_money_type=send_money_type,
#                     core_banking_ref=payload_class.transaction_ref,
#                     fee=fee if send_money_type == "INTER" else Decimal("0.00"),
#                     is_resolved=False if send_money_type == "INTER" else True,
#                     is_transaction_chargable=True
#                     if send_money_type == "INTER"
#                     else False,
#                 )
#                 data = set_response_data(
#                     code=settings.SUCCESS_RESPONSE,
#                     description="SEND MONEY REQUEST SUCCESSFUL",
#                     detail={
#                         "transaction_reference": transaction.transaction_ref,
#                         "user_ref": transaction.user_ref,
#                     },
#                 )
#                 log.info(f"exiting Third Party Send Money API with: {data}")
#                 return Response(data, status=status.HTTP_200_OK)
#             else:
#                 transaction = serializer.save(
#                     response_code=resp.get("response_code") or settings.FAILED_RESPONSE,
#                     response_description=resp.get("response_description")
#                     or "Unable to Complete Transaction",
#                     status="P" if resp.get("response_code") == "02" else "F",
#                     fee=fee if send_money_type == "INTER" else Decimal("0.00"),
#                     core_banking_ref=payload_class.transaction_ref,
#                 )
#                 data = set_response_data(
#                     code=settings.FAILED_RESPONSE,
#                     description=resp.get("response_description")
#                     or "Unable to Complete Transaction",
#                     detail={
#                         "transaction_reference": transaction.transaction_ref,
#                         "user_ref": transaction.user_ref,
#                     },
#                 )
#                 log.info(f"exiting Third Party Send Money API with: {data}")
#                 return Response(resp, status=status.HTTP_400_BAD_REQUEST)
#         except ValidationError:
#             data = set_response_data(
#                 code=settings.FAILED_RESPONSE,
#                 description="Invalid Request Payload",
#                 detail=get_serializer_key_error(serializer.errors),
#             )
#             log.info(f"Error From Third Party Send Money Flow -> {data}")
#             return Response(data, status=status.HTTP_400_BAD_REQUEST)
#         except Exception as e:
#             data = set_response_data(
#                 code=settings.FAILED_RESPONSE,
#                 description=f"Error Sending Money, Error -> {e}",
#                 detail=str(e),
#             )
#             log.info(f"Error From Third Party Send Money Flow -> {data}")
#             return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ChannelAirtimeData(generics.GenericAPIView):
    permission_classes = (IsThirdParty,)
    serializer_class = ChannelAirtimeDataSerializer

    def post(self, request):
        log = logging.getLogger("django")
        try:
            """
            Airtime Data Action for Third Party Service Through Omni Channel
            """
            log.info(
                f"Entering Third Party Airtime Data Flow with request payload {request.data}"
            )
            serializer = ChannelAirtimeDataSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            amount = serializer.validated_data.get("amount")
            sender_account = serializer.validated_data.get("sender_account")
            sender_email = serializer.validated_data.get("sender_email")
            sender_institution_code = serializer.validated_data.get(
                "sender_institution_code"
            )
            phone_number = serializer.validated_data.get("phone_number")
            payment_code = serializer.validated_data.get("payment_code")

            sender_bank = Bank.objects.filter(
                Q(nip_code=sender_institution_code) | Q(code=sender_institution_code)
            )

            if sender_bank:
                sender_bank = sender_bank[0]
            else:
                raise Exception("Sender Bank Does Not Exist")

            fee = get_fee(
                amount=Decimal(amount),
                transaction_type="airtime_data",
                partner_bank=sender_bank,
            )
            settlement_account = SettlementAccount.objects.get(
                transaction_type__code="airtime_data", bank=sender_bank
            )
            resp = fund_settlement_account(
                sender_account,
                amount,
                settlement_account,
                sender_bank.alias,
                services.send_money,
            )
            if resp.get("response_code") == settings.FAILED_RESPONSE:
                # possibly the user does not have enough money in his account or there is issue with the core banking
                serializer.save(
                    status="F",
                    response_code=settings.FAILED_RESPONSE,
                    response_description="UNABLE TO FUND SETTLEMENT ACCOUNT",
                    core_banking_ref=resp.get("core_banking_ref"),
                )

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail=resp.get(
                        "response_detail", "Unable To Complete Transactions"
                    ),
                )
                log.info(f"exiting Third Party Airtime Data Flow with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            transaction = serializer.save(
                status="P",
                response_code="02",  # 02 here represents this unique state
                response_description="SETTLEMENT ACCOUNT FUNDED SUCCESSFULLY",
                core_banking_ref=resp.get("core_banking_ref"),
                nam_payment_reference=resp.get("nam_payment_ref"),
            )

            payload_class = BillsPaymentAdviceClass()
            payload_class.amount = amount
            payload_class.sender_email = sender_email
            payload_class.sender_mobile = phone_number
            payload_class.hash_value = "************"
            payload_class.institution_code = "000001"
            payload_class.payment_code = payment_code
            payload_class.request_id = resp.get("core_banking_ref")
            payload_class.billing_unique_number = phone_number

            resp = services.bill_payment_advice(payload_class, "airtime_data")
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                # Airtime-Data Transaction is successful, at this point we save the success state to the db
                transaction.response_code = settings.SUCCESS_RESPONSE
                transaction.response_description = "AIRTIME/DATA SUCCESSFUL"
                transaction.status = "S"
                transaction.fee = fee
                transaction.is_resolved = False
                transaction.is_transaction_chargable = True
                transaction.switch_ref = resp.get("response_detail", {}).get(
                    "switch_ref", ""
                )
                transaction.save()
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="AIRTIME/DATA PAYMENT REQUEST SUCCESSFUL",
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                    },
                )
                log.info(f"exiting Third Party Airtime Data Flow with: {data}")
                return Response(data, status=status.HTTP_200_OK)
            else:
                transaction.response_description = (
                    "POSSIBLE AIRTIME/DATA FAILURE, PENDING REVERSAL"
                )
                transaction.fee = fee
                transaction.switch_ref = resp.get("response_detail", {}).get(
                    "switch_ref", ""
                )
                if resp.get("response_code") != "02":
                    log.info(
                        "FROM Third Party Airtime Data Flow -> Calling For Transaction Reversal"
                    )
                    transaction.response_code = settings.FAILED_RESPONSE
                    Thread(
                        target=account_settlement_reversal,
                        args=(
                            sender_account,
                            amount,
                            settlement_account,
                            sender_bank.alias,
                            transaction,
                            services.send_money,
                        ),
                    ).start()

                transaction.save()

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                    },
                )
                log.info(f"exiting Third Party Airtime Data Flow with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Request Payload",
                detail=get_serializer_key_error(serializer.errors),
            )
            log.info(f"Error From Third Party Airtime Data Flow -> {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Processing Airtime Data Transaction, Error -> {e}",
                detail=str(e),
            )
            log.info(f"Error From Third Party Airtime Data Flow -> {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ChannelPayBills(generics.GenericAPIView):
    permission_classes = (IsThirdParty,)
    serializer_class = ChannelPayBillsSerializer

    def post(self, request):
        log = logging.getLogger("django")
        try:
            """
            Pay Bills Action for Third Party Service Through Omni Channel
            """
            log.info(
                f"Entering Third Party Pay Bills Flow with request payload {request.data}"
            )
            serializer = ChannelPayBillsSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            amount = serializer.validated_data.get("amount")
            sender_account = serializer.validated_data.get("sender_account")
            sender_email = serializer.validated_data.get("sender_email")
            sender_institution_code = serializer.validated_data.get(
                "sender_institution_code"
            )
            phone_number = serializer.validated_data.get("phone_number")
            payment_code = serializer.validated_data.get("payment_code")
            customer_unique_number = serializer.validated_data.get(
                "customer_unique_number"
            )

            sender_bank = Bank.objects.filter(
                Q(nip_code=sender_institution_code) | Q(code=sender_institution_code)
            )

            if sender_bank:
                sender_bank = sender_bank[0]
            else:
                raise Exception("Sender Bank Does Not Exist")

            fee = get_fee(
                amount=Decimal(amount),
                transaction_type="bill_payment",
                partner_bank=sender_bank,
            )
            settlement_account = SettlementAccount.objects.get(
                transaction_type__code="bill_payment", bank=sender_bank
            )
            resp = fund_settlement_account(
                sender_account,
                amount,
                settlement_account,
                sender_bank.alias,
                services.send_money,
            )
            if resp.get("response_code") == settings.FAILED_RESPONSE:
                # possibly the user does not have enough money in his account or there is issue with the core banking
                serializer.save(
                    status="F",
                    response_code=settings.FAILED_RESPONSE,
                    response_description="UNABLE TO FUND SETTLEMENT ACCOUNT",
                    core_banking_ref=resp.get("core_banking_ref"),
                )

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail=resp.get(
                        "response_detail", "Unable To Complete Transactions"
                    ),
                )
                log.info(f"exiting Third Party Bill Payment Flow with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            transaction = serializer.save(
                status="P",
                response_code="02",  # 02 here represents this unique state
                response_description="SETTLEMENT ACCOUNT FUNDED SUCCESSFULLY",
                core_banking_ref=resp.get("core_banking_ref"),
                nam_payment_reference=resp.get("nam_payment_ref"),
            )

            payload_class = BillsPaymentAdviceClass()
            payload_class.amount = amount
            payload_class.sender_email = sender_email
            payload_class.sender_mobile = phone_number
            payload_class.hash_value = "************"
            payload_class.institution_code = "000001"
            payload_class.payment_code = payment_code
            payload_class.request_id = resp.get("core_banking_ref")
            payload_class.billing_unique_number = customer_unique_number

            resp = services.bill_payment_advice(payload_class, "bill_payment")
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                # Airtime-Data Transaction is successful, at this point we save the success state to the db
                transaction.response_code = settings.SUCCESS_RESPONSE
                transaction.response_description = "BILL PAYMENT SUCCESSFUL"
                transaction.status = "S"
                transaction.fee = fee
                transaction.is_resolved = False
                transaction.is_transaction_chargable = True
                transaction.switch_ref = resp.get("response_detail", {}).get(
                    "switch_ref", ""
                )
                transaction.save()
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="BILL PAYMENT REQUEST SUCCESSFUL",
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                    },
                )
                log.info(f"exiting Third Party PAY BILLS Flow with: {data}")
                return Response(data, status=status.HTTP_200_OK)
            else:
                transaction.response_description = (
                    "POSSIBLE PAY BILLS FAILURE, PENDING REVERSAL"
                )
                transaction.fee = fee
                transaction.switch_ref = resp.get("response_detail", {}).get(
                    "switch_ref", ""
                )
                if resp.get("response_code") != "02":
                    log.info(
                        "FROM Third Party PAY BILLS Flow -> Calling For Transaction Reversal"
                    )
                    transaction.response_code = settings.FAILED_RESPONSE
                    Thread(
                        target=account_settlement_reversal,
                        args=(
                            sender_account,
                            amount,
                            settlement_account,
                            sender_bank.alias,
                            transaction,
                            services.send_money,
                        ),
                    ).start()

                transaction.save()

                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=resp.get(
                        "response_description", "Unable To Complete Transaction"
                    ),
                    detail={
                        "transaction_reference": transaction.transaction_ref,
                        "user_ref": transaction.user_ref,
                    },
                )
                log.info(f"exiting Third Party PAY BILLS Flow with: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Request Payload",
                detail=get_serializer_key_error(serializer.errors),
            )
            log.info(f"Error From Third Party PAY BILLS Flow -> {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Processing PAY BILLS Transaction, Error -> {e}",
                detail=str(e),
            )
            log.info(f"Error From Third Party PAY BILLS Flow -> {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ChannelSendSMS(generics.GenericAPIView):

    permission_classes = (IsThirdParty,)
    serializer_class = ChannelSendSMSSerialializer

    def post(self, request):
        log = logging.getLogger("django")
        try:
            serializer = ChannelSendSMSSerialializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            code = serializer.validated_data.get("code")
            text = serializer.validated_data.get("text")
            phone_number = serializer.validated_data.get("phone_number")

            bank = Bank.objects.filter(Q(nip_code=code) | Q(code=code))
            if bank:
                bank = bank[0]
            else:
                raise Exception("Institution Does Not Exist")

            resp = services.send_sms(
                phone_number, text, bank.code, bank.message_sender_name, get_reference()
            )
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                return Response(resp, status=status.HTTP_200_OK)
            return Response(resp, status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Sending SMS -> {e}",
                detail=str(e),
            )
            log.info(f"Error From Third Party Send SMS Flow -> {data}")


class TerminalNotification(generics.GenericAPIView):

    permission_classes = (AllowAny,)

    def post(self, request):
        """
        Retrieve Terminal Notification data and saves to the database
        """
        log = logging.getLogger("django")
        log.info(f"Terminal Notifcation Payload: {request.data}")
        state = request.data.get("terminalInformation", {}).get("state")
        ejournal = (
            request.data.get("terminalInformation", {})
            .get("ejournals", {})
            .get("ejournal", {})
        )

        if state and ejournal:
            for count, transaction in enumerate(ejournal):
                data = TerminalData.objects.create(
                    state=state, ejournal=ejournal, transaction=transaction
                )
                Thread(target=process_transaction, args=(data,)).start()
                log.info(f"{count + 1} Notification logged successfully")

        return Response(
            data={"status": "successful", "description": "successfully logged"},
            status=status.HTTP_200_OK,
        )
    

class ChannelsBVNValidation(generics.GenericAPIView):
    permission_classes = (IsThirdParty,)

    def post(self, request, bvn):

        try:

            log = logging.getLogger("django")

            try:
                bvn_enq = BVN.objects.get(bvn=bvn)
                bvn_exists_locally = True
                bvn = bvn_enq.bvn
                phone_number = bvn_enq.phone_number
                first_name = bvn_enq.first_name
                last_name = bvn_enq.last_name
                dob = bvn_enq.dob
                formatted_dob = bvn_enq.formatted_dob
            except BVN.DoesNotExist:
                bvn_exists_locally = False
                # partner_bank = Bank.objects.get(alias=app_code)
                bvn_enq = services.validate_bvn(bvn)
                log.info(
                    f"FROM CHANNEL SERVICES BVN API: Response from bvn verification service {bvn_enq}"
                )
                if bvn_enq["response_code"] == settings.SUCCESS_RESPONSE:
                    bvn = bvn_enq["response_description"]["bvn"]
                    phone_number = bvn_enq["response_description"]["mobile"]
                    first_name = bvn_enq["response_description"]["first_name"]
                    last_name = bvn_enq["response_description"]["last_name"]
                    dob = bvn_enq["response_description"]["dob"]
                    formatted_dob = bvn_enq["response_description"]["formatted_dob"]
                    gender = bvn_enq.get("response_description", {}).get("gender", "")
                    residential_address = bvn_enq.get("response_description", {}).get(
                        "residential_address", )
                else:
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="Error Resolving BVN",
                        detail=bvn_enq.get("response_description"),
                    )
                    log.info(
                        f"FROM BVN API: FAILED RESPONSE from bvn verification service"
                    )
                    log.info(f"exiting BVN API with: {data}")
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
                
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "BVN is Valid",
                "response_detail": {
                    "bvn": bvn,
                    "phone_number": phone_number,
                    "first_name": first_name,
                    "last_name": last_name,
                    "dob": formatted_dob,
                },
            }
            
            if not bvn_exists_locally:
                log.info(
                    f"FROM CHANNEL SERVICES BVN API: SPINNING UP THREAD to save bvn details locally"
                )
                Thread(
                    target=create_bvn_record,
                    args=(
                        bvn,
                        phone_number,
                        first_name,
                        last_name,
                        dob,
                        formatted_dob,
                        gender,
                        residential_address,
                    ),
                ).start()
            # store dob in internal local memory for customer profile creation in user creation to pick from
            set_bvn_dob(bvn, formatted_dob)
            log.info(f"exiting  CHANNEL SERVICES BVN API with: {data}")
            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Switch Error",
                "response_detail": str(e),
            }
            log.info(f"exiting BVN API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        

class ChannelGetTransationStatus(generics.GenericAPIView):

    permission_classes = (IsThirdParty,) # TEST BALANCE ENQ WORKS TOO, BEFORE PUSHING ..

    def get(self, request, user_ref: str, tsq_type: str):
        try:
            """
            """
            transaction_type = tsq_type.lower()

            if transaction_type == "send_money":
                transaction = Transaction.objects.get(
                    send_money__user_ref=user_ref
                )
                # RESTRICT TO ONLY THE THIRD PARTY MAKING THE REQUEST.
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"Invalid Transaction Type {transaction_type}",
                    detail=f"Invalid Transaction Type {transaction_type}",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            transaction_status = get_transaction_status(
                transaction, transaction_type, services.send_money, services.bill_payment_advice_get_tsq
            )
            if transaction_status:
                code, status_desc = settings.TRANSACTION_STATUS_CODE.get(
                    transaction_status, (None, None)
                )
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Get Transaction Status Request Successful",
                    detail={
                        "status": status_desc,
                        "transaction_summary": TransactionSummarySerializer(
                            transaction
                        ).data,
                    },
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable to retrieve transaction status at the moment, Please try later",
                    detail="Unable to retrieve transaction status at the moment, Please try later",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Transaction.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Transaction with user_ref {user_ref} Does Not Exist for this user",
                detail=f"Transaction with user_ref {user_ref} Does Not Exist for this user",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Getting Transaction Status",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        