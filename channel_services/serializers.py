from rest_framework import serializers

from common.validators import validate_numbers, validate_phone_number

from .models import ChannelAirtimeData, ChannelPayBills, ChannelSendMoney


class RetrieveBalanceEnquirySerializer(serializers.Serializer):

    account_number = serializers.CharField(
        min_length=10, max_length=10, required=True, validators=[validate_numbers]
    )
    code = serializers.CharField(min_length=3, max_length=15, required=True)
    core_code = serializers.CharField(max_length=5, allow_blank=True, required=False)

    class Meta:
        fields = "__all__"


# class ChannelSendMoneySerializer(serializers.ModelSerializer):
#     class Meta:
#         model = ChannelSendMoney
#         fields = [
#             "user_ref",
#             "amount",
#             "sender_account",
#             "sender_institution_code",
#             "destination_account",
#             "destination_institution_code",
#             "remark",
#         ]

class ChannelSendMoneySerializer(serializers.Serializer):

    user_ref = serializers.<PERSON><PERSON><PERSON><PERSON>(max_length=30)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    sender_account = serializers.Char<PERSON><PERSON>(max_length=10)
    sender_institution_code = serializers.Char<PERSON><PERSON>(max_length=10)
    destination_account = serializers.CharField(max_length=10)
    destination_institution_code = serializers.CharField(max_length=10)
    destination_account_name = serializers.CharField(max_length=100)
    remark = serializers.CharField(max_length=250, required=False)


    class Meta:
        fields = [
            "user_ref",
            "amount",
            "sender_account",
            "sender_institution_code",
            "destination_account",
            "destination_institution_code",
            "destination_account_name",
            "remark",
        ]


class ChannelAirtimeDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChannelAirtimeData
        fields = [
            "user_ref",
            "amount",
            "sender_account",
            "sender_email",
            "sender_institution_code",
            "phone_number",
            "payment_code",
        ]


class ChannelPayBillsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChannelPayBills
        fields = [
            "user_ref",
            "amount",
            "sender_account",
            "sender_email",
            "sender_institution_code",
            "phone_number",
            "payment_code",
            "customer_unique_number",
        ]


class ChannelSendSMSSerialializer(serializers.Serializer):

    code = serializers.CharField(min_length=3, max_length=15, required=True)
    text = serializers.CharField(max_length=250, required=True)
    phone_number = serializers.CharField(
        max_length=16, required=True, validators=[validate_phone_number]
    )

    class Meta:
        fields = "__all__"
