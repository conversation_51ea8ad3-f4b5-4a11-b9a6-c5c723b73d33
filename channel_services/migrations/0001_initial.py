# Generated by Django 3.2.3 on 2024-04-10 22:51

import common.validators
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ChannelAirtimeData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ref', models.CharField(max_length=60, unique=True)),
                ('transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('sender_account', models.Char<PERSON>ield(max_length=10)),
                ('sender_email', models.EmailField(max_length=254)),
                ('sender_institution_code', models.Char<PERSON>ield(max_length=10)),
                ('phone_number', models.Char<PERSON><PERSON>(max_length=16, validators=[common.validators.validate_phone_number])),
                ('payment_code', models.Char<PERSON>ield(max_length=10)),
                ('core_banking_ref', models.CharField(blank=True, db_index=True, max_length=25, null=True)),
                ('nam_payment_reference', models.CharField(blank=True, max_length=30, null=True)),
                ('switch_ref', models.CharField(blank=True, max_length=30, null=True)),
                ('response_code', models.CharField(blank=True, max_length=5)),
                ('response_description', models.CharField(blank=True, max_length=250)),
                ('is_resolved', models.BooleanField(default=True)),
                ('bill_ref', models.CharField(blank=True, max_length=25, null=True)),
                ('is_transaction_chargable', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('R', 'Reversed'), ('F', 'Failed'), ('P', 'Pending'), ('S', 'Successful'), ('I', 'Invalid')], default='P', max_length=1)),
            ],
        ),
        migrations.CreateModel(
            name='ChannelPayBills',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ref', models.CharField(max_length=60, unique=True)),
                ('transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('sender_account', models.CharField(max_length=10)),
                ('sender_email', models.EmailField(max_length=254)),
                ('sender_institution_code', models.CharField(max_length=10)),
                ('phone_number', models.CharField(max_length=16, validators=[common.validators.validate_phone_number])),
                ('customer_unique_number', models.CharField(max_length=100)),
                ('payment_code', models.CharField(max_length=10)),
                ('core_banking_ref', models.CharField(blank=True, db_index=True, max_length=25, null=True)),
                ('nam_payment_reference', models.CharField(blank=True, max_length=30, null=True)),
                ('switch_ref', models.CharField(blank=True, max_length=30, null=True)),
                ('response_code', models.CharField(blank=True, max_length=5)),
                ('response_description', models.CharField(blank=True, max_length=250)),
                ('is_resolved', models.BooleanField(default=True)),
                ('bill_ref', models.CharField(blank=True, max_length=25, null=True)),
                ('is_transaction_chargable', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('R', 'Reversed'), ('F', 'Failed'), ('P', 'Pending'), ('S', 'Successful'), ('I', 'Invalid')], default='P', max_length=1)),
            ],
        ),
        migrations.CreateModel(
            name='ChannelSendMoney',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_ref', models.CharField(max_length=60, unique=True)),
                ('transaction_ref', models.UUIDField(db_index=True, default=uuid.uuid4, unique=True)),
                ('send_money_type', models.CharField(choices=[('intra', 'Intra'), ('inter', 'Inter')], max_length=5)),
                ('amount', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('sender_account', models.CharField(max_length=10)),
                ('sender_institution_code', models.CharField(max_length=10)),
                ('destination_account', models.CharField(max_length=10)),
                ('destination_institution_code', models.CharField(max_length=10)),
                ('fee', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('remark', models.TextField()),
                ('core_banking_ref', models.CharField(blank=True, db_index=True, max_length=25, null=True)),
                ('nam_payment_reference', models.CharField(blank=True, max_length=30, null=True)),
                ('response_code', models.CharField(blank=True, max_length=5)),
                ('response_description', models.CharField(blank=True, max_length=250)),
                ('is_resolved', models.BooleanField(default=True)),
                ('bill_ref', models.CharField(blank=True, max_length=25, null=True)),
                ('is_transaction_chargable', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('R', 'Reversed'), ('F', 'Failed'), ('P', 'Pending'), ('S', 'Successful'), ('I', 'Invalid')], default='P', max_length=1)),
            ],
        ),
        migrations.CreateModel(
            name='TerminalData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_provider', models.CharField(choices=[('unified_payments', 'Unified Payments')], default='unified_payments', max_length=100)),
                ('state', models.JSONField()),
                ('ejournal', models.JSONField()),
                ('transaction', models.JSONField()),
                ('is_valid', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('R', 'Reversed'), ('F', 'Failed'), ('P', 'Pending'), ('S', 'Successful'), ('I', 'Invalid')], default='P', max_length=1)),
                ('description', models.TextField(blank=True)),
                ('reference_id', models.CharField(blank=True, max_length=50)),
                ('date_created', models.DateTimeField(auto_now=True)),
                ('date_updated', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
