import uuid
from django.db import models

from common.validators import validate_phone_number
from django.utils.translation import gettext_lazy as _

# Create your models here.


class Status(models.TextChoices):
    REVERSED = "R", ("Reversed")
    FAILED = "F", ("Failed")
    PENDING = "P", ("Pending")
    COMPLETED = "S", ("Successful")
    INVALID = "I", ("Invalid")


class ChannelSendMoney(models.Model):
    class SendMoneyType(models.TextChoices):
        INTRA = "intra", ("Intra")
        INTER = "inter", ("Inter")

    user_ref = models.CharField(max_length=60, unique=True)
    transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    send_money_type = models.CharField(choices=SendMoneyType.choices, max_length=5)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default="0.00")
    sender_account = models.Char<PERSON><PERSON>(max_length=10)
    sender_institution_code = models.CharField(max_length=10)
    destination_account = models.CharField(max_length=10)
    destination_institution_code = models.Char<PERSON>ield(max_length=10)
    fee = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    remark = models.TextField()
    core_banking_ref = models.CharField(
        max_length=25, blank=True, null=True, db_index=True
    )
    nam_payment_reference = models.CharField(max_length=30, blank=True, null=True)
    response_code = models.CharField(max_length=5, blank=True)
    response_description = models.CharField(max_length=250, blank=True)
    is_resolved = models.BooleanField(default=True)
    bill_ref = models.CharField(max_length=25, blank=True, null=True)
    is_transaction_chargable = models.BooleanField(default=False)
    status = models.CharField(
        max_length=1, choices=Status.choices, default=Status.PENDING
    )


class ChannelAirtimeData(models.Model):

    user_ref = models.CharField(max_length=60, unique=True)
    transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default="0.00")
    sender_account = models.CharField(max_length=10)
    sender_email = models.EmailField()
    sender_institution_code = models.CharField(max_length=10)
    phone_number = models.CharField(max_length=16, validators=[validate_phone_number])
    payment_code = models.CharField(max_length=10)
    core_banking_ref = models.CharField(
        max_length=25, blank=True, null=True, db_index=True
    )
    nam_payment_reference = models.CharField(max_length=30, blank=True, null=True)
    switch_ref = models.CharField(max_length=30, blank=True, null=True)
    response_code = models.CharField(max_length=5, blank=True)
    response_description = models.CharField(max_length=250, blank=True)
    is_resolved = models.BooleanField(default=True)
    bill_ref = models.CharField(max_length=25, blank=True, null=True)
    is_transaction_chargable = models.BooleanField(default=False)
    status = models.CharField(
        max_length=1, choices=Status.choices, default=Status.PENDING
    )


class ChannelPayBills(models.Model):

    user_ref = models.CharField(max_length=60, unique=True)
    transaction_ref = models.UUIDField(default=uuid.uuid4, unique=True, db_index=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default="0.00")
    sender_account = models.CharField(max_length=10)
    sender_email = models.EmailField()
    sender_institution_code = models.CharField(max_length=10)
    phone_number = models.CharField(max_length=16, validators=[validate_phone_number])
    customer_unique_number = models.CharField(max_length=100)
    payment_code = models.CharField(max_length=10)
    core_banking_ref = models.CharField(
        max_length=25, blank=True, null=True, db_index=True
    )
    nam_payment_reference = models.CharField(max_length=30, blank=True, null=True)
    switch_ref = models.CharField(max_length=30, blank=True, null=True)
    response_code = models.CharField(max_length=5, blank=True)
    response_description = models.CharField(max_length=250, blank=True)
    is_resolved = models.BooleanField(default=True)
    bill_ref = models.CharField(max_length=25, blank=True, null=True)
    is_transaction_chargable = models.BooleanField(default=False)
    status = models.CharField(
        max_length=1, choices=Status.choices, default=Status.PENDING
    )


class TerminalData(models.Model):
    class ServiceProviderChoices(models.TextChoices):
        UNIFIED_PAYMENTS = "unified_payments", _("Unified Payments")

    service_provider = models.CharField(
        max_length=100,
        choices=ServiceProviderChoices.choices,
        default=ServiceProviderChoices.UNIFIED_PAYMENTS,
    )
    state = models.JSONField()
    ejournal = models.JSONField()
    transaction = models.JSONField()
    is_valid = models.BooleanField(default=False)
    status = models.CharField(
        max_length=1, choices=Status.choices, default=Status.PENDING
    )
    description = models.TextField(blank=True)
    reference_id = models.CharField(max_length=50, blank=True)
    date_created = models.DateTimeField(auto_now=True)
    date_updated = models.DateTimeField(auto_now_add=True)
