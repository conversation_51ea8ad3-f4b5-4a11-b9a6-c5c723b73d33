import logging
import requests

from .models import TerminalData
# from logic_omnichannel import settings
from django.conf import settings


def process_transaction(self, data: TerminalData):
    """
    Processes successful terminal transactions
    """
    log = logging.getLogger("django")
    transaction = data.transaction
    log.info(
        f"Processing Terminal Transaction for transaction with terminal transaction id {data.id}"
    )

    response_code = transaction.get("resp")
    if response_code == "00":
        log.info(
            f"Transaction is certified successful from PTSP, settlement process initiated"
        )
        # possible successful terminal transaction
        pan = transaction.get("mPan")
        if pan and pan[0] in ["4", "5", "6", "7"]:
            log.info("Transaction PAN is Valid")
            data.is_valid = True
            data.save()
            url = f"{settings.AGENCY_BANKING_BASE_URL}/terminal_api/process_terminal_transaction/"
            payload = {
                "terminal_code": transaction.get("tid"),
                "channel_transaction_id": data.id,
                "transaction_ref": f'{transaction.get("tid")}{transaction.get("rrn")}',
                "amount": transaction.get("amount"),
            }

            try:
                log.info(
                    f"Calling Agency Banking Service url -> {url} with payload -> {payload}"
                )
                resp = requests.post(url, json=payload)
                resp = resp.json()
                log.info(f"Response from Agency Banking Service -> {payload}")

                if resp.get("response_code") == "00":
                    # transaction processing
                    data.status = "S"
                    data.description = resp.get("response_description")
                    data.reference_id = resp.get("reference_id")
                    data.save()
                    log.info(
                        f"Transaction settlement submitted successfully. Terminal transaction updated to Success"
                    )
                else:
                    log.info(f"Unsuccessful response from Agency Banking -> {resp}")
                    data.status = "F"
                    data.description = resp.get("response_description")
                    data.save()

            except Exception as e:
                data.status = "P"
                data.description = f"Error Processing Transaction -> {e}"
                data.save()
                log.info(
                    f"Error occurred while processing Terminal transaction settlement error -> {e}. Terminal transaction updated to Pending"
                )
        else:
            # Invalid PAN
            data.status = "I"
            data.description = f"Invalid Terminal Transaction, PAN - {pan} not valid"
            data.save()
            log.info(
                f"Transaction PAN - {pan} is INVALID. Terminal transaction updated to invalid"
            )
    else:
        data.status = "I"
        data.description = (
            f"response code -> {response_code}; Transaction not successful"
        )
        data.save()
        log.info(
            f"Transaction is certified Failed from PTSP. Terminal transaction updated to Invalid"
        )
