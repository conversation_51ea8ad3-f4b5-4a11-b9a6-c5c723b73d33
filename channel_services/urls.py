from django.urls import path

from channel_services.views import (
    # ChannelSendMoney,
    ChannelSendMoneyAPI,
    ChannelSendSMS,
    RetrieveBalanceEnquiry,
    RetrieveBillPaymentItems,
    RetrieveNameEnquiry,
    ChannelAirtimeData,
    ChannelPayBills,
    TerminalNotification,
    ChannelsBVNValidation,
    ChannelGetTransationStatus
)
from common.views import ValidateUtility

urlpatterns = [
    path(
        "retrieve_bill_payment_items/<str:biller_code>/",
        RetrieveBillPaymentItems.as_view(),
        name="retrieve_bill_payment_items",
    ),
    path(
        "retrieve_balance_enquiry/",
        RetrieveBalanceEnquiry.as_view(),
        name="retrieve_balance_enquiry",
    ),
    path(
        "bill_payment_validation/",
        ValidateUtility.as_view(),
        name="bill_payment_validation",
    ),
    path(
        "retrieve_name_enquiry/",
        RetrieveNameEnquiry.as_view(),
        name="retrieve_name_enquiry",
    ),
    path("send_money/", ChannelSendMoneyAPI.as_view(), name="channel_send_money"),
    path("airtime_data/", ChannelAirtimeData.as_view(), name="channel_airtime_data"),
    path("send_sms/", ChannelSendSMS.as_view(), name="channel_send_sms"),
    path("pay_bills/", ChannelPayBills.as_view(), name="channel_pay_bills"),
    path(
        "terminal_notification/",
        TerminalNotification.as_view(),
        name="channel_terminal_notifocation",
    ),
    path("bvn_validation/<str:bvn>/", ChannelsBVNValidation.as_view(), name="channel_bvn_validation"),
    path("get_transaction_status/<str:user_ref>/<str:tsq_type>", ChannelGetTransationStatus.as_view(), name="channel_get_trans_status")
]
