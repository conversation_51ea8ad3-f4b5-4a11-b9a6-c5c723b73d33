import logging
import requests
import string

from requests import request
from channel_services.functions import process_transaction
from channel_services.models import TerminalData
# from logic_omnichannel import settings
from django.conf import settings

from logic_omnichannel.celery import app


@app.task(name="resolve_pending_terminal_transactions")
def resolve_pending_terminal_transaction():
    """
    Possible network error issue on the Agency Banking Service. Tasks queries
    Agency Banking service for terminal record. if it exists, terminal record
    is updated as required if not transaction is resent and the response is
    recorded
    """
    def terminal_transaction_exists(transaction: TerminalData) -> tuple(bool, string):
        url = f"{settings.AGENCY_BANKING_BASE_URL}/terminal_api/terminal_transaction?channel_id={transaction.id}"

        resp = requests.get(url)
        resp = resp.json()
        if resp.get("response_code") == "00":
            return (True, resp.get("reference_id"))
        else:
            return (False, "")

    transactions = TerminalData.objects.filter(is_valid=True, status="P")

    for transaction in transactions:
        transaction: TerminalData = transaction
        exists, reference_id = terminal_transaction_exists(transaction)
        if exists:
            # update transaction as successful
            transaction.status = "S"
            transaction.reference_id = reference_id
            transaction.save()
        else:
            # resend transaction
            process_transaction(transaction)
