from django.urls import path
from .views import (
    AccountStatement,
    AddCustomerAccount,
    DeleteBeneficiary,
    DeleteCustomerAccount,
    GetBeneficiaries,
    GetCustomerBalance,
    GetCustomerProfile,
    GetTransactions,
    SetPin,
    UpdateCustomerProfile,
    ValidatePin,
    VerifyCustomerAccount,
)


urlpatterns = [
    path(
        "add_customer_account/",
        AddCustomerAccount.as_view(),
        name="add_customer_account",
    ),
    path(
        "get_customer_profile/",
        GetCustomerProfile.as_view(),
        name="get_customer_profile",
    ),
    path(
        "delete_customer_account/<int:id>/",
        DeleteCustomerAccount.as_view(),
        name="delete_customer_account",
    ),
    path(
        "verify_customer_account/",
        VerifyCustomerAccount.as_view(),
        name="verify_customer_account",
    ),
    path("set_pin/", SetPin.as_view(), name="set_pin"),
    path("validate_pin/", ValidatePin.as_view(), name="validate_pin"),
    path("get_beneficiaries/", GetBeneficiaries.as_view(), name="get_beneficiaries"),
    path(
        "get_transactions/",
        GetTransactions.as_view({"get": "list"}),
        name="get_transactions",
    ),
    path(
        "get_transactions/<str:account_number>/",
        GetTransactions.as_view({"get": "retrieve"}),
        name="get_account_transactions",
    ),
    path(
        "get_account_balance/<str:account_number>/<str:account_institution_code>/",
        GetCustomerBalance.as_view(),
        name="get_account_balance",
    ),
    path(
        "delete_beneficiary/<str:account_number>/",
        DeleteBeneficiary.as_view(),
        name="delete_beneficiary",
    ),
    path(
        "update_customer_profile/",
        UpdateCustomerProfile.as_view(),
        name="update_customer_profile",
    ),
    path(
        "account_statement/<str:account_number>/",
        AccountStatement.as_view(),
        name="customer_account_statement",
    ),
]
