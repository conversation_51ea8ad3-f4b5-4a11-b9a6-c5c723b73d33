from django.contrib import admin

from .models import CustomerProfile, CustomerAccount
# Register your models here.


class CustomerAccountInline(admin.TabularInline):
    model = CustomerAccount


@admin.register(CustomerProfile)
class CustomerProfileAdmin(admin.ModelAdmin):

    list_display = ('user_name', 'first_name', 'last_name', 'status', 'app_code', 'is_phone_number_verified', 'is_email_verified', 'date_created')
    list_filter = ['status', 'is_phone_number_verified', 'is_email_verified']
    search_fields = ('customer__username', 'customer__first_name', 'customer__last_name')
    fields = ['user_name', 'first_name', 'last_name', 'phone_number', 'bvn', 'no_of_customer_account', 'mobile_channel_access', 'web_channel_access', 
    'ussd_channel_access', 'status']

    inlines = [
        CustomerAccountInline,
    ]

    def date_created(self, obj):
        return obj.customer.date_joined

    def user_name(self, obj):
        return obj.customer.username
    
    def first_name(self, obj):
        return obj.customer.first_name
    
    def last_name(self, obj):
        return obj.customer.last_name
    
    def app_code(self, obj):
        return obj.customer.app_code
    
    def has_change_permission(self, request, obj=None) -> bool:
        return False
    
    def has_add_permission(self, request, obj=None) -> bool:
        return False
    
    date_created.short_description = 'date created'
    user_name.short_description = 'username'
    first_name.short_description = 'first name'
    last_name.short_description = 'last name'


@admin.register(CustomerAccount)
class CustomerAccountAdmin(admin.ModelAdmin):

    list_display = ('user_name', 'first_name', 'last_name', 'account_number', 'bank', 'account_type', 'status', 'date_created')
    list_filter = ['account_type', 'status', 'bank']
    search_fields = ('account_number','profile__customer__username', 'profile__customer__first_name', 'profile__customer__last_name')
    readonly_fields = ("daily_cummulative", "onboarding_agent")

    def user_name(self, obj):
        return obj.profile.customer.username
    
    def first_name(self, obj):
        return obj.profile.customer.first_name
    
    def last_name(self, obj):
        return obj.profile.customer.last_name

    user_name.short_description = 'username'
    first_name.short_description = 'first name'
    last_name.short_description = 'last name'

