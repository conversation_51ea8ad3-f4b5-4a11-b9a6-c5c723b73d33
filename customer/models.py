from verdant.models import WalletType
from dfa.models import Agent
from bank.models import Bank, BankAccountType
from django.db import models
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save

from common.validators import validate_phone_number
from bank.models import Bank, BankAccountType

from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


User = get_user_model()
# Create your models here.

class CustomerProfile(models.Model):
    class Meta:
        verbose_name = "Profile"
        verbose_name_plural = "Profiles"

    ACTIVE = "A"
    INACTIVE = "I"
    INCOMPLETE = "incomplete"
    PENDING = "pending"
    COMPLETE = "complete"
    MALE = "MALE"
    FEMALE = "FEMALE"

    STATUS_CHOICES = (
        (ACTIVE, "Active"),
        (INACTIVE, "Inactive"),
    )
    KYC_VERIFICATION_STATUS_CHOICES = (
        (INCOMPLETE, "Incomplete"),
        (PENDING, "Pending"),
        (COMPLETE, "Complete"),
    )
    GENDER_CHOICES = (
        (FEMALE, "Female"),
        (MALE, "Male"),
    )

    customer = models.OneToOneField(User, on_delete=models.CASCADE, null=True)
    bvn = models.CharField(
        max_length=11, default="", blank=True, null=True, db_index=True
    )
    pin = models.CharField(max_length=250, default="", blank=True, null=True)
    phone_number = models.CharField(
        max_length=16, blank=True, null=True, validators=[validate_phone_number]
    )
    dob = models.CharField(
        verbose_name="Date of birth", max_length=25, blank=True, null=True
    )
    is_phone_number_verified = models.BooleanField(default=False)
    is_email_verified = models.BooleanField(default=False)
    no_of_customer_account = models.PositiveSmallIntegerField(default=0)
    mobile_channel_access = models.BooleanField(default=True)
    web_channel_access = models.BooleanField(default=True)
    ussd_channel_access = models.BooleanField(default=False)
    status = models.CharField(
        max_length=1,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    kyc_verification_status = models.CharField(
        max_length=10,
        choices=KYC_VERIFICATION_STATUS_CHOICES,
        default=INCOMPLETE,
    )
    gender = models.CharField(max_length=6, choices=GENDER_CHOICES, blank=True)
    has_recieved_app_update_notification = models.BooleanField(default=False)
    mifos_customer_id = models.CharField(max_length=10, blank=True, null=True)

    def __str__(self) -> str:
        return self.customer.username


class CustomerAccount(models.Model):

    class Meta:
        verbose_name = 'Account'
        verbose_name_plural = 'Accounts'

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    profile = models.ForeignKey(CustomerProfile, on_delete=models.CASCADE)
    account_number = models.CharField(max_length=15, unique=True, db_index=True)
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, to_field='code')
    account_type = models.ForeignKey(
        BankAccountType, on_delete=models.CASCADE, to_field="code", null=True
    )
    mobile_channel_access = models.BooleanField(default=True)
    web_channel_access = models.BooleanField(default=True)
    ussd_channel_access = models.BooleanField(default=False)
    status = models.CharField(
        max_length=1,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    daily_limit = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    daily_cummulative = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    date_created = models.DateTimeField('date published', auto_now_add=True)
    onboarding_agent = models.ForeignKey(
        Agent, on_delete=models.CASCADE, null=True, blank=True
    )
    is_wallet = models.BooleanField(default=False)
    wallet_type = models.ForeignKey(
        WalletType, on_delete=models.CASCADE, blank=True, null=True
    )
    wallet_no = models.CharField(max_length=15, unique=True, blank=True, null=True)
    nuban = models.CharField(max_length=15, unique=True, blank=True, null=True)

    def __str__(self) -> str:
        return self.account_number


class NextOfKin(models.Model):

    MALE = "MALE"
    FEMALE = "FEMALE"

    GENDER_CHOICES = (
        (FEMALE, "Female"),
        (MALE, "Male"),
    )

    profile = models.ForeignKey(CustomerProfile, on_delete=models.CASCADE)
    first_name = models.CharField(max_length=20)
    last_name = models.CharField(max_length=20)
    dob = models.CharField(max_length=20)
    phone_number = models.CharField(max_length=16, validators=[validate_phone_number])
    email = models.EmailField()
    gender = models.CharField(max_length=6, choices=GENDER_CHOICES, blank=True)


class KYCDocs(models.Model):
    class Meta:
        verbose_name = "Know Your Customer Documents"
        verbose_name_plural = "Know Your Customer Documents"

    INTERNATIONAL_PASSPORT = "international_passport"
    DRIVERS_LICENSE = "drivers_license"
    NATIONAL_ID = "national_id"
    VOTERS_CARD = "voters_card"

    MEANS_OF_ID_CHOICES = (
        (INTERNATIONAL_PASSPORT, "International Passport"),
        (DRIVERS_LICENSE, "Drivers License"),
        (NATIONAL_ID, "National ID"),
        (VOTERS_CARD, "Voters Card"),
    )

    profile = models.OneToOneField(CustomerProfile, on_delete=models.CASCADE)
    means_of_id = models.CharField(
        max_length=25, choices=MEANS_OF_ID_CHOICES, blank=True
    )
    id_base_64 = models.TextField(blank=True)
    id_issue_date = models.DateField(null=True, blank=True)
    id_expiry_date = models.DateField(null=True, blank=True)
    id_number = models.CharField(max_length=50, blank=True)

    signature_base_64 = models.TextField(blank=True)
    profile_picture_base_64 = models.TextField(blank=True)

    is_verified = models.BooleanField(default=False)

    def get_kyc_requirements_status(self):
        """
        returns the kyc requirments status for each kyc requirment
        """
        return [
            {
                "kyc_requirement": "Means of Identification",
                "is_updated": True if self.id_base_64 and self.id_number else False,
                "kyc_requirement_choice": [
                    {
                        "name": "International Passport",
                        "code": self.INTERNATIONAL_PASSPORT,
                    },
                    {"name": "Drivers License", "code": self.DRIVERS_LICENSE},
                    {"name": "National ID", "code": self.NATIONAL_ID},
                    {"name": "Voters Card", "code": self.VOTERS_CARD},
                ],
                "code": "means_of_identification",
            },
            {
                "kyc_requirement": "Signature",
                "is_updated": True if self.signature_base_64 else False,
                "kyc_requirment_choice": [],
                "code": "signature",
            },
            {
                "kyc_requirement": "Profile Picture",
                "is_updated": True if self.profile_picture_base_64 else False,
                "kyc_requirment_choice": [],
                "code": "profile_picture",
            },
        ]

    def clean_fields(self, exclude=None) -> None:
        super().clean_fields(exclude=exclude)

        # means_of_id, id_base_64 and id_number must all exist together
        if self.means_of_id or self.id_base_64 or self.id_number:
            # if any of this exists, all must exist
            if not (self.means_of_id and self.id_base_64 and self.id_number):
                error_dict = {}
                if not self.means_of_id:
                    error_dict['means_of_id'] = _(
                        "means of id must exist along side id image and id number"
                    )
                if not self.id_base_64:
                    error_dict['id_base_64'] =  _(
                        "id image must exist along side means of id and id number"
                    )
                if not self.id_number:
                    error_dict['id_number'] = _(
                        "id number must exist along side id image and means of id"
                    )
                raise ValidationError(
                    error_dict
                )
    
    def save(self, *args, **kwargs):
        # force clean data before saving into the database
        self.clean_fields()
        super().save(*args, **kwargs)


def update_kyc_verification_status(sender, instance, created, **kwargs):
    """
    Updates the customer profile verification status
    if all the documents have been uploaded and is_verified
    is still False, customer_profile kyc status will be pending
    and if all documents are uploaded and have been verified
    the status will be set to complete else status will remain
    as incomplete
    """
    customer_profile = instance.profile

    if (
        instance.id_base_64
        and instance.id_number
        and instance.signature_base_64
        and instance.profile_picture_base_64
    ):
        if instance.is_verified:
            customer_profile.kyc_verification_status = "complete"
        else:
            customer_profile.kyc_verification_status = "pending"
    else:
        customer_profile.kyc_verification_status = "incomplete"

    customer_profile.save()


post_save.connect(update_kyc_verification_status, sender=KYCDocs)
