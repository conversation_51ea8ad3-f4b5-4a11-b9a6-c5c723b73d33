# Generated by Django 3.2.3 on 2024-04-10 22:51

import common.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_number', models.CharField(db_index=True, max_length=15, unique=True)),
                ('mobile_channel_access', models.BooleanField(default=True)),
                ('web_channel_access', models.BooleanField(default=True)),
                ('ussd_channel_access', models.BooleanField(default=True)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=1)),
                ('daily_limit', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('daily_cummulative', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='date published')),
                ('is_wallet', models.BooleanField(default=False)),
                ('wallet_no', models.CharField(blank=True, max_length=15, null=True, unique=True)),
                ('nuban', models.CharField(blank=True, max_length=15, null=True, unique=True)),
            ],
            options={
                'verbose_name': 'Account',
                'verbose_name_plural': 'Accounts',
            },
        ),
        migrations.CreateModel(
            name='CustomerProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bvn', models.CharField(blank=True, db_index=True, default='', max_length=11, null=True)),
                ('pin', models.CharField(blank=True, default='', max_length=250, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=16, null=True, validators=[common.validators.validate_phone_number])),
                ('dob', models.CharField(blank=True, max_length=25, null=True, verbose_name='Date of birth')),
                ('is_phone_number_verified', models.BooleanField(default=False)),
                ('is_email_verified', models.BooleanField(default=False)),
                ('no_of_customer_account', models.PositiveSmallIntegerField(default=0)),
                ('mobile_channel_access', models.BooleanField(default=True)),
                ('web_channel_access', models.BooleanField(default=True)),
                ('ussd_channel_access', models.BooleanField(default=True)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=1)),
                ('kyc_verification_status', models.CharField(choices=[('incomplete', 'Incomplete'), ('pending', 'Pending'), ('complete', 'Complete')], default='incomplete', max_length=10)),
                ('gender', models.CharField(blank=True, choices=[('FEMALE', 'Female'), ('MALE', 'Male')], max_length=6)),
                ('has_recieved_app_update_notification', models.BooleanField(default=False)),
                ('mifos_customer_id', models.CharField(blank=True, max_length=10, null=True)),
            ],
            options={
                'verbose_name': 'Profile',
                'verbose_name_plural': 'Profiles',
            },
        ),
        migrations.CreateModel(
            name='NextOfKin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=20)),
                ('last_name', models.CharField(max_length=20)),
                ('dob', models.CharField(max_length=20)),
                ('phone_number', models.CharField(max_length=16, validators=[common.validators.validate_phone_number])),
                ('email', models.EmailField(max_length=254)),
                ('gender', models.CharField(blank=True, choices=[('FEMALE', 'Female'), ('MALE', 'Male')], max_length=6)),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customerprofile')),
            ],
        ),
        migrations.CreateModel(
            name='KYCDocs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('means_of_id', models.CharField(blank=True, choices=[('international_passport', 'International Passport'), ('drivers_license', 'Drivers License'), ('national_id', 'National ID'), ('voters_card', 'Voters Card')], max_length=25)),
                ('id_base_64', models.TextField(blank=True)),
                ('id_issue_date', models.DateField(blank=True, null=True)),
                ('id_expiry_date', models.DateField(blank=True, null=True)),
                ('id_number', models.CharField(blank=True, max_length=50)),
                ('signature_base_64', models.TextField(blank=True)),
                ('profile_picture_base_64', models.TextField(blank=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('profile', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='customer.customerprofile')),
            ],
            options={
                'verbose_name': 'Know Your Customer Documents',
                'verbose_name_plural': 'Know Your Customer Documents',
            },
        ),
    ]
