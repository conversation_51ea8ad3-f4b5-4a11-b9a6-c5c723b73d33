# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('verdant', '0001_initial'),
        ('dfa', '0001_initial'),
        ('bank', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customer', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='customerprofile',
            name='customer',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customeraccount',
            name='account_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='bank.bankaccounttype', to_field='code'),
        ),
        migrations.AddField(
            model_name='customeraccount',
            name='bank',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bank', to_field='code'),
        ),
        migrations.AddField(
            model_name='customeraccount',
            name='onboarding_agent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dfa.agent'),
        ),
        migrations.AddField(
            model_name='customeraccount',
            name='profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='customer.customerprofile'),
        ),
        migrations.AddField(
            model_name='customeraccount',
            name='wallet_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='verdant.wallettype'),
        ),
    ]
