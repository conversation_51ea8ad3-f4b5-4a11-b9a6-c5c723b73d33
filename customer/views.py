from datetime import date, timedelta, datetime
import logging
from threading import Thread
import os

from bank.models import Bank, Beneficiary
from bank.serializers import BeneficiarySerializer
from customer.functions import process_account_statement
from customer.utils import retrieve_update_core_banking_transactions
from transactions.models import AirtimeData, BillPayment, CoreBankingTransactions, SendMoney, Transaction
from django.core.exceptions import ObjectDoesNotExist
from customer.models import CustomerAccount
from django.db.models import Q
from django.contrib.auth.hashers import make_password, check_password
from django.core.validators import validate_email

from oauth2_provider.contrib.rest_framework import permissions
from rest_framework import generics, status, viewsets
from rest_framework.views import APIView
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response

from .serializers import AddCustomerAccountSerializer, GetAccountTransactionSerializer, GetAccountTransactionSerializerV2, GetTransactionsSerializer, SetPinSerializer, UpdateCustomerProfileSerializer, VerifyCustomerAccountSerializer
from common import services
from common.functions import (
    CustomDictPagination,
    CustomPageNumberPagination,
    account_has_platform_access,
    clean_transaction_records,
    get_bank_name_by_code,
    get_bank_profile,
    get_daily_limit,
    get_filtered_queryset_by_date,
    get_onboarding_status,
    get_platform,
    send_otp,
    set_response_data,
    update_daily_cummulative,
    verify_bvn_account,
    verify_otp,
)
from django.conf import settings
# from logic_omnichannel.settings import settings
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from drf_yasg import openapi

# Create your views here.

class AddCustomerAccount(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = AddCustomerAccountSerializer

    def post(self, request):

        log = logging.getLogger('django')
        log.info(f'ENTERING ADD CUSTOMER ACCOUNT API with data {request.data}')

        try:
            '''
                Retrieve and validate the data from the request payload the save into the 
                Customer Account model and update the number of accounts in the customer 
                profile
            '''
            serializer = AddCustomerAccountSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            validate_otp_at = serializer.validated_data['validate_otp_at']
            sms_otp = serializer.validated_data['sms_otp']

            # Verify the OTP
            otp_valid = verify_otp(validate_otp_at, sms_otp, request)
            if not otp_valid:
                raise ValidationError('Invalid SMS OTP, Please Confirm')
            
            customer = request.auth.user
            customer_profile = customer.customerprofile
            # Pass the authenticated customer into the serializer save function
            # serializer.save(profile=customer_profile)
            '''
                Begining of hack <CHECK WHY THE serializer.save() is not functioning well>
                
                after some weeks coming back here, I know why this was not working in the create function on serializer.save()
                some variables which are not need were included, popping out those variable will allow serializer.save() to work
                but since this is working fine, let it stay like this
            '''
            account_number = serializer.validated_data['account_number']
            bank = serializer.validated_data['bank']
            account_type = serializer.validated_data['account_type']
            daily_limit = get_daily_limit(account_type, bank)
            account = CustomerAccount.objects.create(profile=customer_profile, account_number=account_number, bank=bank, account_type=account_type, daily_limit=daily_limit)
            '''
                End of hack
            '''

            # increase the number of account the customer owns by 1
            # For some wierd reason I am doing this because i feel += 1 will not work
            customer_profile.no_of_customer_account = customer_profile.no_of_customer_account + 1
            customer_profile.save()

            data = {
                'response_code' : settings.SUCCESS_RESPONSE,
                'response_description' : 'Customer Account Added Successfully',
                'response_detail': f'{customer.last_name} {customer.first_name} has successfully added an account'
            }
            log.info(f'Exiting ADD CUSTOMER API with {data}')
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError as e:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : 'Invalid Request Payload',
                'response_detail' : serializer.errors if len(serializer.errors) > 0 else str(e)
            }
            log.info(f'Exiting ADD CUSTOMER API with {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : 'Unable to Add Customer Account',
                'response_detail' : str(e)
            }
            log.info(f'Exiting ADD CUSTOMER API with {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class DeleteCustomerAccount(APIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def delete(self, request, id):
        try:
            '''
                get the user from the authenticated request, get the list of accounts 
                owned by this user and then verify that the account with the passed id
                belongs to this user before deleting the account. Also substract 1 from the 
                no_of account in this customer profile
            '''
            customer = request.auth.user
            customer_profile = customer.customerprofile
            customer_accounts = CustomerAccount.objects.filter(profile=customer_profile)
            
            account_to_be_deleted = CustomerAccount.objects.get(id=id)

            if account_to_be_deleted in customer_accounts:
                account_to_be_deleted.delete()

                # decrease the number of account the customer owns by 1
                # For some wierd reason I am doing this because i feel -= 1 will not work
                customer_profile.no_of_customer_account = customer_profile.no_of_customer_account - 1
                customer_profile.save()

                data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description' : 'Account Deleted Successfully',
                    'response_detail' : 'Account Deleted Successfully'
                }
                return Response(data, status=status.HTTP_200_OK)
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'respone_description' : 'Account Not User Account',
                'response_detail' : 'Account set for deletion does not belong to user'
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'respone_description' : 'Account Does Not Exist',
                'response_detail' : f'Account with ID: {id} does not exist'
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : 'Error Occurred While Processing Request',
                'response_detail' : str(e)
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetCustomerProfile(APIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request):
        try:
            '''
                From the authenticated request get the customer confirm the customer 
                has a customer profile and then return the profile

                we also use the token to confirm the platform the request is coming from and use that to 
                return profiles accounts active on that platform
            '''
            customer = request.auth.user

            request_platform = get_platform(token=request.auth.token)

            if hasattr(customer, 'customerprofile'):
                customer_bank_profiles = []
                bank_id_list = []
                if customer.customerprofile.no_of_customer_account > 0:
                    accounts = CustomerAccount.objects.filter(profile=customer.customerprofile)
                    for account in accounts:
                        _account = {
                            'id' : account.id,
                            'account_name' : f'{account.profile.customer.last_name} {account.profile.customer.first_name}',
                            'account_number' : account.account_number,
                            'status' : account.status,
                            'date_created' : account.date_created,
                            'account_type_code' : account.account_type.code,
                            'channel_access' : 'BLOCK' if not account_has_platform_access(request_platform, account) or account.status == 'I' else 'ALLOW'
                        }
                        if account.bank.id not in bank_id_list:
                            bank_profile = get_bank_profile(account, request_platform)
                            customer_bank_profiles.append(bank_profile)
                            bank_id_list.append(account.bank.id)
                        bank_profile = customer_bank_profiles[bank_id_list.index(account.bank.id)]
                        if account.status == 'A':  # <-- Only include ACTIVE accounts
                            bank_profile['profile_account'].append(_account)
                            
                data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description' : 'Get Customer Profile Request Successful',
                    'response_detail' : {
                        'customer_details' : {
                            'id' : customer.id,
                            'username' : customer.get_username(),
                            'first_name' : customer.first_name,
                            'last_name' : customer.last_name,
                            'email' : customer.email,
                            'is_active' : customer.is_active,
                            'last_login' : customer.last_login,
                            'onboarding_status' : get_onboarding_status(customer),
                            'bvn' : customer.customerprofile.bvn,
                            'phone_number' : customer.customerprofile.phone_number,
                            'is_phone_number_verified' : customer.customerprofile.is_phone_number_verified,
                            'is_email_verified' : customer.customerprofile.is_email_verified,
                            'no_of_customer_account' : customer.customerprofile.no_of_customer_account,
                            "pin_enabled": True if customer.customerprofile.pin else False,
                            'status' : customer.customerprofile.status,
                            'channel_access' : 'BLOCK' if not account_has_platform_access(request_platform, customer.customerprofile) or customer.customerprofile.status == 'I' else 'ALLOW'
                        },
                        'customer_bank_profiles' : customer_bank_profiles
                    }
                }
            else:
                data = {
                    'response_code' : settings.SUCCESS_RESPONSE,
                    'response_description' : 'User Does Not Have A Customer Profile',
                    'response_detail' : 'User Does Not Have A Customer Profile'
                }
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : 'Error Retriving Customer Profile',
                'response_detail' : str(e)
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)



class UpdateCustomerProfile(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = UpdateCustomerProfileSerializer

    def patch(self, request):
        try:
            '''
                Try to get the authenticated user then pass the instance of user into the the serializer
                to validate the sent payload and then update the user
            '''
            customer = request.auth.user
            serializer = UpdateCustomerProfileSerializer(instance=customer, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            
            serializer.save()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Customer Profile Updated Successfully',
                detail='Customer Profile Updated Successfully'
            )
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Invalid Request Payload',
                detail=serializer.errors
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Error Updating Customer Profile',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetCustomerBalance(generics.ListAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def list(self, request, account_number, account_institution_code):
        try:
            '''
                Confirm the customer from the authenticated request, then confirm the account is mapped
                to the customer and confirm the account_number as well as the institution provides a valid
                account mapped to the customer
            '''
            customer = request.auth.user
            token = request.auth.token

            # PRODUCTION
            app_code = customer.app_code 

            # DEVELOPMENT
            # app_code = None

            if hasattr(customer, 'customerprofile'):
                if customer.customerprofile.no_of_customer_account > 0:
                    customer_account = CustomerAccount.objects.get(profile=customer.customerprofile, account_number=account_number)
                    bank = customer_account.bank
                else:
                    raise Exception('Customer Does Not Have Accounts Mapped To Profile')
            else:
                raise Exception('Customer Does Not Have a Valid Profile')
            if customer_account.bank.code != account_institution_code:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description='Invalid Account Institution Code',
                    detail='Institution code does not match for account number'
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            # if customer_account.bank.belong_to_mifos_wallet_service:
            #     resp = services.get_wallet_balance(customer_account.wallet_no)
            # else:
            resp = services.get_balance_enquiry(
                bank,
                account_number,
                account_institution_code,
                app_code=app_code,
                b_token=token,
                )
            if resp.get('response_code') == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='Balance Enquiry Request Successful',
                    detail=resp.get('response_description')
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description='Error Calling the Balance Enquiry Service',
                    detail=resp.get('response_description')
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Invalid Account',
                detail='Account number does not exist'
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Error Processing Balance Enquiry Request',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        

class VerifyCustomerAccount(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = VerifyCustomerAccountSerializer

    def post(self, request):

        log = logging.getLogger('django')
        log.info(f'ENTERING VERIFY CUSTOMER ACCOUNT API with data {request.data}')

        try:
            '''
                Validate the request payload. then use the payload to call the name enquiry 
                service. Based on the reponse verify the response against the BVN and if 
                verified return the customer name
            '''
            serializer = VerifyCustomerAccountSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # PRODUCTION
            app_code = request.auth.user.app_code 

            # DEVELOPMENT
            # app_code = None


            account_number = serializer.validated_data['account_number']
            institution_code = serializer.validated_data['institution_code']

            sender_bank = Bank.objects.filter(Q(nip_code=institution_code) | Q(code=institution_code)).first()

            name_enq = services.get_name_enquiry(
                sender_bank=sender_bank, 
                account_number=account_number,
                institution_code=institution_code, 
                app_code=app_code
                )

            if name_enq['response_code'] == settings.SUCCESS_RESPONSE:
                customer = request.auth.user
                # bvn = customer.customerprofile.bvn
                name_enq_description = name_enq['response_description']
                # is_customer_account = verify_bvn_account(bvn, name_enq_description)
                # if is_customer_account or settings.APP_ENV == "development":
                    # Send the customer phone number OTP
                resp = send_otp(request)
                if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                    data = {
                        'response_code' : settings.SUCCESS_RESPONSE,
                        'response_description' : "Account Verification Successful",
                        'response_detail' : {
                            'account_name' : name_enq_description['account_name'],
                            'account_number': account_number,
                            'validate_otp_at' : resp['response_detail']['validate_otp_at'],
                            'otp': resp['response_detail']['otp']
                        }
                    }
                    return Response(data, status=status.HTTP_200_OK)
                else:
                    return Response(resp, status=status.HTTP_400_BAD_REQUEST)
                # else:
                #     data = {
                #         'response_code' : settings.FAILED_RESPONSE,
                #         'response_description' : 'Account Verification Failed: BVN mismatch Kindly contact your core banking provider',
                #         'response_detail' : 'This Account is not Customer Account: BVN mismatch Kindly contact your core banking provider'
                #     }
                #     log.info(f'Exiting ADD CUSTOMER API with {data}')
                #     return Response(data, status=status.HTTP_400_BAD_REQUEST)
            elif name_enq['response_code'] == settings.FAILED_RESPONSE:
                data = {
                    'response_code' : settings.FAILED_RESPONSE,
                    'response_description' : "Name Enquiry Failed",
                    'response_detail' : name_enq['response_description']
                }
                log.info(f'Exiting ADD CUSTOMER API with {data}')
                return Response(data, status=status.HTTP_200_OK)

            raise Exception('Name Enquiry Failed')
        except ValidationError:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : 'Invalid Request Payload',
                'response_detail' : serializer.errors
            }
            log.info(f'Exiting ADD CUSTOMER API with {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                'response_code' : settings.FAILED_RESPONSE,
                'response_description' : 'Switch Error',
                'response_detail' : str(e)
            }
            log.info(f'Exiting ADD CUSTOMER API with {data}')
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SetPin(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = SetPinSerializer

    def post(self, request):

        log = logging.getLogger("django")
        log.info("Entering SET PIN API")

        try:
            """
            gets the validated pin from the request payload and also get the authenticated
            customer. Then set the pin on the customer profile
            """
            serializer = SetPinSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            pin = serializer.validated_data["pin"]
            password = serializer.validated_data["password"]
            user = request.auth.user

            # check password is valid
            is_password_valid = check_password(password, user.password)
            if is_password_valid:
                if hasattr(user, "customerprofile"):
                    user.customerprofile.pin = make_password(pin)
                    user.customerprofile.save()
                    data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description="PIN SET SUCCESSFUL",
                        detail="Customer Pin Set Successfully",
                    )
                    log.info(f"Exiting SET PIN API with response: {data}")
                    return Response(data, status=status.HTTP_200_OK)
                elif hasattr(user, "agent"):
                    user.agent.pin = make_password(pin)
                    user.agent.save()
                    data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description="PIN SET SUCCESSFUL",
                        detail="Agent Pin Set Successfully",
                    )
                    log.info(f"Exiting SET PIN API with response: {data}")
                    return Response(data, status=status.HTTP_200_OK)
                else:
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="USER HAS NO PROFILE",
                        detail="USER HAS NO PROFILE",
                    )
                    log.info(f"Exiting SET PIN API with response: {data}")
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Password",
                    detail="Password is incorrect",
                )
                log.info(f"Exiting SET PIN API with response: {data}")
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="INVALID PAYLOAD REQUEST",
                detail=serializer.errors,
            )
            log.info(f"Exiting SET PIN API with response: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="ERROR SETTING PASSWORD",
                detail=str(e),
            )
            log.info(f"Exiting SET PIN API with response: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

class ValidatePin(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = SetPinSerializer

    def post(self, request):
        try:
            '''
                gets the pin from the request payload and also get the authenticated
                customer. Then check the pin is same as the customer pin
            '''
            serializer = SetPinSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            pin = serializer.validated_data['pin']
            customer = request.auth.user

            if not hasattr(customer, 'customerprofile'):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description='USER HAS NO PROFILE',
                    detail='USER HAS NO PROFILE'
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            if customer.customerprofile.pin is not None:
                is_pin_valid = check_password(pin, customer.customerprofile.pin)
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='VALIDATE PIN REQUEST SUCCESSFUL',
                    detail={
                        'is_pin_valid': is_pin_valid
                    }
                )
                return Response(data, status=status.HTTP_200_OK)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='USER DOES NOT HAVE SET PIN',
                detail='User does not have set pin, Kindly set pin'
            )
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='INVALID PAYLOAD REQUEST',
                detail=serializer.errors
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='ERROR VALIDATING PIN',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetBeneficiaries(generics.ListAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = BeneficiarySerializer
    pagination_class = CustomPageNumberPagination

    def get_queryset(self):
        '''
            get the list of beneficiaries based on descending order of the number of transactions
            made by the user
        '''
        customer = self.request.auth.user
        benficiary_list = Beneficiary.objects.filter(user=customer).order_by('-transactions_count')
        return benficiary_list

    def list(self, request):
        try:
            '''
                Get the query set, Paginate the query to reduce the request been sent
                the format the request been sent using the serializer as well as the 
                paginated response.
            '''
            queryset = self.get_queryset()

            queryset = self.paginate_queryset(queryset)
            serializer = BeneficiarySerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)

            if queryset:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    'Get Beneficiaries Request Successful',
                    paginated_resp.data
                )
            else:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    'Benficiaries List Empty',
                    paginated_resp.data
                )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Unable to Get Beneficiaries',
                str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class DeleteBeneficiary(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    def delete(self, request, account_number):
        try:
            '''
                get the authenticated customer and confirm that the customer has the account number
                has a valid beneficiary
            '''
            customer = request.auth.user
            beneficiary = Beneficiary.objects.get(user=customer, account_number=account_number)
            beneficiary.delete()
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Beneficiary Deleted Successfully',
                detail='Beneficiary deleted successfully'
            )
            return Response(data, status=status.HTTP_200_OK)
        except Beneficiary.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Beneficiary Does Not Exist',
                detail='Beneficiary does not exist'
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Error Occurred While Processing Request',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetTransactions(viewsets.GenericViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = GetTransactionsSerializer
    pagination_class = CustomPageNumberPagination
    
    start_date = openapi.Parameter(
        "start_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    end_date = openapi.Parameter(
        "end_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    page = openapi.Parameter(
        "page",
        openapi.IN_QUERY,
        description="Page No.",
        type=openapi.TYPE_STRING,
        required=False,
    )
    page_size = openapi.Parameter(
        "page_size",
        openapi.IN_QUERY,
        description="Number of items per page",
        type=openapi.TYPE_STRING,
        required=False,
    )

    def get_queryset(self, account=None):
        '''
            From a combined list of all transactions <Send Money, Airtime/Data, Bills Payment> all combined into
            the Transaction Model, provide the filtered list based on query params.
            
            To access the particular transaction object, we query with content_object to get the transaction type,
            we query with content_type.

            We use the Q expression to generate  query for all transactions.
        '''
        customer = self.request.auth.user
        if account:
            # for accounts we also consider the core banking transactions
            transactions = CoreBankingTransactions.objects.filter(sender_account=account).order_by('-date_created')
        else:
            if hasattr(customer, 'customerprofile'):
                transactions = Transaction.objects.filter(
                    Q(send_money__sender=customer.customerprofile) | Q(airtime_data__sender=customer.customerprofile) |
                    Q(bill_payment__sender=customer.customerprofile)
                )
            elif hasattr(customer, 'agent'):
                transactions = Transaction.objects.filter(send_money__agent=customer.agent)

        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)
        amount = self.request.query_params.get('amount', None)
        transaction_type = self.request.query_params.get('transaction_type', None)
        transaction_ref = self.request.query_params.get('transaction_ref', None)

        if start_date and end_date:
            transactions = get_filtered_queryset_by_date(
                start_date,
                end_date,
                transactions,
                CoreBankingTransactions if account else Transaction,
            )
        if account:
            # at this point the remaining checks are not required so we just return the filtered queryset
            return transactions
        if amount:
            transactions = transactions.filter(Q(send_money__amount=amount) | Q(airtime_data__amount=amount) | Q(bill_payment__amount=amount))
        if transaction_type:
            transactions = transactions.filter(
                Q(send_money__transaction_type=transaction_type) | Q(airtime_data__transaction_type=transaction_type) | 
                Q(bill_payment__transaction_type=transaction_type)
            )
        if transaction_ref:
            transactions = transactions.filter(
                Q(send_money__transaction_ref=transaction_ref) | Q(airtime_data__transaction_ref=transaction_ref) | 
                Q(bill_payment__transaction_ref=transaction_ref)
            )
        return transactions
    
    @swagger_auto_schema(manual_parameters=[start_date, end_date])
    def list(self, request):
        try:
            '''
                Get the query set, Paginate the query to reduce the request been sent
                the format the request been sent using the serializer as well as the 
                paginated response.
            '''
            queryset = self.get_queryset()

            queryset = self.paginate_queryset(queryset)
            serializer = GetTransactionsSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)

            if queryset:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='GET TRANSACTIONS SUCCESSFUL',
                    detail=paginated_resp.data
                )
            else:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='TRANSACTIONS LIST EMPTY',
                    detail=paginated_resp.data
                )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='UNABLE TO GET TRANSACTIONS LIST',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST) 

    @swagger_auto_schema(manual_parameters=[start_date, end_date, page_size, page])
    def retrieve(self, request, account_number):
        try:
            '''
                Get the query set, Paginate the query to reduce the request been sent
                the format the request been sent using the serializer as well as the 
                paginated response.
            '''
            customer = request.auth.user
            account = CustomerAccount.objects.get(
                account_number=account_number, profile=customer.customerprofile
                )
            
            start_date = self.request.query_params.get('start_date', None)
            end_date = self.request.query_params.get('end_date', None)
            page = self.request.query_params.get('page', None)
            bank = account.bank
            if start_date and end_date:
                response = services.get_account_transactions_history(
                    account_number=account_number, bank=bank, from_date=start_date, to_date=end_date
                )
            # elif start_date and end_date and page:
            #     response = services.get_account_transactions_history(
            #         account_number=account_number, bank=bank, from_date=start_date, to_date=end_date, page_number=page
            #     )
            else:
                response = services.get_account_transactions_history(account_number=account_number, bank=bank)
                logging.info(f"Response from get_account_transactions_history -> {response}")
            if response.get("response_code") == "00":
                transactions = response.get("response_detail").get("transactions")
                # refined_transactions = []

                for trnx in transactions:
                    reference = trnx.get("reference_id", None)
                    if reference:
                        transaction = Transaction.objects.filter(
                            Q(send_money__nam_payment_reference=reference)
                            | Q(airtime_data__nam_payment_reference=reference)
                            | Q(bill_payment__nam_payment_reference=reference)
                        )
                        if transaction:
                            transaction = transaction[0]
                            beneficiary_account_number = ""
                            beneficiary_name = ""
                            beneficiary_bank = ""
                            sender_account_number = ""
                            sender_name = ""
                            sender_bank = ""

                            # -----transaction details----------#
                            transaction_date = datetime.strftime(transaction.content_object.date_created, "%Y-%m-%d %H:%M:%S")
                            tsq_type = transaction.content_object.transaction_type.code
                            transaction_type = transaction.content_object.transaction_type.name
                            transaction_status = transaction.content_object.status
                            transaction_description = transaction.content_object.response_description
                            customer_reference = transaction.content_object.user_ref
                            channel = transaction.content_object.channel
                            if isinstance(transaction.content_object, SendMoney):
                                # ------ Beneficiary details----------#
                                beneficiary_account_number = transaction.content_object.destination_account_number
                                beneficiary_name = transaction.content_object.destination_account_name
                                beneficiary_bank = get_bank_name_by_code(
                                    transaction.content_object.destination_institution_code
                                )
                                # ------ Sender details----------#
                                sender_account_number = (
                                    transaction.content_object.sender_account.account_number or 
                                    transaction.content_object.agent_account.account_number
                                    )
                                sender_name = (
                                    transaction.content_object.sender.customer.get_full_name()
                                    or transaction.content_object.agent.get_full_name()
                                    )
                                sender_bank = (
                                    transaction.content_object.sender_account.bank.name or
                                    transaction.content_object.agent.bank.name
                                    )

                                trnx["tsq_type"] = tsq_type or "FT"
                                trnx["transaction_type"] = transaction_type or "Fund Transfer"
                            
                                
                            elif isinstance(transaction.content_object, AirtimeData):
                                
                                # ------ Beneficiary details----------#
                                biller = transaction.content_object.biller.biller_name
                                credited_phone_number = transaction.content_object.phone_number
                            
                                # ------ Sender details----------#
                                sender_account_number = (
                                    transaction.content_object.sender_account.account_number or 
                                    transaction.content_object.agent_account.account_number
                                    )
                                sender_name = (
                                    transaction.content_object.sender.customer.get_full_name()
                                    or transaction.content_object.agent.get_full_name()
                                    )
                                sender_bank = (
                                    transaction.content_object.sender_account.bank.name or
                                    transaction.content_object.agent.bank.name
                                    )
                                
                                trnx["biller"] = biller or ""
                                trnx["credited_phone_number"] = credited_phone_number or ""
                                trnx["tsq_type"] = tsq_type or "AT"
                                trnx["transaction_type"] = transaction_type or "Airtime/Data"

                            elif isinstance(transaction.content_object, BillPayment):
                                
                                # ------ Beneficiary details----------#
                                biller = transaction.content_object.biller_item.biller_name
                                category = transaction.content_object.biller_category.name
                                beneficiary_unique_number = transaction.content_object.customer_field_1
                                beneficiary_name = transaction.content_object.customer_field_2
                                recharge_pin = transaction.content_object.recharge_pin
                                
                                # ------ Sender details----------#
                                sender_account_number = transaction.content_object.sender_account.account_number 
                                sender_name = transaction.content_object.sender.customer.get_full_name()
                                sender_bank = transaction.content_object.sender_account.bank.name

                                trnx["biller"] = biller or ""
                                trnx["category"] = category or ""
                                trnx["beneficiary_unique_number"] = beneficiary_unique_number or ""
                                trnx["beneficiary_name"] = beneficiary_name or ""   
                                trnx["recharge_pin"] = recharge_pin or ""
                                trnx["tsq_type"] = tsq_type or "BP"
                                trnx["transaction_type"] = transaction_type or "Bill Payment"
                            
                        
                            trnx["transaction_date"] = transaction_date 
                            trnx["transaction_status"] = transaction_status or ""
                            trnx["transaction_description"] = transaction_description or ""
                            trnx["customer_reference"] = customer_reference or ""                
                            trnx["channel"] = channel or ""
                            
                            trnx["beneficiary_account_number"] = beneficiary_account_number
                            trnx["beneficiary_name"] = beneficiary_name
                            trnx["beneficiary_bank"] = beneficiary_bank or ""
                            trnx["sender_account_number"] = sender_account_number or ""
                            trnx["sender_name"] = sender_name or ""
                            trnx["sender_bank"] = sender_bank or ""


                paginator = CustomDictPagination()
                paginated_data = paginator.paginate_queryset(transactions, request)
                serializer = GetAccountTransactionSerializerV2(paginated_data, many=True)
                paginated_resp = paginator.get_paginated_response(serializer.data)
                logging.info(f"Response from get_account_transactions_history PAGINATED RESP-> {response}")

                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Successfully retrieved transactions",
                    detail=paginated_resp.data
                )
                return Response(data, status=status.HTTP_200_OK)
            
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable to retrieve account history",
                    detail="Unable to retrieve account history"
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Customer Account",
                detail="Invalid Customer Account"
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Retrieving Account Transactions",
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
    
    def retrieve_update_core_banking_transactions(self, account, app_code):
        """
        try to retrieve new core banking transactions from the core banking service and update it
        in the local core banking transaction database storage.
        We take into consideration date so we do not have to make large date range calls to the core
        banking service
        """
        core_banking_transactions = CoreBankingTransactions.objects.filter(
            sender_account=account
        ).order_by("-date_created")
        if len(core_banking_transactions) == 0:
            # at this point, it is either the customer is a new customer or the customer has
            # not made any transaction which has been saved to the db yet
            # so we query from the current day till the day the customer account was created
            date_range = {
                "end_date": f'{date.today().strftime("%Y-%m-%d")}',
                "start_date": f'{account.date_created.strftime("%Y-%m-%d")}',
            }
        else:
            # at this point the customer has some transactions stored so we query from the
            # current day till the day of the last stored transaction there by reducing the
            # amount of data gotten back from the core banking service
            date_range = {
                "end_date": f'{date.today().strftime("%Y-%m-%d")}',
                "start_date": f'{core_banking_transactions[0].date_created.strftime("%Y-%m-%d")}',
            }
        resp = services.get_account_transactions(
            date_range, account.account_number, app_code, account.bank.code
        )
        if resp.get("response_code") == settings.SUCCESS_RESPONSE:
            transactions = resp.get("transactions", [])
            for transaction in transactions:
                date_created = datetime.strptime(transaction.get("date"), "%Y-%m-%d")
                (
                    core_banking_transaction,
                    created,
                ) = CoreBankingTransactions.objects.get_or_create(
                    sender_account=account,
                    type=transaction.get("type"),
                    payment_ref=transaction.get("paymentReference"),
                    transaction_ref=transaction.get("transactionReference"),
                    description=transaction.get("description"),
                    amount=f'{transaction.get("amount")}',
                    narration=transaction.get("narration"),
                    core_banking_date=date_created,
                )
                if core_banking_ref := transaction.get("transactionReference"):
                    # This means the transaction was generated from omni channel platform
                    # so we find that transaction reference and map it the corresponding core banking transaction
                    omni_channel_generated_transaction = Transaction.objects.filter(
                        Q(send_money__core_banking_ref=core_banking_ref)
                        | Q(airtime_data__core_banking_ref=core_banking_ref)
                        | Q(bill_payment__core_banking_ref=core_banking_ref)
                        | Q(international_send_money__core_banking_ref=core_banking_ref)
                    )
                    if omni_channel_generated_transaction:
                        omni_channel_generated_transaction = (
                            omni_channel_generated_transaction[0]
                        )
                        if isinstance(
                            omni_channel_generated_transaction.content_object, SendMoney
                        ):
                            # it is possible that the omni channel generated transaction was saved as pending or failed due to
                            # system / network glitch but the transaction was infact successful, we fix our internal db at this point
                            transaction_object = (
                                omni_channel_generated_transaction.content_object
                            )
                            if transaction_object.sender_account != account:
                                # this situation occurs for edge case scenario where is sending from one of his/her
                                # account to the other within the same bank we just halt the remainder flow
                                continue
                            if (
                                transaction_object.status != "S"
                                and core_banking_transaction.type == "Debit"
                            ):
                                transaction_object.status = "S"
                                transaction_object.is_resolved = (
                                    False if transaction_object.fee else True
                                )
                                transaction_object.is_transaction_chargable = (
                                    True if transaction_object.fee else False
                                )
                                transaction_object.response_code = (
                                    settings.SUCCESS_RESPONSE
                                )
                                transaction_object.response_description = (
                                    "Transaction Successful"
                                )
                                transaction_object.save()

                            # it is also possible that a transaction was debited and then credit back causing the reversal problem so in this case we
                            # we need to mark that transaction has reversed and update our internal db as well
                            if (
                                transaction_object.status == "S"
                                and core_banking_transaction.type == "Credit"
                            ):
                                if (
                                    transaction_object.response_code
                                    == settings.SUCCESS_RESPONSE
                                ):
                                    """
                                    case scenario where account was debited and the daily cummulative as well is also
                                    adjusted we will have to subtract the reversal from the daily cummulative
                                    """
                                    Thread(
                                        target=update_daily_cummulative,
                                        args=(
                                            (transaction_object.amount * -1),
                                            transaction_object.sender_account,
                                        ),
                                    ).start()
                                transaction_object.status = "R"
                                transaction_object.is_resolved = True
                                transaction_object.is_transaction_chargable = False
                                transaction_object.response_code = (
                                    settings.FAILED_RESPONSE
                                )
                                transaction_object.response_description = (
                                    "Transaction Reversed"
                                )
                                transaction_object.save()

                        core_banking_transaction.omni_channel_generated_transaction = (
                            omni_channel_generated_transaction
                        )
                        core_banking_transaction.save()
                if created and core_banking_transaction.type.lower() == "credit":
                    # we check for newly created core banking credit transactions then we request for an
                    # update in account balance for the particular account
                    services.update_balance_enquiry(
                        self.request.auth.token, account.account_number, retry=True
                    )
            return {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Retrieve, Update Successful",
            }
        else:
            return {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f'Unable to Update Records -> {resp.get("response_description")}',
            }


class AccountStatement(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]

    start_date = openapi.Parameter(
        "start_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=True,
    )
    end_date = openapi.Parameter(
        "end_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=True,
    )
    email = openapi.Parameter(
        "email",
        openapi.IN_QUERY,
        description="Email - Not required defaults to customer email.",
        type=openapi.TYPE_STRING,
        required=False,
    )
    file_type = openapi.Parameter(
        "file_type",
        openapi.IN_QUERY,
        description="Account Statement File Type",
        type=openapi.TYPE_STRING,
        enum=["pdf", "excel"],
        required=True,
    )

    @swagger_auto_schema(manual_parameters=[start_date, end_date, email, file_type])
    def get(self, request, account_number):
        try:
            """
            validates the query params and tries to generate and send account statement
            to customer or email provided by the customer.
            """
            customer = request.auth.user
            account: CustomerAccount = CustomerAccount.objects.get(
                account_number=account_number, profile=customer.customerprofile
            )
            start_date = request.query_params.get("start_date", None)
            end_date = request.query_params.get("end_date", None)
            email = request.query_params.get("email", None)
            file_type = request.query_params.get("file_type", None)

            if not start_date or not end_date:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Start date and end date is required",
                    detail="Start date and end date is required",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if not file_type:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="File Type is required",
                    detail="File Type is required",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            elif file_type not in ["pdf", "excel"]:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"Invalid File Type - {file_type}",
                    detail=f"Invalid File Type - {file_type}",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if email:
                try:
                    validate_email(email)
                except Exception:
                    data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description=f"Invalid Email Address - {email}",
                        detail=f"Invalid Email Address - {email}",
                    )
                    return Response(data, status=status.HTTP_400_BAD_REQUEST)
            else:
                email = account.profile.customer.email

            start_date = datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)

            if end_date < start_date:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid date: start date can not be greater than end date",
                    detail="Invalid Date",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            # Thread(
            #     target=process_account_statement,
            #     args=(account, account_number, start_date, end_date, file_type, email),
            # ).start()

            process_account_statement(account, start_date, end_date, file_type, email)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Statement Generation in Progress, You'd be notified as soon as it is completed",
                detail="Account Statement Generation in Progress",
            )
            return Response(data, status=status.HTTP_200_OK)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Customer Account",
                detail="Invalid Customer Account",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Generating Account Statement",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


        
# def get_query_params(self):

    #     start_date = self.request.query_params.get('start_date', None)
    #     end_date = self.request.query_params.get('end_date', None)
    #     transaction_type = self.request.query_params.get('transaction_type', None)

    #     if not start_date or not end_date:
    #         end_date = date.today().strftime("%Y-%m-%d")
    #         start_date = (date.today() - timedelta(weeks=4)).strftime("%Y-%m-%d") # 1 month ago
        
    #     return {
    #         'start_date': start_date,
    #         'end_date': end_date,
    #         'transaction_type': transaction_type
    #     }


# def retrieve(self, request, account_id):
#         try:
#             '''
#                 Try to call the core banking to get list of transactions,
#                 we then use the transaction reference to provide more details for transactions 
#                 that orignated from the omni channel platform
#             '''
#             customer = request.auth.user
#             account = CustomerAccount.objects.get(id=account_id, profile=customer.customerprofile)
#             query_params = self.get_query_params()
#             app_code = customer.app_code
#             if app_code != account.bank.alias:
#                 data =set_response_data(
#                     code=settings.FAILED_RESPONSE,
#                     description='Invalid Customer Account',
#                     detail='Invalid Customer Account'
#                 )
#                 return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
#             resp = services.get_account_transactions(query_params, account.account_number, app_code, account.bank.code)
#             if resp.get('response_code') == settings.SUCCESS_RESPONSE:
#                 transactions = resp.get('transactions', [])
#                 transactions = clean_transaction_records(transactions, self.get_queryset(account))
#                 data = set_response_data(
#                     code=settings.SUCCESS_RESPONSE,
#                     description='GET ACCOUNTS TRANSACTIONS SUCCESSFUL',
#                     detail=transactions
#                 )
#                 return Response(data, status=status.HTTP_200_OK)
#             else:
#                 data = set_response_data(
#                     code=settings.FAILED_RESPONSE,
#                     description=resp.get('response_description', 'UNABLE To RETRIEVE TRANSACTIONS'),
#                     detail=[]
#                 )
#                 return Response(data, status=status.HTTP_400_BAD_REQUEST)
#         except CustomerAccount.DoesNotExist:
#             data = set_response_data(
#                 code=settings.FAILED_RESPONSE,
#                 description='Invalid Customer Account',
#                 detail='Invalid Customer Account'
#             )
#             return Response(data, status=status.HTTP_400_BAD_REQUEST)
#         except Exception as e:
#             data = set_response_data(
#                 code=settings.FAILED_RESPONSE,
#                 description='UNABLE TO GET TRANSACTIONS LIST',
#                 detail=str(e)
#             )
#             return Response(data, status=status.HTTP_400_BAD_REQUEST) 
