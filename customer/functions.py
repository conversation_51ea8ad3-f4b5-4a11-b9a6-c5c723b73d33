import os
import logging
import xlsxwriter
from fpdf import FPDF
from datetime import datetime

from customer.models import CustomerAccount
from common.services import retrieve_account_statement, send_email
# from logic_omnichannel import settings
from django.conf import settings

log = logging.getLogger("django")


class PDF(FPDF):
    def __init__(
        self,
        account: CustomerAccount,
        start_balance: str,
        end_balance: str,
        start_date: str,
        end_date: str,
        *args,
        **kwargs,
    ):
        super().__init__(*args, **kwargs)
        self.colour = account.bank.email_background_color
        self.account: CustomerAccount = account
        self.start_balance = start_balance
        self.end_balance = end_balance
        self.start_date = start_date
        self.end_date = end_date
        self.bank_name = account.bank.name

    def header(self):
        # Colors of frame, background and text
        bank_fill_color = self.hex_to_rgb()
        r = bank_fill_color[0]
        g = bank_fill_color[1]
        b = bank_fill_color[2]
        self.set_draw_color(225, 225, 225)
        self.set_fill_color(r, g, b)
        self.set_text_color(28, 28, 28)
        # pdf.set_margins(left=0, top=0)
        # Thickness of frame (1 mm)
        self.set_xy(0, 0)
        self.set_line_width(0)

        self.set_font("helvetica", "B", 20)
        self.cell(250, 50, "", 0, 0, fill=1, align="L")
        # padding
        self.image(self.account.bank.logo_img.path, 12, 13, 15)

        self.cell(90, 10, "", border=0, ln=1, align="R")
        self.cell(
            189,
            10,
            f"{self.account.profile.customer.first_name} {self.account.profile.customer.last_name}",
            ln=1,
            border=0,
            align="R",
        )
        self.set_font("helvetica", "", 10)
        # self.cell(199, 11, '20 Aladelola Close, Admiralty Way,Lekki Phase 1, Lagos', ln=1, border=0, align='R')

        # self.cell(120, 100, {name})
        self.ln(14)
        # self.cell(20)
        # self.set_text_color(255,255,255)
        self.set_font("Arial", "B", 8)
        self.cell(24, 5, "Account number:", ln=0, border=0)
        self.cell(24, 5, self.account.account_number, ln=0, border=0)
        self.cell(20, 5, "Account Type:", ln=0, border=0)
        self.cell(27, 5, f"{self.account.account_type}", ln=0, border=0)
        self.cell(25, 5, "Opening Balance:", ln=0, border=0)
        self.cell(24, 5, f"{self.start_balance}", ln=0, border=0)
        self.cell(11, 5, "Period :", ln=0, border=0)
        self.cell(80, 5, f"{self.start_date} - {self.end_date}", ln=1, border=0)
        self.ln(8)

        self.set_font("times", "", 10)
        # self.cell(20)
        # self.set_font('Arial', 'I', 10)
        # self.set_text_color(28, 28, 28)
        self.cell(28, 15, "Date", ln=0, border=0)
        self.cell(35, 15, "Transactional type", ln=0, border=0)
        self.cell(60, 15, "Reference ID", ln=0, border=0)
        self.cell(30, 15, "Debit", ln=0, border=0)
        self.cell(25, 15, "Credit", ln=0, border=0)
        self.cell(30, 15, "Balance", ln=1, border=0)

    def footer(self):
        # set position of the footer
        self.set_y(-12)
        # set font
        self.set_font("helvetica", "I", 7)
        # page number
        self.cell(3, 10, f"Page {self.page_no()}/{{nb}}", align="C")
        self.cell(0, 10, f"{self.bank_name}", align="C")

    def hex_to_rgb(self):
        # convert hex string colour to RGB
        colour = self.colour.lstrip("#")
        colour_length = len(colour)
        return list(
            int(colour[i : i + colour_length // 3], 16)
            for i in range(0, colour_length, colour_length // 3)
        )


def build_account_statement_pdf(account: CustomerAccount, data):
    """
    Builds and returns pdf document of account statement
    """
    customer = account.profile.customer
    now = datetime.now()
    time = now.strftime("%I-%M-%S%p")
    date = now.strftime("%b%d%Y")
    file_name = (
        f"{customer.first_name}_{customer.last_name}_Statement_{date}_{time}.pdf"
    )

    pdf = PDF(
        account=account,
        start_balance=data.get("start_balance"),
        end_balance=data.get("end_balance"),
        start_date=data.get("start_date"),
        end_date=data.get("end_date"),
        orientation="P",
        unit="mm",
        format="Letter",
    )

    pdf.set_draw_color(0, 80, 180)
    pdf.set_fill_color(20, 50, 200)
    pdf.set_text_color(28, 28, 28)
    # get total page numbers
    pdf.alias_nb_pages()

    pdf.set_auto_page_break(
        auto=1,
        margin=1,
    )

    pdf.add_page()

    statement = data.get("statement")
    for data in statement:
        pdf.cell(28, 15, data.get("valueDate"), ln=0, border=0)
        pdf.cell(35, 15, data.get("description"), ln=0, border=0)
        pdf.cell(60, 15, data.get("tnxRef"), ln=0, border=0)
        pdf.cell(30, 15, data.get("debitAmount"), ln=0, border=0)
        pdf.cell(25, 15, data.get("creditAmount"), ln=0, border=0)
        pdf.cell(30, 15, data.get("closingBalance"), ln=1, border=0)
        pdf.ln(1)

    pdf.output(file_name, "F")
    return file_name


def build_account_statement_excel(account: CustomerAccount, data):
    """
    Builds and returns excel documet of account statement
    """
    log = logging.getLogger("django")
    log.info("Entering Build account statement excel")
    customer = account.profile.customer
    now = datetime.now()
    time = now.strftime("%I-%M-%S%p")
    date = now.strftime("%b%d%Y")
    file_name = (
        f"{customer.first_name}_{customer.last_name}_Statement_{date}_{time}.xlsx"
    )
    workbook = xlsxwriter.Workbook(file_name)

    # Add a bold format to use to highlight cells.
    bold = workbook.add_format({"bold": True})

    merge_format = workbook.add_format(
        {
            "bold": 1,
            "border": 0,
            "align": "center",
            "valign": "vcenter",
            "font_size": 20,
        }
    )
    #'fg_color': 'yellow',
    cell_format = workbook.add_format()
    cell_format.set_text_wrap()
    cell_format.set_align("top")
    cell_format.set_align("left=")

    name = f"{customer.first_name} {customer.last_name}"
    start_balance = data.get("start_balance")
    # end_balance = data.get("end_balance")
    start_date = data.get("start_date")
    end_date = data.get("end_date")

    money_format = workbook.add_format({"num_format": "₦0,000.00"})
    moneyRedformat = workbook.add_format({"num_format": "₦0,000.00"})
    moneyRedformat.set_font_color("red")

    worksheet = workbook.add_worksheet("Account_Statement")
    worksheet.insert_image(
        "A1",
        "SMB logo.jpeg",
        {
            "x_scale": 0.1,
            "y_scale": 0.1,
            "x_offset": 15,
            "y_offset": 10,
            "object_position": 3,
        },
    )
    # Widen the first column to make the text clearer.
    worksheet.set_column("A:F", 35)
    worksheet.merge_range("A1:F4", f"{name}", merge_format)

    worksheet.write("A5", "Account number:" + f"{account.account_number}", bold)
    worksheet.write("B5", "Account Type:" + f"{account.account_type}", bold)
    worksheet.write("C5", "Opening Balance:" + f"{start_balance}", bold)
    worksheet.write("D5", "Period :" + f"{start_date} - {end_date}", bold)
    worksheet.merge_range("A6:F6", "", merge_format)

    worksheet.write("A7", "Date", bold)
    worksheet.write("B7", "Transactional type", bold)
    worksheet.write("C7", "Reference ID", bold)
    worksheet.write("D7", "Debit", bold)
    worksheet.write("E7", "Credit", bold)
    worksheet.write("F7", "Balance", bold)

    rowindex = 8
    statement1 = data.get("statement")
    for data in statement1:
        """date= data.get("valueDate")
        description= data.get("description")
        tnxRef= data.get("tnxRef")
        debitAmount= data.get("debitAmount")
        creditAmount= data.get("creditAmount")
        closingBalance= data.get("closingBalance")"""

        worksheet.write("A" + str(rowindex), data.get("valueDate"), cell_format)
        worksheet.write("B" + str(rowindex), data.get("description"), cell_format)
        worksheet.write("C" + str(rowindex), data.get("tnxRef"), cell_format)
        worksheet.write("D" + str(rowindex), data.get("debitAmount"), money_format)
        worksheet.write("E" + str(rowindex), data.get("creditAmount"), money_format)
        if data.get("closingBalance") < str(0):
            worksheet.write(
                "F" + str(rowindex), data.get("closingBalance"), moneyRedformat
            )
        else:
            worksheet.write(
                "F" + str(rowindex), data.get("closingBalance"), money_format
            )

        rowindex += 1
        rowindex = len(statement1)

    # Insert an image.
    # worksheet.insert_image('B5', 'SMB logo.jpeg')

    workbook.close()
    return file_name


def process_account_statement(
    account: CustomerAccount,
    start_date: datetime,
    end_date: datetime,
    file_type: str,
    email: str,
):
    """
    Process retrieval as well as building statement document
    """
    log = logging.getLogger("django")
    log.info("Entering Process account flow")
    resp = retrieve_account_statement(
        account.bank,
        account.account_number,
        start_date.strftime("%Y%m%d"),
        end_date.strftime("%Y%m%d"),
    )

    if resp.get("response_code") == settings.FAILED_RESPONSE:
        log.info(f"Error processing account statement, resp -> {resp}")
        return

    # send email containing file to customer
    message = f"Kindly find attached your account statement report. Kindly review."
    email_args = {
        "email_from": account.bank.email,
        "to": email,
        "subject": "Account Statement Report",
        "admin_name": f"{account.profile.customer.first_name}".upper(),
        "message": message,
        "img_url": settings.EMAIL_DEFAULT_IMAGE_URL,
        "foreground": settings.EMAIL_DEFAULT_FOREGROUND,
        "background": settings.EMAIL_DEFAULT_BACKGROUND,
    }

    if file_type == "pdf":
        # build pdf file
        file_name = build_account_statement_pdf(
            account,
            {
                **resp.get("response_detail"),
                "start_date": start_date.strftime("%Y-%b-%d"),
                "end_date": end_date.strftime("%Y-%b-%d"),
            },
        )
        email_args["file_type"] = "application/pdf"
    else:
        file_name = build_account_statement_excel(
            account,
            {
                **resp.get("response_detail"),
                "start_date": start_date.strftime("%Y-%b-%d"),
                "end_date": end_date.strftime("%Y-%b-%d"),
            },
        )
        email_args[
            "file_type"
        ] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

    email_args["file_path"] = file_name
    email_args["file_name"] = file_name

    log.info(f"email args -> {email_args}")
    resp = send_email(email_args, "STATEMENT")
    os.remove(file_name)
