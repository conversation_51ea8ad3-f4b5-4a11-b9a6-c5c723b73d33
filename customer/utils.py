from datetime import date, datetime
from threading import Thread
from common.functions import update_daily_cummulative
from transactions.models import CoreBankingTransactions, SendMoney, Transaction
from common import services
# from logic_omnichannel import settings
from django.conf import settings
from django.db.models import Q


def retrieve_update_core_banking_transactions(account, app_code, token=None):
    """
    try to retrieve new core banking transactions from the core banking service and update it
    in the local core banking transaction database storage.
    We take into consideration date so we do not have to make large date range calls to the core
    banking service
    """
    core_banking_transactions = CoreBankingTransactions.objects.filter(
        sender_account=account
    ).order_by("-date_created")
    if len(core_banking_transactions) == 0:
        # at this point, it is either the customer is a new customer or the customer has
        # not made any transaction which has been saved to the db yet
        # so we query from the current day till the day the customer account was created
        date_range = {
            "end_date": f'{date.today().strftime("%Y-%m-%d")}',
            "start_date": f'{account.date_created.strftime("%Y-%m-%d")}',
        }
    else:
        # at this point the customer has some transactions stored so we query from the
        # current day till the day of the last stored transaction there by reducing the
        # amount of data gotten back from the core banking service
        date_range = {
            "end_date": f'{date.today().strftime("%Y-%m-%d")}',
            "start_date": f'{core_banking_transactions[0].date_created.strftime("%Y-%m-%d")}',
        }
    resp = services.get_account_transactions(
        date_range, account.account_number, app_code, account.bank.code
    )
    if resp.get("response_code") == settings.SUCCESS_RESPONSE:
        transactions = resp.get("transactions", [])
        for transaction in transactions:
            date_created = datetime.strptime(transaction.get("date"), "%Y-%m-%d")
            (
                core_banking_transaction,
                created,
            ) = CoreBankingTransactions.objects.get_or_create(
                sender_account=account,
                type=transaction.get("type"),
                payment_ref=transaction.get("paymentReference"),
                transaction_ref=transaction.get("transactionReference"),
                description=transaction.get("description"),
                amount=f'{transaction.get("amount")}',
                narration=transaction.get("narration"),
                core_banking_date=date_created,
            )
            if core_banking_ref := transaction.get("transactionReference"):
                # This means the transaction was generated from omni channel platform
                # so we find that transaction reference and map it the corresponding core banking transaction
                omni_channel_generated_transaction = Transaction.objects.filter(
                    Q(send_money__core_banking_ref=core_banking_ref)
                    | Q(airtime_data__core_banking_ref=core_banking_ref)
                    | Q(bill_payment__core_banking_ref=core_banking_ref)
                    | Q(international_send_money__core_banking_ref=core_banking_ref)
                )
                if omni_channel_generated_transaction:
                    omni_channel_generated_transaction = (
                        omni_channel_generated_transaction[0]
                    )
                    if isinstance(
                        omni_channel_generated_transaction.content_object, SendMoney
                    ):
                        # it is possible that the omni channel generated transaction was saved as pending or failed due to
                        # system / network glitch but the transaction was infact successful, we fix our internal db at this point
                        transaction_object = (
                            omni_channel_generated_transaction.content_object
                        )
                        if transaction_object.sender_account != account:
                            # this situation occurs for edge case scenario where is sending from one of his/her
                            # account to the other within the same bank we just halt the remainder flow
                            continue
                        if (
                            transaction_object.status != "S"
                            and core_banking_transaction.type == "Debit"
                        ):
                            transaction_object.status = "S"
                            transaction_object.is_resolved = (
                                False if transaction_object.fee else True
                            )
                            transaction_object.is_transaction_chargable = (
                                True if transaction_object.fee else False
                            )
                            transaction_object.response_code = settings.SUCCESS_RESPONSE
                            transaction_object.response_description = (
                                "Transaction Successful"
                            )
                            transaction_object.save()

                        # it is also possible that a transaction was debited and then credit back causing the reversal problem so in this case we
                        # we need to mark that transaction has reversed and update our internal db as well
                        if (
                            transaction_object.status == "S"
                            and core_banking_transaction.type == "Credit"
                        ):
                            if (
                                transaction_object.response_code
                                == settings.SUCCESS_RESPONSE
                            ):
                                """
                                case scenario where account was debited and the daily cummulative as well is also
                                adjusted we will have to subtract the reversal from the daily cummulative
                                """
                                Thread(
                                    target=update_daily_cummulative,
                                    args=(
                                        (transaction_object.amount * -1),
                                        transaction_object.sender_account,
                                    ),
                                ).start()
                            transaction_object.status = "R"
                            transaction_object.is_resolved = True
                            transaction_object.is_transaction_chargable = False
                            transaction_object.response_code = settings.FAILED_RESPONSE
                            transaction_object.response_description = (
                                "Transaction Reversed"
                            )
                            transaction_object.save()

                    core_banking_transaction.omni_channel_generated_transaction = (
                        omni_channel_generated_transaction
                    )
                    core_banking_transaction.save()
            if created and core_banking_transaction.type.lower() == "credit":
                # we check for newly created core banking credit transactions then we request for an
                # update in account balance for the particular account
                if token:
                    services.update_balance_enquiry(
                        token, account.account_number, retry=True
                    )
        return {
            "response_code": settings.SUCCESS_RESPONSE,
            "response_description": "Retrieve, Update Successful",
        }
    else:
        return {
            "response_code": settings.FAILED_RESPONSE,
            "response_description": f'Unable to Update Records -> {resp.get("response_description")}',
        }
