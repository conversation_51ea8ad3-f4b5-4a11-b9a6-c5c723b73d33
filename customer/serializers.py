from datetime import datetime
from bank.models import Bank
from common.functions import get_amount_in_words, get_bank_name_by_code
from common.validators import validate_numbers
from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

from .models import CustomerAccount, KYCDocs
from .models import CustomerAccount
from transactions.models import (
    AirtimeData,
    BillPayment,
    CoreBankingTransactions,
    InternationalSendMoney,
    SendMoney,
    Transaction,
    Collection,
)
import logging


class AddCustomerAccountSerializer(serializers.ModelSerializer):
    validate_otp_at = serializers.CharField(max_length=11, required=True)
    sms_otp = serializers.CharField(max_length=7, required=True)

    class Meta:
        model = CustomerAccount
        fields = ['account_number', 'bank', 'account_type', 'validate_otp_at', 'sms_otp']
    
    def create(self, validated_data, profile=None):
        if profile is None:
            raise ValidationError('Profile Does not exist')
        account_number = validated_data['account_number']
        bank = validated_data['bank']
        account_type = validated_data['account_type']
        account = CustomerAccount.objects.create(profile=profile, account_number=account_number, bank=bank, account_type=account_type)
        return account


class VerifyCustomerAccountSerializer(serializers.Serializer):

    account_number = serializers.CharField(min_length=10, max_length=10, required=True, validators=[validate_numbers])
    institution_code = serializers.CharField(min_length=3, max_length=15, required=True)

    class Meta:
        fields = ['account_number', 'institution_code']


class UpdateCustomerProfileSerializer(serializers.Serializer):

    email = serializers.EmailField(required=True, allow_null=True, allow_blank=True)

    class Meta:
        fields = ['email']
    
    def update(self, instance, validated_data):
        email = validated_data.get('email')

        if email:
            if instance.customerprofile.is_email_verified:
                raise Exception('Email has been verified and cannot be changed')
            instance.username = email
            instance.email = email
            try:
                instance.save()
            except Exception:
                raise Exception('User with this email already exist. Please confirm this email is valid')
        return instance


class SetPinSerializer(serializers.Serializer):

    pin = serializers.CharField(min_length=4, write_only=True, validators=[validate_numbers])
    password = serializers.CharField(min_length=5, required=False)
    class Meta:
        fields = ['pin', 'password']


class GetTransactionsSerializer(serializers.Serializer):

    user_ref = serializers.SerializerMethodField('get_user_ref')
    transaction_ref = serializers.SerializerMethodField('get_transaction_ref')
    amount = serializers.SerializerMethodField('get_amount')
    currency = serializers.SerializerMethodField('get_currency')
    channel = serializers.SerializerMethodField('get_channel')
    transaction_type = serializers.SerializerMethodField('get_transaction_type')
    transaction_class = serializers.SerializerMethodField('get_transaction_class')
    source_account_name = serializers.SerializerMethodField("get_source_account_name")
    source_account_number = serializers.SerializerMethodField('get_source_account')
    source_institution_code = serializers.SerializerMethodField('get_source_institution_code')
    source_institution_name = serializers.SerializerMethodField('get_source_institution_name')
    meta_data = serializers.SerializerMethodField('get_meta_data')
    remark = serializers.SerializerMethodField('get_remark')
    response_code = serializers.SerializerMethodField('get_response_code')
    response_description = serializers.SerializerMethodField('get_response_description')
    date_created = serializers.SerializerMethodField('get_date_created')
    status = serializers.SerializerMethodField('get_status')

    class Meta:
        fields = [
            "user_ref",
            "transaction_ref",
            "amount",
            "currency",
            "channel",
            "transaction_type",
            "transaction_class",
            "source_account_name",
            "source_account_number",
            "source_institution_code",
            "source_institution_name",
            "meta_data",
            "remark",
            "response_code",
            "response_description",
            "date_created",
            "status",
        ]

    def get_user_ref(self, obj):
        return obj.content_object.user_ref

    def get_transaction_ref(self, obj):
        return obj.content_object.transaction_ref

    def get_amount(self, obj):
        return obj.content_object.amount
    
    def get_currency(self, obj):
        try:
            return obj.content_object.currency.symbol
        except:
            log = logging.getLogger("django")
            log.info(
                f"Error with retrieve currency for content object -> {obj.content_type}, please review and fix this transaction -> {obj}"
            )
            return ""

    def get_channel(self, obj):
        return obj.content_object.channel
    
    def get_transaction_type(self, obj):
        return obj.content_object.transaction_type.name

    def get_transaction_class(self, obj):
        if isinstance(obj.content_object, SendMoney):
            return 'DEBIT'
        elif isinstance(obj.content_object, AirtimeData):
            return 'DEBIT'
        elif isinstance(obj.content_object, BillPayment):
            return 'DEBIT'
        elif isinstance(obj.content_object, InternationalSendMoney):
            return "DEBIT"

    def get_source_account_name(self, obj):
        try:
            customer = obj.content_object.agent.user
        except Exception:
            customer = obj.content_object.sender.customer
        return f"{customer.first_name} {customer.last_name}"

    def get_source_account(self, obj):
        try:
            source_account = obj.content_object.sender_account.account_number
        except Exception:
            source_account = obj.content_object.agent_account.account_number
        return source_account
      
    def get_source_institution_code(self, obj):
        try:
            institution_code = obj.content_object.sender_account.bank.code
        except Exception:
            institution_code = obj.content_object.agent_account.bank.code
        return institution_code
    
    def get_source_institution_name(self, obj):
        try:
            bank = get_bank_name_by_code(obj.content_object.sender_account.bank.code)
        except Exception:
            bank = get_bank_name_by_code(obj.content_object.agent_account.bank.code)
        return bank 

    def get_meta_data(self, obj):
        if isinstance(obj.content_object, SendMoney):
            return {
                'destination_account_number': obj.content_object.destination_account_number,
                'destination_institution_code': obj.content_object.destination_institution_code, 
                'destination_account_name': obj.content_object.destination_account_name,
                'destination_institution_name': get_bank_name_by_code(obj.content_object.destination_institution_code)
            }
        elif isinstance(obj.content_object, AirtimeData):
            return {
                'biller': obj.content_object.biller.biller_name,
                'credited_phone_number' : obj.content_object.phone_number
            }
        elif isinstance(obj.content_object, BillPayment):
            return {
                "biller": obj.content_object.biller_item.biller_name,
                "category": obj.content_object.biller_category.name,
                "beneficiary_unique_number": obj.content_object.customer_field_1,
                "beneficiary_name": obj.content_object.customer_field_2,
                "recharge_pin": obj.content_object.recharge_pin,
            }
        elif isinstance(obj.content_object, InternationalSendMoney):
            details = {}
            if obj.content_object.payout_type == "bank_account":
                details = {
                    "payout_type": obj.content_object.payout_type,
                    "beneficiary_account_number": obj.content_object.reciever_account_number,
                    "beneficiary_name": obj.content_object.reciever_account_name,
                    "beneficiary_bank": obj.content_object.reciever_mfs_institution_code,
                    "beneficiary_country": obj.content_object.reciever_country.name,
                    "recieved_amount_in_beneficiary_currency": obj.content_object.omni_reciever_amount,
                }
            return details
    
    def get_remark(self, obj):
        return obj.content_object.remark
        
    def get_response_code(self, obj):
        return obj.content_object.response_code
        
    def get_response_description(self, obj):
        return obj.content_object.response_description
        
    def get_date_created(self, obj):
        return obj.content_object.date_created
        
    def get_status(self, obj):
        return obj.content_object.status

class GetAccountTransactionSerializer(serializers.Serializer):

    transaction_details = serializers.SerializerMethodField("get_transaction_details")
    beneficiary_details = serializers.SerializerMethodField("get_beneficiary_details")
    sender_details = serializers.SerializerMethodField("get_sender_details")

    class Meta:
        fields = ["transaction_details", "beneficiary_details", "sender_details"]

    def get_transaction_details(self, obj):

        def retrieve_details(transaction):
            if (
                    isinstance(transaction, SendMoney)
                    or isinstance(transaction, AirtimeData)
                    or isinstance(transaction, BillPayment)
                    or isinstance(transaction, Collection)
                    or isinstance(transaction, InternationalSendMoney)
                ):
                    return {
                        "transaction_date": transaction.date_created,
                        "transaction_class": "DEBIT",
                        "transaction_type": transaction.transaction_type.name,
                        "tsq_type": transaction.transaction_type.code,
                        "transaction_status": transaction.status,
                        "amount": transaction.amount,
                        "amount_in_words": get_amount_in_words(f"{transaction.amount}"),
                        "transaction_description": transaction.response_description,
                        "transaction_narration": transaction.remark,
                        "customer_reference": transaction.user_ref,
                        "channel": transaction.channel,
                    }

        if isinstance(obj, Transaction):
            # This is definitely an omni channel transaction
            transaction = obj.content_object
            return retrieve_details(transaction)

        if isinstance(obj, CoreBankingTransactions):

            if obj.transaction_ref and obj.omni_channel_generated_transaction is not None:
                # This means the transaction originated from omni channel so we can find which kind of omni channel
                transaction = obj.omni_channel_generated_transaction.content_object
                return retrieve_details(transaction)
            return {
                "transaction_date": obj.core_banking_date,
                "transaction_class": "CREDIT" if obj.type == "Credit" else "DEBIT",
                "transaction_type": obj.description,
                "transaction_status": "S",  # since it is from the core banking it is assumed to always be successful
                "amount": obj.amount,
                "amount_in_words": get_amount_in_words(f"{obj.amount}"),
                "transaction_description": obj.description,
                "transaction_narration": obj.narration,
                "customer_reference": "",
                "channel": "",
            }
        return {}

    def get_beneficiary_details(self, obj):

        def retrieve_details(transaction):

            if isinstance(transaction, SendMoney):
                return {
                    "beneficiary_account_number": transaction.destination_account_number,
                    "beneficiary_name": transaction.destination_account_name,
                    "beneficiary_bank": get_bank_name_by_code(
                        transaction.destination_institution_code
                    ),
                }
            elif isinstance(transaction, Collection):
                return {
                    "beneficiary_account_number": transaction.customer_account_number,
                    "beneficiary_name": transaction.customer_account_name,
                    "beneficiary_bank": transaction.bank.name
                }
            elif isinstance(transaction, AirtimeData):
                return {
                    "biller": transaction.biller.biller_name,
                    "credited_phone_number": transaction.phone_number,
                }
            elif isinstance(transaction, BillPayment):
                return {
                    "biller": transaction.biller_item.biller_name,
                    "category": transaction.biller_category.name,
                    "beneficiary_unique_number": transaction.customer_field_1,
                    "beneficiary_name": transaction.customer_field_2,
                    "recharge_pin": transaction.recharge_pin,
                }
            elif isinstance(transaction, InternationalSendMoney):
                details = {}
                if transaction.payout_type == "bank_account":
                    details = {
                        "payout_type": transaction.payout_type,
                        "beneficiary_account_number": transaction.reciever_account_number,
                        "beneficiary_name": transaction.reciever_account_name,
                        "beneficiary_bank": transaction.reciever_mfs_institution_code,
                        "beneficiary_country": transaction.reciever_country.name,
                        "recieved_amount_in_beneficiary_currency": transaction.omni_reciever_amount,
                    }
                return details

        if isinstance(obj, Transaction):
            transaction = obj.content_object
            return retrieve_details(transaction)

        if isinstance(obj, CoreBankingTransactions):

            if obj.transaction_ref and obj.omni_channel_generated_transaction is not None:
                # This means the transaction originated from omni channel so we can find which kind of omni channel
                transaction = obj.omni_channel_generated_transaction.content_object
                return retrieve_details(transaction)
        return {}

    def get_sender_details(self, obj):
        def retrieve_details(transaction):
            if (
                isinstance(transaction, AirtimeData)
                or isinstance(transaction, BillPayment)
                or isinstance(transaction, InternationalSendMoney)
            ):
                return {
                    "sender_account_number": transaction.sender_account.account_number,
                    "sender_name": transaction.sender_account.profile.customer.get_full_name(),
                    "sender_bank": transaction.sender_account.bank.name,
                }
            elif isinstance(transaction, Collection):
                return {
                    "agent_account": transaction.agent_account.account_number,
                    "agent_name": transaction.agent.get_full_name(),
                    "agent_bank": transaction.agent.bank.name,
                }
            elif isinstance(transaction, SendMoney):
                if transaction.channel == "DFA OAuth":
                    return {
                        "agent_account": transaction.agent_account.account_number,
                        "agent_name": transaction.agent.get_full_name(),
                        "agent_bank": transaction.agent.bank.name,
                    }
                elif transaction.channel.upper().startswith("3RD-PARTY"):
                    return {
                        "sender_account_number": transaction.sender_account_third_party,
                        "sender_bank":  get_bank_name_by_code(
                            transaction.destination_institution_code),
                    }
                else:
                    return {
                        "sender_account_number": transaction.sender_account.account_number,
                        "sender_name": transaction.sender_account.profile.customer.get_full_name(),
                        "sender_bank": transaction.sender_account.bank.name,
                    }
        
        if isinstance(obj, Transaction):
            transaction = obj.content_object
            return retrieve_details(transaction)
        
        if isinstance(obj, CoreBankingTransactions):
            if obj.transaction_ref and obj.omni_channel_generated_transaction is not None:
                # This means the transaction originated from omni channel so we can find which kind of omni channel
                transaction = obj.omni_channel_generated_transaction.content_object
                return retrieve_details(transaction)

        return {}

class GetAccountTransactionSerializerV2(serializers.Serializer):
    transaction_details = serializers.SerializerMethodField("get_transaction_details")
    beneficiary_details = serializers.SerializerMethodField("get_beneficiary_details")
    sender_details = serializers.SerializerMethodField("get_sender_details")

    def normalize_datetime(self, date_str):
        try:
            # Try parsing with time
            dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            # If no time is provided, default to 00:00:00
            dt = datetime.strptime(date_str, "%Y-%m-%d")
            dt = dt.replace(hour=0, minute=0, second=0)

        # Format with full year and full time
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    
    def get_transaction_details(self, obj):
        return {
            "transaction_date": self.normalize_datetime(obj.get("transaction_date")) or "",
            "transaction_class": obj.get("record_type").upper() or "",
            "transaction_type": obj.get("transaction_type") or "",
            "tsq_type": obj.get("tsq_type") or "",
            "transaction_status": obj.get("transaction_status") or "",
            "amount": obj.get("amount"),
            "amount_in_words": get_amount_in_words(f"{obj.get('amount')}"),
            "transaction_description": "Transaction Successful",
            "transaction_narration": obj.get("narration") or "",
            "customer_reference": obj.get("customer_reference") or "",
            "channel": obj.get("channel") or "",
        }
    
    def get_beneficiary_details(self, obj):
        transaction_type = obj.get("transaction_type")
        if transaction_type == "SEND MONEY":

            return {
                "beneficiary_account_number": obj.get("beneficiary_account_number"),
                "beneficiary_name": obj.get("beneficiary_name"),
                "beneficiary_bank": obj.get("beneficiary_bank"),
            }
        
        elif transaction_type == "AIRTIME/DATA":
            return {
                "biller": obj.get("biller"),
                "credited_phone_number": obj.get("credited_phone_number"),
            }
        
        elif transaction_type == "BILL PAYMENT":
            return {
                "biller": obj.get("biller"),
                "category": obj.get("category"),
                "beneficiary_unique_number": obj.get("beneficiary_unique_number"),
                "beneficiary_name": obj.get("beneficiary_name"),
                "recharge_pin": obj.get("recharge_pin"),
                }

    def get_sender_details(self, obj):
        return {
            "sender_account_number": obj.get("sender_account_number") or "",
            "sender_name": obj.get("sender_name") or "",
            "sender_bank": obj.get("sender_bank") or "",
        }


class KYCDocSerializer(serializers.ModelSerializer):
    class Meta:
        model = KYCDocs
        fields = [
            "means_of_id",
            "id_base_64",
            "id_issue_date",
            "id_expiry_date",
            "id_number",
            "signature_base_64",
            "profile_picture_base_64",
        ]
    
    def validate(self, attrs):
        validated_data = super().validate(attrs)

        means_of_id = validated_data.get('means_of_id')
        id_base_64 = validated_data.get('id_base_64')
        id_number = validated_data.get('id_number')

        # means_of_id, id_base_64 and id_number must all exist together
        if means_of_id or id_base_64 or id_number:
            # if any of this exists, all must exist
            if not (means_of_id and id_base_64 and id_number):
                error_dict = {}
                if not means_of_id:
                    error_dict['means_of_id'] = _(
                        "means of id must exist along side id image and id number"
                    )
                if not id_base_64:
                    error_dict['id_base_64'] =  _(
                        "id image must exist along side means of id and id number"
                    )
                if not id_number:
                    error_dict['id_number'] = _(
                        "id number must exist along side id image and means of id"
                    )
                raise ValidationError(
                    error_dict
                )

        return validated_data

    def update(self, instance, validated_data):
        means_of_id = validated_data.get("means_of_id")
        id_base_64 = validated_data.get("id_base_64")
        id_issue_date = validated_data.get("id_issue_date")
        id_expiry_date = validated_data.get("id_expiry_date")
        id_number = validated_data.get("id_number")
        signature_base_64 = validated_data.get("signature_base_64")
        profile_picture_base_64 = validated_data.get("profile_picture_base_64")

        if means_of_id is not None:
            instance.means_of_id = means_of_id
        if id_base_64 is not None:
            instance.id_base_64 = id_base_64
        if id_issue_date is not None:
            instance.id_issue_date = id_issue_date
        if id_expiry_date is not None:
            instance.id_expiry_date = id_expiry_date
        if id_number is not None:
            instance.id_number = id_number
        if signature_base_64 is not None:
            instance.signature_base_64 = signature_base_64
        if profile_picture_base_64 is not None:
            instance.profile_picture_base_64 = profile_picture_base_64

        instance.save()

        return instance
