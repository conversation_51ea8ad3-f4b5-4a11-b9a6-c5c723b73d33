import logging
from django.core.cache import cache
from oauth2_provider.models import AccessToken
from django.utils import timezone

from logic_omnichannel.celery import app
from common.services import update_balance_enquiry


@app.task(name='clear_balance_enquiry_cache')
def clear_balance_enquiry_cache():
    '''
        Gets the balance enquiry cache and set it to an empty dictionary
    '''
    log = logging.getLogger('django')
    log.info('FROM CELERY -> Running clear balance enquiry cache')
    try:
        balance_enquiry_cache = cache.get("balance_enquiry_dict")
        if balance_enquiry_cache:
            log.info('FROM CELERY -> Clearing Balance Enquiry Cache')
            cache.set("balance_enquiry_dict", {})
            log.info('FROM CELERY -> Balance Enquiry Cache cleared successfully')
        else:
            log.info('FROM CELERY -> Balance Enquiry Cache is already Empty')
    except Exception as e:
        log.info(f'FROM CELERY -> Error Occurred while running Clear Balance Enquiry Cache Function {e}')


@app.task(name='update_balance_enquiry_state')
def update_balance_enquiry_state(state=True):
    '''
        With default state set to True, This function changes the state of all active token sessions to True
        to allow for a refresh in balance enquiry from the core banking
    '''
    log = logging.getLogger('django')
    log.info('FROM CELERY -> Running Update Balance Enquiry State for Active Sessions')
    try:
        now = timezone.now()
        active_sessions = AccessToken.objects.filter(expires__gt=now)
        if active_sessions:
            count = len(active_sessions)
            log.info(f'FROM CELERY -> About to Update Balance Enquiry State for {count} Active Session(s)')
            for session in active_sessions:
                # conditon update balance enquiry for tokens from mobile app, internet banking and ussd
                if session.application.name in ['Mobile Application OAuth', 'USSD Application OAuth', 'Browser Banking Application OAuth']:
                    update_balance_enquiry(token=session.token, retry=True)
            log.info(f'FROM CELERY -> Successfully Updated Balance Enquiry State for {count} Active Session(s)')
        else:
            log.info('FROM CELERY -> No Active Session to Update')
    except Exception as e:
        log.info(f'FROM CELERY -> Error Occurred while running Update Balance Enquiry State Function {e}')