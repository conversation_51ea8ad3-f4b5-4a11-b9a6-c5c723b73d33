import logging
from threading import Thread
from datetime import datetime, timedelta
import os

import io
import xlwt
import xlsxwriter
from django.core.exceptions import ValidationError, ObjectDoesNotExist
from django.core.files import File
from rest_framework.response import Response
from django.http import HttpResponse
from common.functions import (
    set_response_data,
    get_client_credentials,
    get_platform,
    get_week_of_month,
    get_bank_name_by_code,
)
from common.services import send_email
from bank.models import Bank
from customer.models import CustomerAccount, CustomerProfile
from transactions.models import SendMoney, AirtimeData, BillPayment
from rest_framework import generics, status, viewsets
from oauth2_provider.contrib.rest_framework import permissions
from rest_framework.permissions import AllowAny
from django.db.models.query import QuerySet
from django.db.models import Sum

from .serializers import (
    ReportConfigurationPostSerializer,
    ReportConfigurationSerializer,
    ReportSerializer,
)
from .models import ReportConfiguration, Report
# from logic_omnichannel import settings
from django.conf import settings

# Create your views here.


class ReportConfigViewSet(viewsets.ModelViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = ReportConfigurationPostSerializer
    queryset = ReportConfiguration.objects.all()

    def get_queryset(self, bank_admin=None):
        """
        return queryset containing all report config created by bank admin
        """
        if bank_admin:
            queryset = ReportConfiguration.objects.filter(
                partner_bank_admin=bank_admin
            ).order_by("-id")
            return queryset
        else:
            return super().get_queryset()

    def list(self, request, *args, **kwargs):
        """
        Returns a paginated list of all report config set by bank super admin user
        """

        try:

            bank_admin = request.auth.user.bankadmin
            if not bank_admin.is_bank_superadmin:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                    detail="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            queryset = self.filter_queryset(self.get_queryset(bank_admin))

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = ReportConfigurationSerializer(page, many=True)
                serializer = self.get_paginated_response(serializer.data)
            else:
                serializer = ReportConfigurationSerializer(queryset, many=True)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Report Configuration List Request Successful",
                detail=serializer.data,
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Retrieve Report Configuration List {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def create(self, request, *args, **kwargs):
        """
        Get Report Configuration Options for Bank Super Admin. We confirm the signed up
        user is a super admin then we validate the request payload then we save into the
        database
        """
        try:

            bank_admin = request.auth.user.bankadmin
            if not bank_admin.is_bank_superadmin:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                    detail="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            serializer = ReportConfigurationPostSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            serializer.save(
                email=bank_admin.user.username,  # username is email in our user model
                partner_bank_admin=bank_admin,
                partner_bank=bank_admin.bank,
            )

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Report Configuration Settings Set Successfully",
                detail="Report Configuration Settings Set Successfully",
            )
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Request Payload",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Unable to Complete Request",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a single report configuration based on the report configuration ID
        """
        try:

            bank_admin = request.auth.user.bankadmin
            if not bank_admin.is_bank_superadmin:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                    detail="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            instance = self.get_object()
            serializer = self.get_serializer(instance)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Report Configuration Request Successful",
                detail=serializer.data,
            )

            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Retrieve Ticket {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        """
        Update fields in report configurations
        """
        try:
            bank_admin = request.auth.user.bankadmin
            if not bank_admin.is_bank_superadmin:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                    detail="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            partial = kwargs.pop("partial", False)
            instance = self.get_object()
            serializer = ReportConfigurationPostSerializer(
                instance, data=request.data, partial=partial
            )
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)

            if getattr(instance, "_prefetched_objects_cache", None):
                # If 'prefetch_related' has been applied to a queryset, we need to
                # forcibly invalidate the prefetch cache on the instance.
                instance._prefetched_objects_cache = {}

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Report Configuration Updated Successfully",
                detail=serializer.data,
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Update Report Configuration",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def perform_update(self, serializer):
        serializer.save()

    def partial_update(self, request, *args, **kwargs):
        kwargs["partial"] = True
        return self.update(request, *args, **kwargs)


class GetReports(viewsets.GenericViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = ReportSerializer

    def get_queryset(self, bank_admin=None):
        """
        Returns a queryset containing all reports generated for this particular bank admin
        based on pre configurations set
        """
        if bank_admin:
            queryset = Report.objects.filter(
                configuration__partner_bank_admin=bank_admin
            ).order_by("-date_created")
        else:
            queryset = Report.objects.all()
        return queryset

    def list(self, request):
        """
        Returns a paginated list of generated reports assigned to a particular bank admin
        """
        try:
            bank_admin = request.auth.user.bankadmin
            if not bank_admin.is_bank_superadmin:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                    detail="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            queryset = self.filter_queryset(self.get_queryset(bank_admin))

            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                serializer = self.get_paginated_response(serializer.data)
            else:
                serializer = self.get_serializer(queryset, many=True)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Reports List Request Successful",
                detail=serializer.data,
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Complete Request {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, id):
        """
        Retrieve a single report based on the report ID
        """
        try:
            bank_admin = request.auth.user.bankadmin
            if not bank_admin.is_bank_superadmin:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                    detail="Current Role Can Not Perform Function. Contact Super Admin to Adjust Role",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            instance = self.get_object()
            serializer = self.get_serializer(instance)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Report Request Successful",
                detail=serializer.data,
            )

            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Retrieve Report {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GenerateReports(generics.GenericAPIView):

    permission_classes = (AllowAny,)

    def get(self, request):
        """
        Get all report configurations and for each of the configuration based on its
        settings generate a report, save the report and then send the report to the admin
        """
        log = logging.getLogger("django")
        log.info(f"entering GENERATE REPORTS API")

        try:
            """
            Validate request point of entry
            """
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
            log.info(f"FROM GENERATE REPORTS API: Request from {request_platform}")
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Credentials",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            log.info(f"exiting GENERATE REPORTS API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Invalid Client Application",
                "response_detail": "Invalid Basic Auth Header parameter, confirm request header is valid",
            }
            log.info(f"exiting GENERATE REPORTS API with: {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        try:
            report_configurations = ReportConfiguration.objects.all()

            if report_configurations:
                Thread(
                    target=self.generate_reports, args=(report_configurations,)
                ).start()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Report Building Started",
                detail="Reports Generation and Building Started",
            )
            log.info(f"exiting GENERATE REPORTS API with: {data}")
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable Complete Request {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def generate_reports(self, report_configurations):
        """
        runs through the list of report configurations and builds individual reports for each
        saves report into the database and sends report to the configured email
        """
        now = datetime.now()
        date = now.strftime("%a, %d-%b-%Y")
        for configuration in report_configurations:
            try:
                if not self.report_due(now, configuration):
                    # if report is not due based on the configuration and the current time we halt generating the report
                    continue
                report_excel, report_pdf = self.build_report(now, configuration)
                report = Report.objects.create(
                    name=f"{configuration.name} {date}",
                    configuration=configuration,
                    to=configuration.email,
                    admin_name=f"{configuration.partner_bank_admin.first_name} {configuration.partner_bank_admin.last_name}"
                    if configuration.partner_bank_admin
                    else "",
                )

                # try to save excel report into the report model
                with open(report_excel, "rb") as f:
                    report.excel.save(report_excel, File(f))
                report.save()
                # Delete the temporary file
                os.remove(report_excel)

                freq = {"D": "daily", "W": "weekly", "M": "monthly"}
                time = report.date_created.strftime("%I:%M:%S %p")
                date = report.date_created.strftime("%b %d, %Y")

                message = f"Kindly find attached your {configuration.name} {freq[configuration.frequency]} report generated at {time}, (GMT+1)  {date}. Kindly review."
                email_args = {
                    "email_from": settings.DEFAULT_FROM_EMAIL,
                    "to": configuration.email,
                    "subject": "OMNI CHANNEL REPORT",
                    "file_path": f"{report.excel.path}",
                    "file_name": f'{report.excel.name.split("/")[-1]}',
                    "file_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "admin_name": f"{report.admin_name}".upper(),
                    "message": message,
                    "img_url": settings.EMAIL_DEFAULT_IMAGE_URL,
                    "foreground": settings.EMAIL_DEFAULT_FOREGROUND,
                    "background": settings.EMAIL_DEFAULT_BACKGROUND,
                }
                resp = send_email(email_args, "REPORTING")
                if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                    report.sent = True
                    report.save()
            except Exception:
                pass

    def build_report(self, date_time: datetime, configuration: ReportConfiguration):
        """
        Builds report based on report configuration and returns the report
        """
        report_type = configuration.report_type
        if report_type == "send_money":
            transaction = SendMoney
        elif report_type == "airtime_data":
            transaction = AirtimeData
        elif report_type == "bill_payment":
            transaction = BillPayment
        elif report_type == "onboarding":
            transaction = CustomerAccount
        else:
            raise Exception("Invalid Report Type")

        today = date_time.date()
        if configuration.frequency == "D":
            start_date = today - timedelta(days=1)
            end_date = today
        elif configuration.frequency == "W":
            start_date = today - timedelta(days=7)
            end_date = today
        elif configuration.frequency == "M":
            # for monthly events it is vague to say each month difference is 4 weeks so we just change the month
            # numbers while also considering possiblities of the year changing as well
            this_month = today.month
            last_month = (this_month - 1) % 12
            if last_month == 0:
                # this means this month is january and last month is december of the previous year
                start_date = datetime(year=today.year - 1, month=12, day=today.day)
            else:
                start_date = datetime(year=today.year, month=last_month, day=today.day)
            end_date = today
        else:
            raise Exception("Invalid Configuration Frequency")

        if configuration.report_type == "onboarding":
            queryset = transaction.objects.filter(
                bank=configuration.partner_bank,
                date_created__range=[start_date, end_date],
            ).order_by("date_created")
        else:
            queryset = transaction.objects.filter(
                sender_account__bank=configuration.partner_bank,
                date_created__range=[start_date, end_date],
            ).order_by("date_created")

        wb = xlwt.Workbook(encoding="utf-8")
        ws = wb.add_sheet(f"{configuration.name}")
        row_num = 0
        font_style = xlwt.XFStyle()
        font_style.font.bold = True

        if report_type == "send_money":
            # CAREFUL WHEN ADJUSTING
            columns = [
                "user_ref",
                "transaction_ref",
                "sender",
                "amount",
                "fee",
                "transaction_type",
                "channel",
                "currency",
                "sender_account",
                "destination_account_number",
                "destination_institution_code",
                "destination_account_name",
                "core_banking_ref",
                "response_code",
                "response_description",
                "remark",
                "date_created",
                "nam_payment_reference",
                "is_transaction_chargable",
                "status",
            ]

            # CAREFUL WHEN ADJUSTING
            rows = queryset.values_list(
                "user_ref",
                "transaction_ref",
                "sender__customer__username",
                "amount",
                "fee",
                "transaction_type",
                "channel",
                "currency__name",
                "sender_account",
                "destination_account_number",
                "destination_institution_code",
                "destination_account_name",
                "core_banking_ref",
                "response_code",
                "response_description",
                "remark",
                "date_created",
                "nam_payment_reference",
                "is_transaction_chargable",
                "status",
            )
        elif report_type == "airtime_data":
            # CAREFUL WHEN ADJUSTING
            columns = [
                "user_ref",
                "transaction_ref",
                "sender",
                "amount",
                "fee",
                "transaction_type",
                "channel",
                "currency",
                "sender_account",
                "phone_number",
                "biller",
                "payment_code",
                "core_banking_ref",
                "response_code",
                "response_description",
                "remark",
                "date_created",
                "nam_payment_reference",
                "is_transaction_chargable",
                "status",
            ]

            # CAREFUL WHEN ADJUSTING
            rows = queryset.values_list(
                "user_ref",
                "transaction_ref",
                "sender__customer__username",
                "amount",
                "fee",
                "transaction_type",
                "channel",
                "currency__name",
                "sender_account",
                "phone_number",
                "biller__biller_name",
                "payment_code",
                "core_banking_ref",
                "response_code",
                "response_description",
                "remark",
                "date_created",
                "nam_payment_reference",
                "is_transaction_chargable",
                "status",
            )
        elif report_type == "bill_payment":
            # CAREFUL WHEN ADJUSTING
            columns = [
                "user_ref",
                "transaction_ref",
                "sender",
                "amount",
                "fee",
                "transaction_type",
                "channel",
                "currency",
                "sender_account",
                "customer_field_1",
                "customer_field_2",
                "biller_category",
                "biller_item",
                "payment_code",
                "core_banking_ref",
                "response_code",
                "response_description",
                "remark",
                "date_created",
                "nam_payment_reference",
                "is_transaction_chargable",
                "status",
            ]

            # CAREFUL WHEN ADJUSTING
            rows = queryset.values_list(
                "user_ref",
                "transaction_ref",
                "sender__customer__username",
                "amount",
                "fee",
                "transaction_type",
                "channel",
                "currency__name",
                "sender_account",
                "customer_field_1",
                "customer_field_2",
                "biller_category__name",
                "biller_item__biller_item",
                "payment_code" "core_banking_ref",
                "response_code",
                "response_description",
                "remark",
                "date_created",
                "nam_payment_reference",
                "is_transaction_chargable",
                "status",
            )
        elif report_type == "onboarding":
            # CAREFUL WHEN ADJUSTING
            columns = [
                "account_email",
                "account_first_name",
                "account_last_name",
                "account_number",
                "bank",
                "account_type",
                "mobile_channel_access",
                "web_channel_access",
                "ussd_channel_access",
                "daily_limit",
                "status",
                "date_created",
            ]

            # CAREFUL WHEN ADJUSTING
            rows = queryset.values_list(
                "profile__username",
                "profile__first_name",
                "profile__last_name",
                "account_number",
                "bank__name",
                "account_type__name",
                "mobile_channel_access",
                "web_channel_access",
                "ussd_channel_access",
                "daily_limit",
                "status",
                "date_created",
            )

        for column_num, elem in enumerate(columns):
            ws.write(row_num, column_num, elem, font_style)

        font_style = xlwt.XFStyle()

        for row in rows:
            row_num += 1
            for column_num, elem in enumerate(row):
                if column_num == 10 and report_type == "send_money":
                    # this only would work for send money transactions
                    # this is the destination_institution_code we want to convert it to get the destination institution name which is more useful
                    destination_bank_name = get_bank_name_by_code(elem)
                    ws.write(
                        row_num, column_num, str(destination_bank_name), font_style
                    )
                else:
                    ws.write(row_num, column_num, str(elem), font_style)

        # create a temp file to store the excel sheet
        now = datetime.now()
        time = now.strftime("%I-%M-%S%p")
        date = now.strftime("%b%d%Y")
        report_excel = f"{configuration.name}_report_{date}_{time}.xlsx"

        # save into the tmp file
        wb.save(report_excel)
        return (report_excel, None)

    def report_due(self, date_time: datetime, config: ReportConfiguration) -> bool:
        """
        Return True or False if a report is due based on the preset configuration
        """
        if config.frequency == "D":
            # for daily set reports always return True
            return True
        if config.frequency == "W":
            # for weekly set reports we confirm if the current day is valid with the preset config day
            day = date_time.strftime("%a")
            if day.lower() == config.day.lower():
                return True
        if config.frequency == "M":
            # for monthly set reports we confirm if the week and day is valid with the preset config
            date = date_time.date()
            week_num = get_week_of_month(date)

            # map possible week nums to week abbreviations from db model for first, second, third and last weeks of the month
            week_dict = {1: "F", 2: "S", 3: "T", 4: "L", 5: "L"}

            if week_dict[week_num] == config.week:
                # confirm the day is also valid
                day = date_time.strftime("%a")
                if day.lower() == config.day.lower():
                    if week_num == 4:
                        """
                        it is possible for the 4th week not to still be the last week still we verify here
                        if it is indeed the last week. we verify by adding 7 days to the current day, if after
                        7 days we are still in the same month. that means the 4th week is not the last week of
                        the month
                        """
                        next_week = date_time + timedelta(days=7)
                        if next_week.month == date_time.month:
                            # this means in exactly a weeks time we'd still be in the same month making the 4th week not the last week
                            return False
                    return True

        return False


class RetreiveBankDataReport(generics.GenericAPIView):

    permission_classes = (AllowAny,)

    def get(self, request, alias):
        try:
            """
            Retrieves Bank by alias and provides all relevant data mapped to the bank
            """
            output = io.BytesIO()

            bank = Bank.objects.get(alias=alias)
            workbook = xlsxwriter.Workbook(output)
            self.customer_profile(bank, workbook)
            self.customer_account(bank, workbook)
            self.send_money(bank, workbook)
            self.airtime_data(bank, workbook)
            self.bill_payment(bank, workbook)
            workbook.close()
            output.seek(0)
            return HttpResponse(
                output,
                headers={
                    "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "Content-Disposition": f'attachment; filename="{bank.name} Data Report.xlsx"',
                },
            )
        except Bank.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Retrieve Bank with Alias -> {alias}",
                detail="Bank Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable Complete Request {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def customer_profile(self, bank: Bank, workbook: xlsxwriter.Workbook):
        """
        Retrieves bank customer profile data and builds an excel sheet for the
        data
        """

        def get_data(profiles: QuerySet) -> tuple:
            total = profiles.count()
            male = profiles.filter(gender="MALE").count()
            female = profiles.filter(gender="FEMALE").count()
            undefined = total - male - female

            return (total, male, female, undefined)

        def write_data(row: int, profiles: QuerySet):
            for col, elem in enumerate(get_data(profiles)):
                worksheet.write(row, col + 1, elem)

        worksheet = workbook.add_worksheet("CustomerProfile")
        columns = ["ALL", "MALE", "FEMALE", "UNDEFINED"]
        row = 0

        worksheet.set_column(0, 0, len("Number of Created Profiles") * 1.25)

        for col, elem in enumerate(columns):
            worksheet.set_column(col + 1, col + 1, len(elem) * 1.25)

        for col, elem in enumerate(columns):
            worksheet.write(row, col + 1, elem)

        # All Profiles
        profiles = CustomerProfile.objects.filter(customer__app_code=bank.alias)

        row += 1
        worksheet.write(row, 0, "Number of Created Profiles")
        write_data(row, profiles)

        # profiles with verified phone numbers
        profiles = CustomerProfile.objects.filter(
            customer__app_code=bank.alias, is_phone_number_verified=True
        )

        row += 1
        worksheet.write(row, 0, "Number of Verified Phone Number")
        write_data(row, profiles)

        # profiles with verified email address
        profiles = CustomerProfile.objects.filter(
            customer__app_code=bank.alias, is_email_verified=True
        )

        row += 1
        worksheet.write(row, 0, "Number of Verified Email")
        write_data(row, profiles)

        # profiles with fully verified profiles
        profiles = CustomerProfile.objects.filter(
            customer__app_code=bank.alias,
            is_phone_number_verified=True,
            is_email_verified=True,
        )

        row += 1
        worksheet.write(row, 0, "Number of Fully Verified Profiles")
        write_data(row, profiles)

        # profiles with fully verified profiles with No Account
        profiles = CustomerProfile.objects.filter(
            customer__app_code=bank.alias,
            is_phone_number_verified=True,
            is_email_verified=True,
            no_of_customer_account=0,
        )

        row += 1
        worksheet.write(row, 0, "Number of Fully Verified Profiles with No Account")
        write_data(row, profiles)

        # profiles with fully verified profiles with 1 Account
        profiles = CustomerProfile.objects.filter(
            customer__app_code=bank.alias,
            is_phone_number_verified=True,
            is_email_verified=True,
            no_of_customer_account=1,
        )

        row += 1
        worksheet.write(row, 0, "Number of Fully Verified Profiles with 1 Account")
        write_data(row, profiles)

        # profiles with fully verified profiles with 2 Accounts
        profiles = CustomerProfile.objects.filter(
            customer__app_code=bank.alias,
            is_phone_number_verified=True,
            is_email_verified=True,
            no_of_customer_account=2,
        )

        row += 1
        worksheet.write(row, 0, "Number of Fully Verified Profiles with 2 Accounts")
        write_data(row, profiles)

        # profiles with fully verified profiles with more than 2 accounts
        profiles = CustomerProfile.objects.filter(
            customer__app_code=bank.alias,
            is_phone_number_verified=True,
            is_email_verified=True,
            no_of_customer_account__gt=2,
        )

        row += 1
        worksheet.write(
            row, 0, "Number of Fully Verified Profiles with 2 More than 2 Accounts"
        )
        write_data(row, profiles)

    def customer_account(self, bank, workbook):
        """
        Retrieves bank account data and builds an excel sheet for the data
        """

        def get_data(accounts: QuerySet) -> tuple:
            all = accounts.count()
            savings = accounts.filter(account_type__code="savings").count()
            current = accounts.filter(account_type__code="current").count()

            return (all, savings, current)

        def write_data(row: int, accounts: QuerySet):
            for col, elem in enumerate(get_data(accounts)):
                worksheet.write(row, col + 1, elem)

        worksheet = workbook.add_worksheet("CustomerAccount")
        columns = ["ALL", "SAVINGS", "CURRENT"]
        row = 0

        worksheet.set_column(0, 0, len("Number of Accounts") * 1.25)

        for col, elem in enumerate(columns):
            worksheet.set_column(col + 1, col + 1, len(elem) * 1.25)

        for col, elem in enumerate(columns):
            worksheet.write(row, col + 1, elem)

        # All Account
        accounts = CustomerAccount.objects.filter(bank=bank)

        row += 1
        worksheet.write(row, 0, "Number of Accounts")
        write_data(row, accounts)

        accounts = accounts.order_by("date_created")
        start_date = accounts.first().date_created
        end_date = accounts.last().date_created

        prev_date = start_date
        next_date = start_date

        start_year = start_date.year
        year = 0

        while next_date <= end_date:
            if prev_date.month == 12:
                year += 1
                next_date = prev_date.replace(day=1, month=1, year=start_year + year)
            else:
                next_date = prev_date.replace(
                    day=1, month=(prev_date.month + 1), year=start_year + year
                )
            data = accounts.filter(date_created__range=(prev_date, next_date))
            row += 1
            worksheet.write(row, 0, prev_date.strftime("%b %Y"))
            write_data(row, data)
            prev_date = next_date

    def send_money(self, bank, workbook):
        """
        Retrieves all bank send money transactions and build an excel sheet
        for the data
        """

        def get_data(transactions: QuerySet) -> tuple:
            all_count = transactions.count()
            all_amount = transactions.aggregate(value=Sum("amount")).get("value")
            intra = transactions.filter(is_transaction_chargable=False)
            intra_count = intra.count()
            intra_amount = intra.aggregate(value=Sum("amount")).get("value")
            inter = transactions.filter(is_transaction_chargable=True)
            inter_count = inter.count()
            inter_amount = inter.aggregate(value=Sum("amount")).get("value")
            fee_amount = inter.aggregate(value=Sum("fee")).get("value")

            return (
                all_count,
                all_amount,
                intra_count,
                intra_amount,
                inter_count,
                inter_amount,
                fee_amount,
            )

        def write_data(row: int, transactions: QuerySet):
            for col, elem in enumerate(get_data(transactions)):
                worksheet.write(row, col + 1, elem)

        worksheet = workbook.add_worksheet("sendMoney")
        columns = [
            "ALL-COUNT",
            "ALL-AMOUNT",
            "INTRA-COUNT",
            "INTRA-AMOUNT",
            "INTER-COUNT",
            "INTER-AMOUNT",
            "FEE-AMOUNT",
        ]
        row = 0

        worksheet.set_column(0, 0, len("Total Send Money") * 1.25)

        for col, elem in enumerate(columns):
            worksheet.set_column(col + 1, col + 1, len(elem) * 1.25)

        for col, elem in enumerate(columns):
            worksheet.write(row, col + 1, elem)

        # All Send Money
        transactions = SendMoney.objects.filter(sender_account__bank=bank, status="S")

        row += 1
        worksheet.write(row, 0, "Total Send Money")
        write_data(row, transactions)

        transactions = transactions.order_by("date_created")
        start_date = transactions.first().date_created
        end_date = transactions.last().date_created

        prev_date = start_date
        next_date = start_date

        start_year = start_date.year
        year = 0

        while next_date <= end_date:
            if prev_date.month == 12:
                year += 1
                next_date = prev_date.replace(day=1, month=1, year=start_year + year)
            else:
                next_date = prev_date.replace(
                    day=1, month=(prev_date.month + 1), year=start_year + year
                )
            data = transactions.filter(date_created__range=(prev_date, next_date))
            row += 1
            worksheet.write(row, 0, prev_date.strftime("%b %Y"))
            write_data(row, data)
            prev_date = next_date

    def airtime_data(self, bank, workbook):
        """
        Retrieves all bank airtime-data transactions and build an excel sheet
        for the data
        """

        def get_data(transactions: QuerySet) -> tuple:
            count = transactions.count()
            amount = transactions.aggregate(value=Sum("amount")).get("value")
            fee = transactions.aggregate(value=Sum("fee")).get("value")

            return (count, amount, fee)

        def write_data(row: int, transactions: QuerySet):
            for col, elem in enumerate(get_data(transactions)):
                worksheet.write(row, col + 1, elem)

        worksheet = workbook.add_worksheet("airtimeData")
        columns = ["COUNT", "AMOUNT", "FEE"]
        row = 0

        worksheet.set_column(0, 0, len("Total Airtime Data") * 1.25)

        for col, elem in enumerate(columns):
            worksheet.set_column(col + 1, col + 1, len(elem) * 1.25)

        for col, elem in enumerate(columns):
            worksheet.write(row, col + 1, elem)

        # All Airtime Data
        transactions = AirtimeData.objects.filter(sender_account__bank=bank, status="S")

        row += 1
        worksheet.write(row, 0, "Total Airtime Data")
        write_data(row, transactions)

        transactions = transactions.order_by("date_created")
        start_date = transactions.first().date_created
        end_date = transactions.last().date_created

        prev_date = start_date
        next_date = start_date

        start_year = start_date.year
        year = 0

        while next_date <= end_date:
            if prev_date.month == 12:
                year += 1
                next_date = prev_date.replace(day=1, month=1, year=start_year + year)
            else:
                next_date = prev_date.replace(
                    day=1, month=(prev_date.month + 1), year=start_year + year
                )
            data = transactions.filter(date_created__range=(prev_date, next_date))
            row += 1
            worksheet.write(row, 0, prev_date.strftime("%b %Y"))
            write_data(row, data)
            prev_date = next_date

    def bill_payment(self, bank, workbook):
        """
        Retrieves all bank bill-payment transactions and build an excel sheet
        for the data
        """

        def get_data(transactions: QuerySet) -> tuple:
            count = transactions.count()
            amount = transactions.aggregate(value=Sum("amount")).get("value")
            fee = transactions.aggregate(value=Sum("fee")).get("value")

            return (
                count,
                amount,
                fee,
            )

        def write_data(row: int, transactions: QuerySet):
            for col, elem in enumerate(get_data(transactions)):
                worksheet.write(row, col + 1, elem)

        worksheet = workbook.add_worksheet("billPayment")
        columns = ["COUNT", "AMOUNT", "FEE"]
        row = 0

        worksheet.set_column(0, 0, len("Total Bill Payment") * 1.25)

        for col, elem in enumerate(columns):
            worksheet.set_column(col + 1, col + 1, len(elem) * 1.25)

        for col, elem in enumerate(columns):
            worksheet.write(row, col + 1, elem)

        # All Bill Payment
        transactions = BillPayment.objects.filter(sender_account__bank=bank, status="S")

        row += 1
        worksheet.write(row, 0, "Total Bill Payment")
        write_data(row, transactions)

        transactions = transactions.order_by("date_created")
        start_date = transactions.first().date_created
        end_date = transactions.last().date_created

        prev_date = start_date
        next_date = start_date

        start_year = start_date.year
        year = 0

        while next_date <= end_date:
            if prev_date.month == 12:
                year += 1
                next_date = prev_date.replace(day=1, month=1, year=start_year + year)
            else:
                next_date = prev_date.replace(
                    day=1, month=(prev_date.month + 1), year=start_year + year
                )
            data = transactions.filter(date_created__range=(prev_date, next_date))
            row += 1
            worksheet.write(row, 0, prev_date.strftime("%b %Y"))
            write_data(row, data)
            prev_date = next_date
