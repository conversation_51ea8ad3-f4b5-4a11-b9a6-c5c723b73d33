from django.urls import path
from rest_framework.routers import <PERSON>faultRouter

from .views import (
    ReportConfigViewSet,
    GetReports,
    GenerateReports,
    RetreiveBankDataReport,
)

router = DefaultRouter()
router.register("configuration", ReportConfigViewSet)

urlpatterns = [
    path("reports/", GetReports.as_view({"get": "list"}), name="reports"),
    path("reports/<int:id>/", GetReports.as_view({"get": "retrieve"}), name="report"),
    path("generate_reports/", GenerateReports.as_view(), name="generate_reports"),
    path(
        "retrieve_bank_data/<str:alias>/",
        RetreiveBankDataReport.as_view(),
        name="retrieve_bank_data",
    ),
]

urlpatterns += router.urls
