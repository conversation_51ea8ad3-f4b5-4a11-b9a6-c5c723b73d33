from django.db import models
from bank.models import Bank
from user_profile.models import BankAdmin
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# Create your models here.


class ReportConfiguration(models.Model):

    DAILY = "D"
    WEEKLY = "W"
    MONTHLY = "M"

    SEND_MONEY = "send_money"
    AIRTIME_DATA = "airtime_data"
    BILL_PAYMENT = "bill_payment"
    ACCOUNT_OPENING = "onboarding"

    MONDAY = "MON"
    TUESDAY = "TUE"
    WEDNESDAY = "WED"
    THURSDAY = "THU"
    FRIDAY = "FRI"
    SATURDAY = "SAT"
    SUNDAY = "SUN"

    FIRST = "F"
    SECOND = "S"
    THIRD = "T"
    LAST = "L"

    FREQUENCY_CHOICES = (
        (DAILY, "Daily"),
        (WEEKLY, "Weekly"),
        (MONTHLY, "Monthly"),
    )

    REPORT_CHOICES = (
        (SEND_MONEY, "Send Money"),
        (AIRTIME_DATA, "Airtime/Data"),
        (BILL_PAYMENT, "Bill Payment"),
        (ACCOUNT_OPENING, "Customer Onboarding"),
    )

    DAY_CHOICES = (
        (MONDAY, "Monday"),
        (TUESDAY, "Tuesday"),
        (WEDNESDAY, "Wednesday"),
        (THURSDAY, "Thursday"),
        (FRIDAY, "Friday"),
        (SATURDAY, "Saturday"),
        (SUNDAY, "Sunday"),
    )

    WEEK_CHOICES = (
        (FIRST, "First"),
        (SECOND, "Second"),
        (THIRD, "Third"),
        (LAST, "Last"),
    )

    name = models.CharField(max_length=100)
    partner_bank = models.ForeignKey(Bank, on_delete=models.CASCADE)
    partner_bank_admin = models.ForeignKey(
        BankAdmin, on_delete=models.CASCADE, blank=True, null=True
    )
    report_type = models.CharField(max_length=15, choices=REPORT_CHOICES)
    frequency = models.CharField(max_length=2, choices=FREQUENCY_CHOICES)
    email = models.EmailField()
    subscribed = models.BooleanField(default=True)
    day = models.CharField(max_length=4, choices=DAY_CHOICES, blank=True)
    week = models.CharField(max_length=1, choices=WEEK_CHOICES, blank=True)

    class Meta:
        verbose_name = "Report Configuration"
        verbose_name_plural = "Report Configurations"
        constraints = [
            models.UniqueConstraint(
                fields=["name", "email"], name="unique_report_config"
            ),
        ]

    def clean_fields(self, exclude=None) -> None:
        super().clean_fields(exclude=exclude)
        if not self.partner_bank.is_partner_bank:
            # enforce that the chosen bank is a partner bank
            raise ValidationError(
                {
                    "partner_bank": _(
                        f"{self.partner_bank.name} is not a partner bank, ensure that the chosen bank is a partner bank"
                    )
                }
            )

        if admin := self.partner_bank_admin:
            # if an admin is set, we enforce that the admin must belong to it's own bank
            if self.partner_bank != admin.bank:
                raise ValidationError(
                    {
                        "partner_bank_admin": _(
                            f"Partner Bank Admin Must belong to Partner Bank {self.partner_bank.name}"
                        )
                    }
                )

        if self.frequency == "W" and not self.day:
            # for weekly frequency we enforce that the user must pick a day as well
            raise ValidationError({"day": _("Day must be chosen for weekly reporting")})

        if self.frequency == "M" and (not self.day or not self.week):
            # for monthly frequency we enforce that a day as well as the week must be chosen
            if not self.week:
                raise ValidationError(
                    {"week": _("Week must be chosen for monthly reporting")}
                )
            if not self.day:
                raise ValidationError(
                    {"day": _("Day must be chosen for monthly reporting")}
                )
    
    def __str__(self) -> str:
        return self.name


class Report(models.Model):

    name = models.CharField(max_length=120)
    configuration = models.ForeignKey(ReportConfiguration, on_delete=models.CASCADE)
    excel = models.FileField(upload_to="reports/", blank=True, null=True)
    pdf = models.FileField(upload_to="reports/", blank=True, null=True)
    to = models.EmailField()
    admin_name = models.CharField(max_length=100, blank=True)
    date_created = models.DateTimeField(auto_now_add=True)
    sent = models.BooleanField(default=False)
