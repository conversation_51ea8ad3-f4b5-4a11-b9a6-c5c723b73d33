# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('reporting', '0001_initial'),
        ('user_profile', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='reportconfiguration',
            name='partner_bank_admin',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='user_profile.bankadmin'),
        ),
        migrations.AddField(
            model_name='report',
            name='configuration',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='reporting.reportconfiguration'),
        ),
        migrations.AddConstraint(
            model_name='reportconfiguration',
            constraint=models.UniqueConstraint(fields=('name', 'email'), name='unique_report_config'),
        ),
    ]
