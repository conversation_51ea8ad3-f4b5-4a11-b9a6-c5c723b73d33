# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bank', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=120)),
                ('excel', models.FileField(blank=True, null=True, upload_to='reports/')),
                ('pdf', models.FileField(blank=True, null=True, upload_to='reports/')),
                ('to', models.EmailField(max_length=254)),
                ('admin_name', models.CharField(blank=True, max_length=100)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('sent', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='ReportConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('report_type', models.CharField(choices=[('send_money', 'Send Money'), ('airtime_data', 'Airtime/Data'), ('bill_payment', 'Bill Payment'), ('onboarding', 'Customer Onboarding')], max_length=15)),
                ('frequency', models.CharField(choices=[('D', 'Daily'), ('W', 'Weekly'), ('M', 'Monthly')], max_length=2)),
                ('email', models.EmailField(max_length=254)),
                ('subscribed', models.BooleanField(default=True)),
                ('day', models.CharField(blank=True, choices=[('MON', 'Monday'), ('TUE', 'Tuesday'), ('WED', 'Wednesday'), ('THU', 'Thursday'), ('FRI', 'Friday'), ('SAT', 'Saturday'), ('SUN', 'Sunday')], max_length=4)),
                ('week', models.CharField(blank=True, choices=[('F', 'First'), ('S', 'Second'), ('T', 'Third'), ('L', 'Last')], max_length=1)),
                ('partner_bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bank')),
            ],
            options={
                'verbose_name': 'Report Configuration',
                'verbose_name_plural': 'Report Configurations',
            },
        ),
    ]
