from django.db.models import fields
from rest_framework import serializers
from .models import Report, ReportConfiguration


class ReportConfigurationSerializer(serializers.ModelSerializer):

    partner_bank = serializers.SerializerMethodField("get_partner_bank")
    partner_bank_admin = serializers.SerializerMethodField("get_partner_bank_admin")

    class Meta:
        model = ReportConfiguration
        fields = "__all__"

    def get_partner_bank(self, obj):
        return obj.partner_bank.name

    def get_partner_bank_admin(self, obj):
        return obj.partner_bank_admin.user.username


class ReportConfigurationPostSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReportConfiguration
        fields = ["name", "report_type", "frequency", "subscribed", "day", "week"]


class ReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = Report
        fields = "__all__"
