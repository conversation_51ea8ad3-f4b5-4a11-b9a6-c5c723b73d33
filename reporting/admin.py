from django.contrib import admin
from .models import ReportConfiguration, Report

# Register your models here.
@admin.register(ReportConfiguration)
class ReportConfigurationAdmin(admin.ModelAdmin):

    search_fields = [
        "name",
        "partner_bank__name",
        "partner_bank_admin__user__first_name",
        "partner_bank_admin__user__last_name",
        "partner_bank_admin__user__username",
        "email",
    ]
    list_filter = ["report_type", "frequency"]
    list_display = ["name", "partner_bank", "partner_bank_admin", "subscribed"]


@admin.register(Report)
class ReportAdmin(admin.ModelAdmin):

    search_fields = ["name", "admin_name"]
    list_filter = ["sent"]
    list_display = ["name", "configuration", "to", "admin_name", "date_created", "sent"]

    def has_change_permission(self, request, obj=None) -> bool:
        return False

    def has_add_permission(self, request, obj=None) -> bool:
        return False
