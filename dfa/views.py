import json
import io
import logging
import xlsxwriter
import binascii
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import IntegrityError
from django.db.models.query import QuerySet
from django.db.models import Q
from django.core.exceptions import ObjectDoesNotExist
from user_profile.models import BankAdmin, User
from user_profile.permissions import IsBankAdmin
from bank.models import Bank, BankAccountType
import pyotp
from transactions.models import Collection
from common.permissions import DFAAccessPermission
from common.models import BVN
from threading import Thread
from common.services import (
    account_opening,
    createNqrMerchant,
    generate_qr_image,
    get_balance_enquiry,
    getMerchantQr,
    nip_name_enquiry,
    send_email,
    send_sms,
    send_sms_test,
    validate_bvn,
    send_money,
    get_name_enquiry
)
import boto3
import requests
from botocore.exceptions import ClientError
from common.validators import validate_phone_number
from common.functions import get_reference
from user_profile.permissions import IsBank<PERSON>dmin

from dfa.serializers import (
    AccountDetailSerializer,
    AgentAccountSerializer,
    AgentOnboardedAccountListSerializer,
    AgentOnboardedAccountSerializer,
    AgentOnboardedMerchantSerializer,
    AgentSerializer,
    BulkCreateAgentSerializer,
    BulkCreateMerchantSerializer,
    CreateAccountSerializer,
    CreateAgentSerializer,
    CreateAccountSerializerV2,
    CreateOrganisationSerializer,
    CreateMerchantSerializer,
    EmailOTPSerializer,
    ForgotDetailsSerializer,
    OrganizationSerializer,
    RejectAccountOpeningSerializer,
    UpdateKYCstatusSerializer,
    GetCustomerMappedToAgentSerializer,
    CollectionHistorySerializer,
)
from common.functions import (
    CustomPageNumberPagination,
    create_bvn_record,
    generate_rand_password,
    get_agent_username,
    get_client_credentials,
    get_platform,
    get_reference,
    get_serializer_key_error,
    otp_rand,
    set_response_data,
    within_daily_limit,
    update_daily_cummulative,
)
from customer.models import CustomerAccount
from dfa.models import Agent, AgentAccount, AgentOnboardedAccount, Merchant, AgentMappingTable
from .util import (
    create_agent,
    get_data,
    retrieve_bvn,
    send_agent_creation_notification,
    send_customer_initiate_account_creation_notification,
    save_base64_image_to_s3,
    get_base64_image_from_s3,
    generate_s3_half_key,
    generate_s3_full_key,
    )
from rest_framework import generics, status, viewsets
from rest_framework.reverse import reverse
from oauth2_provider.contrib.rest_framework import permissions
from django.contrib.auth import get_user_model
from django.http import HttpResponse
from django.db.models import Sum
# from logic_omnichannel import settings
from django.conf import settings
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError
from rest_framework.filters import SearchFilter
from django.contrib.auth.models import update_last_login
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from drf_yasg import openapi


class CreateAgent(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = CreateAgentSerializer

    def post(self, request):

        log = logging.getLogger("omni_login")
        try:
            """
            Create bank agent with payload request
            """
            bankadmin = request.auth.user.bankadmin

            serializer = CreateAgentSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            password = generate_rand_password()
            
            log.info(f"AGENT PASSWORD: {password}")

            agent = create_agent(bankadmin, serializer.validated_data, password)

            Thread(
                target=send_agent_creation_notification, args=(agent, password)
            ).start()

            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Bank Agent Created Successfully",
                "response_detail": "Bank Agent Created Successfully",
            }
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Failed to Create Bank Agent",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class BulkCreateAgent(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = BulkCreateAgentSerializer

    def post(self, request):
        try:
            """
            Create bank agents in bulk with payload request containing list of agents (the serializer has a
            create function which create user if payload is valid)
            """
            bankadmin = request.auth.user.bankadmin

            serializer = BulkCreateAgentSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            agents: list = serializer.validated_data.get("agents")
            agents_creation_list: list = []

            for agent in agents:
                password = generate_rand_password()
                _agent = {**agent}
                try:
                    agent = create_agent(bankadmin, agent, password)
                    _agent["created"] = True
                except Exception as e:
                    _agent["created"] = False
                    _agent["reason"] = str(e)
                try:
                    send_agent_creation_notification(agent, password)
                except Exception:
                    pass
                agents_creation_list.append(_agent)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Bank Agent Created Successfully",
                detail={"agents": agents_creation_list},
            )
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Creating Agents -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GenerateBulkCreateAgentTemplate(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    def get(self, request):
        try:
            """
            Basically it returns an excel sheet containing the required template for
            creating agents in bulk. the excel sheet is stored in-memory
            """
            # Create an in-memory output file for the new workbook.
            output = io.BytesIO()

            workbook = xlsxwriter.Workbook(output)
            worksheet = workbook.add_worksheet("agents")

            columns = [
                "last_name",
                "first_name",
                "email",
                "phone_number",
                "account_number",
                "transaction_limit",
            ]

            row = 0

            for col, elem in enumerate(columns):
                worksheet.write(row, col, elem)

            workbook.close()

            # Rewind the buffer.
            output.seek(0)

            # Set up the Http response.
            filename = "bulk_create_agent_template.xlsx"
            response = HttpResponse(
                output,
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
            response["Content-Disposition"] = f"attachment; filename={filename}"

            return response
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Generating Template {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetAgents(viewsets.GenericViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = AgentSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = ("=username", "^first_name", "^last_name", "=email")

    suspend_param = openapi.Parameter(
        "suspend",
        openapi.IN_QUERY,
        description="retrieve suspended/active agents",
        type=openapi.TYPE_BOOLEAN,
        required=False,
    )
    search = openapi.Parameter(
        "search",
        openapi.IN_QUERY,
        description="search for agents by (username, first_name, last_name, email)",
        type=openapi.TYPE_STRING,
        required=False,
    )
    organization_id = openapi.Parameter(
        "organization_id",
        openapi.IN_QUERY,
        description="Organization ID",
        type=openapi.TYPE_INTEGER,
        required=False,
    )
    start_date = openapi.Parameter(
        "start_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    end_date = openapi.Parameter(
        "end_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    # request_response = openapi.Response('Response Description', )

    def get_queryset(self):
        user = self.request.auth.user
        bankadmin = user.bankadmin
        if bankadmin.role == "marketer":
            agents = (
                get_user_model()
                .objects.filter(agent__created_by=bankadmin)
                .order_by("last_name", "first_name")
            )
        else:
            agents = (
                get_user_model()
                .objects.filter(agent__bank=bankadmin.bank)
                .order_by("last_name", "first_name")
            )
        search = self.request.query_params.get("search", None)
        organization_id: str = self.request.query_params.get("organization_id", None)
        suspend: str = self.request.query_params.get("suspend", None)

        if search is not None:
            # force call the search field filter
            agents = self.filter_queryset(agents)
        if organization_id:
            if not organization_id.isnumeric():
                raise Exception(
                    "Query Param <organization_id> should be an integer type"
                )
            try:
                organization = get_user_model().objects.get(id=id).bankadmin
                agents = agents.filter(agent__created_by=organization)
            except Exception:
                raise Exception("Query Param <organization_id> is not valid")
        if suspend:
            suspend = suspend.lower()
            if suspend in ["true", "false"]:
                suspend = True if suspend == "true" else False
                agents = agents.filter(agent__status="I" if suspend else "A")
        return agents

    @swagger_auto_schema(manual_parameters=[suspend_param, search, organization_id])
    def list(self, request):
        try:
            """
            Get all bank agents by querying the database for users that are agents for
            the bank. Then paginate the query to allow only the allowed number to be sent
            """
            queryset = self.get_queryset()
            queryset = self.paginate_queryset(queryset)
            serializer = AgentSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)

            if queryset:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Bank Agent List",
                    "response_detail": paginated_resp.data,
                }
            else:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Empty Bank Agent List",
                    "response_detail": paginated_resp.data,
                }
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = {
                "reponse_code": settings.FAILED_RESPONSE,
                "response_description": "Error occurred while getting bank agents",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(manual_parameters=[start_date, end_date])
    def retrieve(self, request, id):
        try:
            """
            Retrieves single instance of Agent Object and return the data serialized to the client
            """
            agent = get_user_model().objects.get(id=id)

            if not hasattr(agent, "agent"):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"Agent With Id -> {id} Does Not Exist",
                    detail=f"Agent With Id -> {id} Does Not Exist",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            serializer = AgentSerializer(instance=agent)

            accounts = AgentOnboardedAccount.objects.filter(agent=agent.agent)
            merchants = Merchant.objects.filter(agent=agent.agent)

            start_date = request.query_params.get("start_date", None)
            end_date = request.query_params.get("end_date", None)

            if start_date and end_date:
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
                end_date = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)

                accounts = accounts.filter(date_created__range=[start_date, end_date])
                merchants = merchants.filter(date_created__range=[start_date, end_date])

            accounts_data = get_data(accounts)
            merchants_data = get_data(merchants)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Agent Retrieved Successfully",
                detail={
                    **serializer.data,
                    "limit_balance": "0.00",
                    "float_balance": "0.00",
                    "accounts_data": accounts_data,
                    "merchants_data": merchants_data,
                    "loan_analysis": {
                        "volume": 0,
                        "value": 0,
                        "repaid": 0,
                        "pending": 0,
                    },
                    "customer_top_up": {"volume": 0, "value": 0},
                },
            )
            return Response(data, status=status.HTTP_200_OK)
        except get_user_model().DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Agent With Id -> {id} Does Not Exist",
                detail=f"Agent With Id -> {id} Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Retrieve Agent -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class UpdateAgent(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = CreateAgentSerializer

    def put(self, request, id):
        try:
            """
            Use the Id to get the agent then update the agent based on the request payload
            """
            serializer = CreateAgentSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            user = get_user_model().objects.get(id=id)
            if hasattr(user, "agent"):
                agent = user.agent
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Agent Does Not Exist",
                    detail="Agent Does Not Exist",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            email = serializer.validated_data["email"]
            phone_number = serializer.validated_data["phone_number"]

            if user.email != email or agent.phone_number != phone_number:
                password = generate_rand_password()
                if agent.phone_number != phone_number:
                    # since username is dependent on phone number a change in phone number would require a change in username
                    bank = request.auth.user.bankadmin.bank
                    username = get_agent_username(bank.alias, phone_number)
                    self.update_agent(user, agent, serializer, password, username)
                else:
                    self.update_agent(user, agent, serializer, password)

                Thread(
                    target=self.send_agent_details_update_notification,
                    args=(agent, password),
                ).start()
            else:
                self.update_agent(user, agent, serializer)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Bank Agent Account Updated Successfully",
                detail="Bank Agent Account Updated Successfully",
            )
            return Response(data, status=status.HTTP_200_OK)
        except get_user_model().DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Agent Does Not Exist",
                detail="Agent Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Updating Bank Agent Account -> {e}",
                detail=f"Error Updating Bank Agent Account -> {e}",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def update_agent(self, user, agent, serializer, password=None, username=None):
        """
        Updates Agents details based on values from the serializer
        """
        account_number = serializer.get("account_number", None)
        transaction_limit = serializer.get("transaction_limit", None)
        user.first_name = serializer.validated_data["first_name"]
        user.last_name = serializer.validated_data["last_name"]
        if password:
            user.email = serializer.validated_data["email"]
            user.set_password(password)
            # Making last login None so it will force the Agent to change the auto generated password
            # allowing the app to think it is a first time login action
            user.last_login = None
            agent.phone_number = serializer.validated_data["phone_number"]
            agent.save()
        if username:
            user.username = username
        user.save()

        if account_number:
            try:
                # Try to create an agent account for agent
                account_type = BankAccountType.objects.get(code="savings")
                AgentAccount.objects.create(
                    agent=agent,
                    account_number=account_number,
                    daily_limit=transaction_limit,
                    account_type=account_type,
                )
            except Exception:
                raise Exception("Unable to Add Agent Account Number")

    def send_agent_details_update_notification(
        self, agent: Agent, password: str
    ) -> None:
        """
        Notifies Bank Agent via mail and text message of the account creation details for login
        """

        message = f"Hello {agent.user.first_name}\n \nYOUR {agent.bank.name.upper()} agent account was updated on omni channel, your login detail are as shown below \nusername: \n \t{agent.user.username}\n \npassword:\n \t{password}\n \nYou can now login on the Agent DFA app"

        email_args = {
            "email_from": agent.bank.email,
            "to": agent.user.email,
            "subject": f"{agent.bank.name.upper()} AGENT ACCOUNT UPDATE NOTIFICATION",
            "message": message,
        }

        send_email(email_args, "INTERNAL_NOTIFICATION")

        message = f"Hello {agent.user.first_name}\nYour updated agent login details \nusername:{agent.user.username}\npassword:{password}"

        request_id = get_reference()
        send_sms(
            agent.phone_number,
            message,
            institution_code=agent.bank.code,
            message_sender_name=agent.bank.message_sender_name,
            request_id=request_id,
        )


class AgentOnboardedAccounts(viewsets.GenericViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = AgentOnboardedAccountSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = (
        "last_name",
        "first_name",
        "=email",
        "=account_number",
    )

    search = openapi.Parameter(
        "search",
        openapi.IN_QUERY,
        description="search for accounts by (username, first_name, last_name, email)",
        type=openapi.TYPE_STRING,
        required=False,
    )
    _status = openapi.Parameter(
        "status",
        openapi.IN_QUERY,
        description="Retrieve Accounts by Status",
        type=openapi.TYPE_STRING,
        required=False,
        enum=["incomplete", "pending", "complete"],
    )
    agent_id = openapi.Parameter(
        "agent_id",
        openapi.IN_QUERY,
        description="search for accounts onboarded by particular agent (parse agent id)",
        type=openapi.TYPE_INTEGER,
        required=False,
    )

    def get_queryset(self, agent: Agent = None, admin: BankAdmin = None):
        """
        From the authenticated request user, get the calling bank and use it filtering
        the customers with that bank account, then return the list of user ordered by
        first name and last name
        """

        if agent:
            queryset = AgentOnboardedAccount.objects.filter(agent=agent).order_by(
                "last_name", "first_name"
            )
            search = self.request.query_params.get("search", None)
            _status = self.request.query_params.get("status", None)
            if search:
                # force call the search field filter
                queryset = self.filter_queryset(queryset)
            if _status:
                # filter by status
                queryset = queryset.filter(kyc_verification_status=_status)
            return queryset
        if admin:
            if admin.role == "marketer":
                queryset = AgentOnboardedAccount.objects.filter(
                    agent__created_by=admin
                ).order_by("last_name", "first_name")
            else:
                queryset = AgentOnboardedAccount.objects.filter(
                    agent__bank=admin.bank
                ).order_by("last_name", "first_name")
            agent_id = self.request.query_params.get("agent_id", None)
            search = self.request.query_params.get("search", None)
            _status = self.request.query_params.get("status", None)
            if agent_id:
                try:
                    user = get_user_model().objects.filter(id=int(agent_id))
                    if user:
                        user = user[0]
                        if hasattr(user, "agent"):
                            agent = user.agent
                            queryset = queryset.filter(agent=agent)
                except Exception:
                    pass
            if search:
                # force call the search field filter
                queryset = self.filter_queryset(queryset)
            if _status:
                # filter by status
                queryset = queryset.filter(kyc_verification_status=_status)

            return queryset

    @swagger_auto_schema(manual_parameters=[search, _status])
    def retrieve(self, request, id):
        try:
            """
            This is retrieve endpoint is for Bank Portal.
            Get the query set, Paginate the query to reduce the request been sent
            the format the request been sent using the serializer as well as the
            paginated response.
            """

            # Validate request point of entry
            client_id, client_secret = get_client_credentials(token=request.auth.token)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )

            if request_platform.upper().startswith("DFA"):
                agent = request.auth.user.agent
                account = AgentOnboardedAccount.objects.get(id=id, agent=agent)

            elif request_platform.upper().startswith("BANK ADMIN"):
                bankadmin = request.auth.user.bankadmin
                account = AgentOnboardedAccount.objects.get(
                    id=id, agent__bank=bankadmin.bank
                )
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Client Credentials",
                    detail="Invalid Client Credentials",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            serializer = AgentOnboardedAccountSerializer(account)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Agent Onboarded Account Retrieved Successfully",
                detail=serializer.data,
            )

            return Response(data, status=status.HTTP_200_OK)
        except AgentOnboardedAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Agent Onboarded Customer Accounts Does Not Exist",
                detail="Agent Onboarded Customer Accounts Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                "Unable to Get All Agent Onboarded Customer Accounts",
                str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(manual_parameters=[search, _status, agent_id])
    def list(self, request):
      
        try:
            """
            This is retrieve endpoint is for DFA Portal.
            Get the query set, Paginate the query to reduce the request been sent
            the format the request been sent using the serializer as well as the
            paginated response.
            """

            # Validate request point of entry
            client_id, client_secret = get_client_credentials(token=request.auth.token)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )

            if request_platform.upper().startswith("DFA"):
                agent = request.auth.user.agent
                queryset = self.get_queryset(agent)
            elif request_platform.upper().startswith("BANK ADMIN"):
                admin = self.request.auth.user.bankadmin
                queryset = self.get_queryset(admin=admin)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Client Credentials",
                    detail="Invalid Client Credentials",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            queryset = self.paginate_queryset(queryset)
            serializer = AgentOnboardedAccountListSerializer(queryset, many=True)
 
            paginated_resp = self.get_paginated_response(data=serializer.data)
            
            if queryset:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    "Get All Agent Onboarded Customer Accounts Request Successful",
                    paginated_resp.data,
                )
            else:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    "Agent Onboarded Customer Accounts List Empty",
                    paginated_resp.data,
                )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                "Unable to Get All Agent Onboarded Customer Accounts",
                str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, id):
        try:
            """
            for dfa agent app, agent are able to delete onboarded account that
            are yet to be approved.
            """

            # Validate request point of entry
            client_id, client_secret = get_client_credentials(token=request.auth.token)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )

            if not request_platform.upper().startswith("DFA"):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Client Credentials",
                    detail="Invalid Client Credentials",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            agent = request.auth.user.agent
            account = AgentOnboardedAccount.objects.get(id=id, agent=agent)
            if account.is_approved or account.kyc_verification_status == "complete":
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable To Delete Account",
                    detail=f"Unable To Delete Account: account approval -> {account.is_approved}, account kyc status -> {account.kyc_verification_status}",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            account.delete()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Agent Onboarded Account Deleted Successfully",
                detail="Agent Onboarded Account Deleted Successfully",
            )
            return Response(data, status=status.HTTP_200_OK)
        except AgentOnboardedAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Agent Onboarded Account Does Not Exist",
                detail="Agent Onboarded Account Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error deleting Agent {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class AgentOnboardedMerchants(viewsets.GenericViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = AgentOnboardedMerchantSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = (
        "last_name",
        "first_name",
        "=email",
        "=account_number",
    )

    search = openapi.Parameter(
        "search",
        openapi.IN_QUERY,
        description="search for agents by (username, first_name, last_name, email)",
        type=openapi.TYPE_STRING,
        required=False,
    )
    _status = openapi.Parameter(
        "status",
        openapi.IN_QUERY,
        description="Retrieve Accounts by Status",
        type=openapi.TYPE_STRING,
        required=False,
        enum=["incomplete", "pending", "complete"],
    )
    agent_id = openapi.Parameter(
        "agent_id",
        openapi.IN_QUERY,
        description="search for accounts onboarded by particular agent (parse agent id)",
        type=openapi.TYPE_INTEGER,
        required=False,
    )

    def get_queryset(self, agent: Agent = None, admin: BankAdmin = None):
        """
        From the authenticated request user, get the calling bank and use it filtering
        the customers with that bank account, then return the list of user ordered by
        first name and last name
        """
        if agent:
            queryset = Merchant.objects.filter(agent=agent).order_by(
                "last_name", "first_name"
            )
            search = self.request.query_params.get("search", None)
            # _status = self.request.query_params.get("status", None)
            if search:
                # force call the search field filter
                queryset = self.filter_queryset(queryset)
            # if _status:
            #     queryset = queryset.filter(kyc_verification_status=_status)
            return queryset
        if admin:
            if admin.role == "marketer":
                queryset = Merchant.objects.filter(agent__created_by=admin).order_by(
                    "last_name", "first_name"
                )
            else:
                queryset = Merchant.objects.filter(agent__bank=admin.bank).order_by(
                    "last_name", "first_name"
                )
            agent_id = self.request.query_params.get("agent_id", None)
            search = self.request.query_params.get("search", None)
            # _status = self.request.query_params.get("status", None)
            if agent_id:
                try:
                    user = get_user_model().objects.filter(id=int(agent_id))
                    if user:
                        user = user[0]
                        if hasattr(user, "agent"):
                            agent = user.agent
                            queryset = queryset.filter(agent=agent)
                except Exception:
                    pass
            if search is not None:
                # force call the search field filter
                queryset = self.filter_queryset(queryset)
            # if _status:
            #     queryset = queryset.filter(kyc_verification_status=_status)

            return queryset

    @swagger_auto_schema(manual_parameters=[search, _status])
    def retrieve(self, request, id):
        try:
            """
            This is retrieve endpoint is for Bank Portal.
            Get the query set, Paginate the query to reduce the request been sent
            the format the request been sent using the serializer as well as the
            paginated response.
            """

            # Validate request point of entry
            client_id, client_secret = get_client_credentials(token=request.auth.token)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )
            if not request_platform.upper().startswith("BANK ADMIN"):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Client Credentials: API FOR BANK ADMIN",
                    detail="Invalid Client Credentials: API FOR BANK ADMIN",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            admin = request.auth.user.bankadmin
            merchant = Merchant.objects.get(id=id, agent__bank=admin.bank)

            serializer = AgentOnboardedMerchantSerializer(merchant)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Retrieve Agent Onboarded Merchants request Successful",
                detail=serializer.data,
            )
            return Response(data, status=status.HTTP_200_OK)
        except Merchant.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Merchant Does Not Exist",
                detail="Merchant Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                "Unable to Get All Agent Onboarded Merchants",
                str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    @swagger_auto_schema(manual_parameters=[search, _status, agent_id])
    def list(self, request):
        try:
            """
            This is retrieve endpoint is for DFA Portal.
            Get the query set, Paginate the query to reduce the request been sent
            the format the request been sent using the serializer as well as the
            paginated response.
            """

            # Validate request point of entry
            client_id, client_secret = get_client_credentials(token=request.auth.token)
            request_platform = get_platform(
                client_id=client_id, client_secret=client_secret
            )

            if request_platform.upper().startswith("DFA"):
                agent = request.auth.user.agent
                queryset = self.get_queryset(agent)
            elif request_platform.upper().startswith("BANK ADMIN"):
                admin = request.auth.user.bankadmin
                queryset = self.get_queryset(admin=admin)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Client Credentials",
                    detail="Invalid Client Credentials",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            queryset = self.paginate_queryset(queryset)
            serializer = AgentOnboardedMerchantSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)
            if queryset:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    "Get All Agent Onboarded Merchants Request Successful",
                    paginated_resp.data,
                )
            else:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    "Agent Onboarded Merchants List Empty",
                    paginated_resp.data,
                )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                "Unable to Get All Agent Onboarded Merchants",
                str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetAccountDetails(generics.RetrieveAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = AccountDetailSerializer

    def retrieve(self, request, id):
        try:
            """
            Using the Id, we retrieve customer account and parse it into the serializer to
            generate onboarded account details
            """
            account = CustomerAccount.objects.get(id=id)
            serializer = self.get_serializer(account)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Details Retrieval Successful",
                detail={**serializer.data},
            )
            return Response(data, status=status.HTTP_200_OK)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Customer Account Does Not Exist",
                detail="Customer Account Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Retrieving Account Details -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetAgentProfile(generics.RetrieveAPIView):

    permission_classes = [DFAAccessPermission]

    def retrieve(self, request):
        try:
            """
            From the authenticated request get the agent, confirm the agent has an agent profile
            then return the profile.
            """
            user = request.auth.user
            agent = user.agent

            agent_accounts = AgentAccount.objects.filter(agent=agent)
            agent_accounts = AgentAccountSerializer(agent_accounts, many=True).data

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Agent Profile Request Successful",
                detail={
                    "agent_details": {
                        "id": agent.id,
                        "username": user.get_username(),
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "email": user.email,
                        "phone_number": agent.phone_number,
                        "is_active": user.is_active,
                        "last_login": user.last_login,
                        "pin_enabled": True if user.agent.pin else False,
                        "status": agent.status,
                        "is_first_time_user": True
                        if user.last_login is None
                        else False,
                    },
                    "agent_bank_profile": {
                        "bank": {
                            "id": agent.bank.id,
                            "name": agent.bank.name,
                            "code": agent.bank.code,
                            "alias": agent.bank.alias,
                            "address": agent.bank.address,
                            "email": agent.bank.email,
                            "phone_number": agent.bank.phone_number,
                            "website": agent.bank.website,
                            "bank_logo_url": agent.bank.logo_img.url,
                            "bank_logo_b64_encode": agent.bank.get_logo_base64(),
                            # "bank_logo_b64_encode": "Not images yet"
                        },
                        "accounts": agent_accounts,
                    },
                },
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Retriving Agent Profile -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetAgentAccountBalance(generics.RetrieveAPIView):

    permission_classes = [DFAAccessPermission]

    def retrieve(self, request, account_number):
        try:
            """
            Confirm the agent from the authenticated request, then confirm the account is mapped to
            the agent and confirm the institution code provided valid then retrieve account balance
            """

            user = request.auth.user
            token = request.auth.token
            agent = user.agent

            AgentAccount.objects.get(agent=agent, account_number=account_number)

            if settings.APP_ENV == "production":
                resp = get_balance_enquiry(
                    agent.bank,
                    account_number,
                    agent.bank.code,
                    app_code=agent.bank.alias,
                    b_token=token,
                )
            else:
                resp = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": {
                        "descripiton": "SUCCESS",
                        "available_balance": "125000.67",
                        "book_balance": "125000.67",
                    },
                }
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Balance Enquiry Request Successful",
                    detail=resp.get("response_description"),
                )
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Error Calling the Balance Enquiry Service",
                    detail=resp.get("response_description"),
                )
            return Response(data, status=status.HTTP_200_OK)
        except AgentAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Agent Account Does Not Exist",
                detail="Agent Account Does Not Exist",
            )
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Processing Balance Enquiry Request",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class CreateAccount(generics.GenericAPIView):

    permission_classes = [DFAAccessPermission]
    serializer_class = CreateAccountSerializer

    def post(self, request):
        try:
            """
            validate the request payload from the client application then try to call the
            create account endpoint from the core banking if the account was successfully
            created we persist the data into the database else we just return failed.
            """

            agent = request.auth.user.agent
            

            if agent.status == "I":
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="You Have Been Suspended, Please Contact Admin",
                    detail="You Have Been Suspended, Please Contact Admin",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            serializer = CreateAccountSerializer(data=request.data)

            # log.info("serializer validity >> " + str(serializer.is_valid(raise_exception=True)))

            serializer.is_valid(raise_exception=True)

            # phone_number = validate_phone_number(request.data['phone_number'])
            # agent_user = str(request.auth.user.agent.user)

            # print("phone_number > "  + phone_number)

            # half_key = phone_number + ':' + agent_user 

            #consider putting this in a thread
            # id_key = save_base64_image_to_s3(request.data['id_base_64'], half_key + ':id')
            # sig_key = save_base64_image_to_s3(request.data['signature_base_64'], half_key + ':signature')
            # prof_key = save_base64_image_to_s3(request.data['profile_picture_base_64'], half_key + ':profile_picture')

            account = serializer.save(
                agent=agent, 
                kyc_verification_status="pending",
                id_base_64 = 'N/A',
                signature_base_64 = 'N/A',
                profile_picture_base_64 = 'N/A',
            )
            
            Thread(
                target=send_customer_initiate_account_creation_notification,
                args=(account,),
            ).start()
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Creation Initiated Successfully",
                detail={
                    "id": serializer.data["id"],
                    "message": "Account Creation Initiated Successfully"
                    },
            )
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Creating Account -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class CreateAccountV2(generics.GenericAPIView):

    permission_classes = [DFAAccessPermission]
    serializer_class = CreateAccountSerializerV2

    def post(self, request):
        try:
            """
            validate the request payload from the client application then try to call the
            create account endpoint from the core banking if the account was successfully
            created we persist the data into the database else we just return failed.
            """

            agent = request.auth.user.agent
            

            if agent.status == "I":
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="You Have Been Suspended, Please Contact Admin",
                    detail="You Have Been Suspended, Please Contact Admin",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            serializer = CreateAccountSerializerV2(data=request.data, context={"agent": agent})

            # log.info("serializer validity >> " + str(serializer.is_valid(raise_exception=True)))

            serializer.is_valid(raise_exception=True)
            validated_data = serializer.validated_data
        
            extra_fields = {
                # "account_type": validated_data.get("account_type", None),
                "marital_status": validated_data.get("marital_status", None),
                "tin": validated_data.get("tin", None),
                "nin": validated_data.get("nin", None),
                "sector": validated_data.get("sector", None),
                "next_kin_address": validated_data.get("next_kin_address", None),
                "next_kin_name": validated_data.get("next_kin_name", None),
                "next_kin_relationship": validated_data.get("next_kin_relationship", None),
                "next_kin_phone": validated_data.get("next_kin_phone", None),
                "next_kin_email": validated_data.get("next_kin_email", None),
                "place_of_birth": validated_data.get("place_of_birth")
            }

             
            account_type_code = validated_data.get("account_type", None)
            account_type = BankAccountType.objects.filter(code=account_type_code).first()

            account = AgentOnboardedAccount.objects.create(
                bvn=validated_data.get("bvn"),
                first_name=validated_data.get("first_name"),
                last_name=validated_data.get("last_name"),
                middle_name=validated_data.get("middle_name"),
                account_type=account_type,
                email=validated_data.get("email"),
                phone_number=validated_data.get("phone_number"),
                address=validated_data.get("address"),
                city=validated_data.get("city"),
                state=validated_data.get("state"),
                gender=validated_data.get("gender"),
                dob=validated_data.get("dob"),
                means_of_id=validated_data.get("means_of_id"),
                id_issue_date=validated_data.get("id_issue_date"),
                id_expiry_date=validated_data.get("id_expiry_date"),
                id_number=validated_data.get("id_number"),
                kyc_verification_status="pending",
                id_base_64 = 'N/A',
                signature_base_64 = 'N/A',
                profile_picture_base_64 = 'N/A',
                agent=agent
        )

            account.set_extra_fields(extra_fields)
            account.save()
                
            Thread(
                target=send_customer_initiate_account_creation_notification,
                args=(account,),
            ).start()
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Creation Initiated Successfully",
                detail={
                    "id": account.id,
                    "message": "Account Creation Initiated Successfully"
                    },
            )
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Creating Account -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class RetryCreateAccount(generics.GenericAPIView):

    permission_classes = [DFAAccessPermission]
    serializer_class = CreateAccountSerializer

    def put(self, request, id):
        try:
            """
            retrieve the agent onboarded account then update the account model table, then
            validate the request payload from the client application then try to call the
            create account endpoint from the core banking if the account was successfully
            created we persist the data into the database else we just return failed.
            """
            agent = request.auth.user.agent
            account = AgentOnboardedAccount.objects.get(id=id, agent=agent)
            serializer = CreateAccountSerializer(instance=account, data=request.data)
            serializer.is_valid(raise_exception=True)
            account = serializer.save(kyc_verification_status="pending")

            Thread(
                target=send_customer_initiate_account_creation_notification,
                args=(account,),
            ).start()
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Creation Initiated Successfully",
                detail="Account Creation Initiated Successfully",
            )
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except AgentOnboardedAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Agent Onboarded Account Does Not Exist",
                detail="Agent Onboarded Account Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

class AddOrUpdateKYCImages(generics.GenericAPIView):
    
    permission_classes = [DFAAccessPermission]
    
    def patch(self, request, id):
        try:
            """ 
            This view follows after the creation view, where images can be added and updated.
            It receives the images as a decoded string, and generates a key that is passed to 
            s3 function if they field is N/A, if not it will take the existing key, split and then
            pass to the s3 function.
            """
            # agent = request.auth.user.agent
            account = AgentOnboardedAccount.objects.get(id=id)
            
            id_base_64 = request.data.get('id_base_64')
            signature_base_64 = request.data.get('signature_base_64')
            profile_picture_base_64 = request.data.get('profile_picture_base_64')
            
            if id_base_64 is not None:
                db_id_key = account.id_base_64
                id_half_key = generate_s3_half_key('id', account.agent.user, account.phone_number)
                id_key = generate_s3_full_key(
                    db_key=db_id_key,
                    s3_half_key=id_half_key,
                    base64_string=id_base_64
                )
                account.id_base_64 = id_key
                       
            if signature_base_64 is not None:  
                db_sig_key = account.signature_base_64 
                sig_half_key = generate_s3_half_key('signature', account.agent.user, account.phone_number)
                sig_key = generate_s3_full_key(
                    db_key=db_sig_key,
                    s3_half_key=sig_half_key,
                    base64_string=signature_base_64
                )
                account.signature_base_64 = sig_key
           
            if profile_picture_base_64 is not None:   
                db_profile_key = account.profile_picture_base_64
                profile_half_key = generate_s3_half_key('profile_picture', account.agent.user, account.phone_number)
                profile_key = generate_s3_full_key(
                    db_key=db_profile_key,
                    s3_half_key=profile_half_key,
                    base64_string=profile_picture_base_64
                )
                account.profile_picture_base_64 = profile_key
                
            account.save()
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Image Updated Successfully",
                detail="Image Updated Successfully"
            )
            return Response(data, status=status.HTTP_200_OK)
            
        except AgentOnboardedAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Agent Onboarded Account Does Not Exist",
                detail="Agent Onboarded Account Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except binascii.Error:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Incorrect Padding.",
                detail="Incorrect Padding: Image string not correctly padded."
            )  
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Failed to process request.",
                detail=f"Failed to process request: {e}"
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SuspendAndUnSuspendAgent(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    def get(self, request, id):
        try:
            """
            use the id to get agent and from the query params get the suspend param which is
            expected to be a boolean
            """
            suspend = request.query_params.get("suspend", "").lower()
            if suspend not in ["true", "false"]:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Kindly provide request param <suspend> with boolean in request url",
                    detail="Kindly provide request param <suspend> with boolean in request url",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            user = get_user_model().objects.get(id=id)

            if not hasattr(user, "agent"):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"Agent With Id -> {id} Does Not Exist",
                    detail=f"Agent With Id -> {id} Does Not Exist",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            user.is_active = False if suspend == "true" else True
            user.save()
            agent = user.agent
            agent.status = "I" if suspend == "true" else "A"
            agent.save()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description=f'Agent Status Has Been Successfully Set to {"Inactive" if suspend == "true" else "Active"}',
                detail=f'Agent Status Has Been Successfully Set to {"Inactive" if suspend == "true" else "Active"}',
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Complete Request: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SendPhoneNumberOTP(generics.GenericAPIView):

    permission_classes = [DFAAccessPermission]

    def get(self, request, phone_number):
        try:
            """
            send sms otp to DFA onboarding customers
            """
            agent = request.auth.user.agent
            bank = agent.bank

            message_sender_name = bank.message_sender_name

            # Generate counter based OTP
            hotp = pyotp.HOTP("base32secret3232")
            validate_otp_at = otp_rand()
            verify_phone_number_otp = hotp.at(validate_otp_at)

            sms_text = f"Welcome to {bank.name.upper()}. \nThis is your phone number verfication OTP: \n{verify_phone_number_otp}"

            if settings.APP_ENV == "development":
                resp = send_sms_test(phone_number, sms_text)
            elif message_sender_name == "OmoluabiMB":
                # run our test sms service because this is a test case
                resp = send_sms_test(phone_number, sms_text)
            else:
                resp = send_sms(
                    phone_number,
                    sms_text,
                    institution_code=bank.code,
                    message_sender_name=message_sender_name,
                    request_id=get_reference(),
                )

            if resp["response_code"] == settings.SUCCESS_RESPONSE:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "OTP SMS Sent Successfully",
                    "response_detail": {
                        "validate_otp_at": validate_otp_at,
                        "otp": verify_phone_number_otp,
                    },
                }
                return Response(data, status=status.HTTP_200_OK)

            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Sending OTP SMS",
                detail="Error Verifying User Phone Number",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Verifying User Phone Number",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SendEmailOTP(generics.GenericAPIView):

    permission_classes = [DFAAccessPermission]
    serializer_class = EmailOTPSerializer

    def post(self, request):
        try:
            """
            Sending email OTP to DFA onboarding customer
            """
            agent = request.auth.user.agent
            bank = agent.bank

            serializer = EmailOTPSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            email = serializer.validated_data["email"]
            first_name = serializer.validated_data["first_name"]

            # Generate counter based OTP
            hotp = pyotp.HOTP("base32secret3232")
            validate_otp_at = otp_rand()
            verify_email_otp = hotp.at(validate_otp_at)

            email_subject = f"Verify Account Email on {bank.name.upper()}"
            email_body = f"Welcome to {bank.name.upper()}. Just one more step to verify your account and you begin to enjoy our exciting offerings on all our platfoms. \nThis is your email verfication OTP: \n{verify_email_otp}"

            email_args = {
                "to": email,
                "subject": email_subject,
                "customer_first_name": f"{first_name}".capitalize(),
                "message": email_body,
                "img_url": f"{bank.email_img_url}",
                "foreground": bank.email_foreground_color,
                "background": bank.email_background_color,
                "email_from": bank.email,
                "otp": verify_email_otp,
            }

            resp = send_email(email_args, "VERIFY_EMAIL")

            if resp["response_code"] == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="RESET PASSWORD OTP SENT TO EMAIL SUCCESSFULLY",
                    detail={"validate_otp_at": validate_otp_at},
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                return Response(resp, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": "Error Sending Verification Email",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class CreateMerchant(generics.GenericAPIView):

    permission_classes = [DFAAccessPermission]
    serializer_class = CreateMerchantSerializer

    def post(self, request):
        log = logging.getLogger("django")
        try:
            """
            Basically for now just retrieve validated data from the customer and persist it
            on the database for now
            """
            log.info(f"CREATE MERCHANT REQUEST PAYLOAD -> {request.data}")
            agent = request.auth.user.agent
            if agent.status == "I":
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="You Have Been Suspended, Please Contact Admin",
                    detail="You Have Been Suspended, Please Contact Admin",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            serializer = CreateMerchantSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            bvn = serializer.validated_data["bvn"]
            bvn = retrieve_bvn(bvn)
            merchant = serializer.save(agent=agent, dob=bvn.formatted_dob)
            agent.merchants_onboarded += 1
            agent.save()

            # Thread(
            #     target=self.send_merchant_creation_notification, args=(merchant,)
            # ).start()

            try:
                log.info("Trying to Create NQR Merchant")
                merchant = createNqrMerchant(merchant)
                resp = getMerchantQr(merchant)
                log.info(f"Resonse from Generating Merchant QR -> {resp}")
                if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                    qr_data = resp.get("response_description", {}).get("qr_data")
                    resp = generate_qr_image(merchant)
                    log.info(
                        f"Response from Generating qr image and sending to mercant -> {resp}"
                    )
            except Exception:
                pass

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Merchant Successfully Created",
                detail="Merchant Successfully Created",
            )
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except BVN.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable To Retrieve BVN Data, Kindly Re-Initate Merchant Onboarding Flow",
                detail="Unable To Retrieve BVN Data, Kindly Re-Initate Merchant Onboarding Flow",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Creating Merchant -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_merchant_creation_notification(self, merchant: Merchant):
        """
        We expect that the originating parent bank is going to send sms text to the customer's
        mobile, so for account creation notification we send the customer a custom email
        """
        agent = merchant.onboarding_agent
        message = f"A Merchant Account was opened for you by one of our agents, {agent.user.last_name} {agent.user.first_name}. Your account detail are as shown below."

        email_args = {
            "email_from": agent.bank.email,
            "to": merchant.email,
            "subject": f"WELCOME!!! YOU JUST OPENED AN ACCOUNT WITH {agent.bank.name.upper()}.",
            "account_number": merchant.account_number,
            "message": message,
            "customer_first_name": merchant.first_name,
            "img_url": f"{agent.bank.email_img_url}",
            "foreground": agent.bank.email_foreground_color,
            "background": agent.bank.email_background_color,
            "account_name": f"ACCOUNT NAME: {merchant.last_name} {merchant.first_name}",
            "account_number": f"ACCOUNT NUMBER: {merchant.account_number} \nBANK: {merchant.bank.name.upper()}",
        }

        send_email(email_args, "ACCOUNT_OPENING")


class BulkCreateMerchant(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = BulkCreateMerchantSerializer

    def post(self, request):
        try:
            """
            Create merchants in bulk with payload request containing list of merchants, we validate the creating
            agent againt the creating bank admin then we try to create the merchant and incase of failures, we
            update the reason for failure in the returning payload
            """
            log = logging.getLogger("django")
            bankadmin = request.auth.user.bankadmin

            serializer = BulkCreateMerchantSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            merchants: list = serializer.validated_data.get("merchants")
            merchant_creation_list: list = []

            for merchant in merchants:
                _merchant = {**merchant}
                try:
                    agent = get_user_model().objects.get(
                        username=merchant.get("agent_username")
                    )
                    agent = agent.agent
                    bank = Bank.objects.get(id=merchant.get("bank_id"))
                    if bankadmin.bank != agent.bank:
                        _merchant[
                            "reason"
                        ] = "Agent Does Not Exist Within Bank Institution"
                        raise Exception

                    resp = nip_name_enquiry(merchant.get("account_number"), bank)
                    if resp.get("response_code") != settings.SUCCESS_RESPONSE:
                        _merchant["reason"] = resp.get(
                            "response_description",
                            "Unable to retrieve Name Enquiry for account number",
                        )
                        raise Exception

                    bvn = resp.get("response_description", {}).get("bvn")
                    bvn = retrieve_bvn(bvn)
                    created_merchant = Merchant.objects.create(
                        agent=agent,
                        bvn=bvn.bvn,
                        phone_number=merchant.get("phone_number"),
                        dob=bvn.formatted_dob,
                        account_number=merchant.get("account_number"),
                        account_name=resp.get("response_description", {}).get(
                            "account_name"
                        ),
                        bank=bank,
                        first_name=merchant.get("first_name"),
                        last_name=merchant.get("last_name"),
                        email=merchant.get("email"),
                        address=merchant.get("address"),
                        long=merchant.get("long"),
                        lat=merchant.get("lat"),
                    )

                    agent.merchants_onboarded += 1
                    agent.save()

                    try:
                        log.info("Trying to Create NQR Merchant")
                        created_merchant = createNqrMerchant(created_merchant)
                        resp = getMerchantQr(created_merchant)
                        log.info(f"Resonse from Generating Merchant QR -> {resp}")
                        if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                            qr_data = resp.get("response_description", {}).get(
                                "qr_data"
                            )
                            resp = generate_qr_image(created_merchant)
                            log.info(
                                f"Response from Generating qr image and sending to merchant -> {resp}"
                            )
                    except Exception:
                        pass
                except get_user_model().DoesNotExist:
                    _merchant["reason"] = "Agent User Name Does Not Exist"
                except Bank.DoesNotExist:
                    _merchant["reason"] = "Invalid Bank Id"
                except Exception as e:
                    log.info(f"Error generating Merchant {e}")
                    _merchant["reason"] = _merchant.get("reason") or str(e)
                merchant_creation_list.append(_merchant)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Merchants Created Successfully",
                detail={"merchants": merchant_creation_list},
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description=f"Error Creating Merchants -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GenerateBulkCreateMerchantTemplate(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    def get(self, request):
        try:
            """
            Basically it returns an excel sheet containing the required template for
            creating agents in bulk. the excel sheet is stored in-memory
            """
            # Create an in-memory output file for the new workbook.
            output = io.BytesIO()

            workbook = xlsxwriter.Workbook(output)
            worksheet = workbook.add_worksheet("merchants")

            columns = [
                "agent_username",
                "phone_number",
                "account_number",
                "first_name",
                "last_name",
                "email",
                "address",
                "long",
                "lat",
                "bank_id",
            ]

            row = 0

            for col, elem in enumerate(columns):
                worksheet.write(row, col, elem)

            worksheet = workbook.add_worksheet("banks")

            columns = ["id", "bank"]

            for col, elem in enumerate(columns):
                worksheet.write(row, col, elem)

            banks = Bank.objects.all().order_by("name")
            for bank in banks:
                row += 1
                for col, elem in enumerate(columns):
                    if elem == "id":
                        worksheet.write(row, col, f"{bank.id}")
                    elif elem == "bank":
                        worksheet.write(row, col, f"{bank.name}")

            workbook.close()

            # Rewind the buffer.
            output.seek(0)

            # Set up the Http response.
            filename = "bulk_create_merchant_template.xlsx"
            response = HttpResponse(
                output,
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
            response["Content-Disposition"] = f"attachment; filename={filename}"

            return response
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Generating Template {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class UpdateKYCstatus(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = UpdateKYCstatusSerializer

    def post(self, request):
        try:
            """
            Based on Bank Admin manual verification of kyc document, bank admin can update the status of
            an account opening application.
            """
            serializer = UpdateKYCstatusSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            id = serializer.validated_data["id"]
            kyc_verification_status = serializer.validated_data[
                "kyc_verification_status"
            ]
            reason = serializer.validated_data.get("reason")

            account = AgentOnboardedAccount.objects.get(id=id)
            account.kyc_verification_status = kyc_verification_status
            if reason:
                account.reason = reason
            account.save()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="KYC STATUS UPDATED",
                detail=f"KYC STATUS UPDATED TO {kyc_verification_status}",
            )

            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Update KYC status, Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ApproveAccountOpening(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    def get(self, request, id):
        try:
            """
            retrieve the account opening application based on the id, and verify that the
            KYC status is complete before requesting for account opening from the core banking,
            as soon as the core banking respond's successfully, we update the account with the
            new account number and update the status to approved.
            """

            account = AgentOnboardedAccount.objects.get(id=id)
            account.kyc_verification_status = "complete"
            other_details = account.get_extra_fields()

            resp = account_opening(account, other_details)

            if resp.get("response_code") == settings.SUCCESS_RESPONSE and resp.get(
                "response_detail", {}
            ).get("account_number"):
                account.account_number = resp.get("response_detail", {}).get(
                    "account_number"
                )
                account.customer_number = resp.get("response_detail", {}).get(
                    "customer_number"
                )
                account.is_approved = True
                account.save()

                Thread(
                    target=self.send_customer_account_creation_notification,
                    args=(account,),
                ).start()
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Account Opening Approved, and Account has been Created",
                    detail="Account Opening Approved, and Account has been Created",
                )

                return Response(data, status=status.HTTP_200_OK)
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable to Approve Account Opening",
                    detail=resp.get(
                        "response_description", "Unable to Approve Account Opening"
                    ),
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except AgentOnboardedAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Account Opening Application Does Not Exist",
                detail="Account Opening Application Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Approve Account Opening: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_customer_account_creation_notification(
        self, account: AgentOnboardedAccount
    ):
        """
        We expect that the originating parent bank is going to send sms text to the customer's
        mobile, so for account creation notification we send the customer a custom email
        """
        message = f"A {account.account_type.name.upper()} was opened for you by one of our agents, {account.agent.last_name} {account.agent.first_name}. Your account detail are as shown below."

        email_args = {
            "email_from": account.agent.bank.email,
            "to": account.email,
            "subject": f"WELCOME!!! YOU JUST OPENED AN ACCOUNT WITH {account.agent.bank.name.upper()}.",
            "account_number": account.account_number,
            "message": message,
            "customer_first_name": account.first_name,
            "img_url": f"{account.agent.bank.email_img_url}",
            "foreground": account.agent.bank.email_foreground_color,
            "background": account.agent.bank.email_background_color,
            "account_name": f"ACCOUNT NAME: {account.last_name} {account.first_name}",
            "account_number": f"ACCOUNT NUMBER: {account.account_number}",
        }

        send_email(email_args, "ACCOUNT_OPENING")


class RejectAccountOpening(generics.GenericAPIView):
    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = RejectAccountOpeningSerializer

    def post(self, request):
        try:
            """
            Retrieve the account opening model with the id, then set approval status to false,
            with a reason attached for account opening rejection.
            """
            serializer = RejectAccountOpeningSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            id = serializer.validated_data.get("id")
            reason = serializer.validated_data.get("reason")

            account = AgentOnboardedAccount.objects.get(id=id)

            account.reason = reason
            account.kyc_verification_status = "incomplete"
            account.is_approved = False
            account.save()

            Thread(
                target=self.send_account_opening_rejection_notification,
                args=(account,),
            ).start()
        except AgentOnboardedAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Account Opening Application Does Not Exist",
                detail="Account Opening Application Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                detail=serializer.errors,
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Reject Account Opening Request: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_account_opening_rejection_notification(account: AgentOnboardedAccount):
        """
        Notifies Customer of account rejection as well as reasons for rejection
        """

        message = f"Your account opening request for a {account.account_type.name.upper()} by one of our agents, {account.agent.last_name} {account.agent.first_name} was rejected for the reason: {account.reason}."

        email_args = {
            "email_from": account.agent.bank.email,
            "to": account.email,
            "subject": f"SORRY!!! YOUR ACCOUNT OPENING REQUEST WAS REJECTED.",
            "message": message,
            "customer_first_name": account.first_name,
            "img_url": f"{account.agent.bank.email_img_url}",
            "foreground": account.agent.bank.email_foreground_color,
            "background": account.agent.bank.email_background_color,
        }

        send_email(email_args, "ACCOUNT_OPENING_NOTIFICATION")


class ForgotDetails(generics.GenericAPIView):

    permission_classes = [DFAAccessPermission]
    serializer_class = ForgotDetailsSerializer

    def post(self, request):
        """
        Due to the structure of our login credentials, it very possible for the DFA Agent to forget both
        his username as well as password hence the reason for a forgot details endpoint. the app_code field
        is the first set of alphabet characters on the DFA agent username, this is his unique identifier to
        the parent bank.
        """
        try:
            """
            we validate the request payload then we try to retrive the dfa agent from the user database,
            on successuly retrival we generate a new password and send agent details to the agent email.
            """
            serializer = ForgotDetailsSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            email = serializer.validated_data["email"]
            app_code: str = serializer.validated_data["app_code"]

            try:
                Bank.objects.get(alias=app_code.lower())
            except Bank.DoesNotExist:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"{app_code} is not a valid bank alias",
                    detail=f"Invalid Request Payload: app_code -> {app_code}  is not a valid bank alias",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            user = get_user_model().objects.get(
                email=email, username__istartswith=app_code
            )
            agent = user.agent
            password = generate_rand_password()
            user.set_password(password)
            user.save()

            Thread(
                target=self.send_agent_login_credentials, args=(agent, password)
            ).start()

            update_last_login(None, user)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Agent Details has been retrieved and sent to Agent Email",
                detail="Agent Details has been retrieved and sent to Agent Email",
            )
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except get_user_model().DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="DFA Agent Does Not Exist",
                detail="DFA Agent Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Retrieving Agent Details: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_agent_login_credentials(self, agent: Agent, password: str):
        """
        Notifies Bank Agent via mail and text message of the account creation details for login
        """

        message = f"Hello {agent.user.first_name}\n \nYOUR {agent.bank.name.upper()} AGENT ACCOUNT LOGIN CREDEENTIALS WAS UPDATED \nusername: \n \t{agent.user.username}\n \npassword:\n \t{password}\n \nYou can now login on the Agent DFA app"

        email_args = {
            "email_from": agent.bank.email,
            "to": agent.user.email,
            "subject": f"{agent.bank.name.upper()} AGENT ACCOUNT UPDATE",
            "message": message,
        }

        message = f"Hello {agent.user.first_name}\nYour agent login details \nusername:{agent.user.username}\npassword:{password}"

        send_email(email_args, "INTERNAL_NOTIFICATION")

        if settings.APP_ENV == "development":
            send_sms_test(agent.phone_number, message)
            return

        if agent.bank.message_sender_name == "OmoluabiMB":
            send_sms_test(agent.phone_number, message)
        else:
            request_id = get_reference()
            send_sms(
                agent.phone_number,
                message,
                institution_code=agent.bank.code,
                message_sender_name=agent.bank.message_sender_name,
                request_id=request_id,
            )


class CreateOrganization(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = CreateOrganisationSerializer

    def post(self, request):
        try:
            """
            We retrieve the request payload and we verify that the payload is valid then we create
            a username, first name and last name from the request payload options. we create the
            organization as a user then we make the organization a bank admin and assign the role
            of merchant organization. we also make sure we send password details to the merchant
            organization email.
            """
            serializer = CreateOrganisationSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            email = serializer.validated_data["email"]
            organization_name = serializer.validated_data["organization_name"]

            bank = request.auth.user.bankadmin.bank

            first_name, last_name = self.get_name(organization_name)
            username = self.get_username(email, request.auth.user.username)
            password = generate_rand_password()

            organization = self.create_organization(
                username,
                first_name,
                last_name,
                password,
                email,
                organization_name,
                bank,
            )

            Thread(
                target=self.send_organization_account_creation_notification,
                args=(organization, password),
            ).start()

            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Marketer Organization Created Successfully",
                "response_detail": "Marketer Organization Created Successfully",
            }
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Creating Markter Organization : Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def get_name(self, organization_name: str) -> tuple:
        """
        we create a first name and last name combination from the organization name
        """

        name_list = organization_name.split(" ")
        return (name_list[0], "Organization")

    def create_organization(
        self,
        username: str,
        first_name: str,
        last_name: str,
        password: str,
        email: str,
        organization_name: str,
        bank: Bank,
    ):
        """
        Creates a user object for the organization then create a bank admin object for the
        organisation then assign the role of Marketer
        """

        user, created = get_user_model().objects.get_or_create(
            username=username, first_name=first_name, last_name=last_name, email=email
        )

        if not created:
            raise Exception("Organization Already Exists")

        user.set_password(password)
        user.save()
        try:
            organization = BankAdmin.objects.create(
                user=user,
                bank=bank,
                is_bank_admin=True,
                role="marketer",
                organization_name=organization_name,
            )
        except Exception:
            user.delete()
            raise Exception("Unable to Create Organization")

        return organization

    def get_username(self, email: str, auth_username: str):
        """
        we create a username from the combination of the marketers username and the mailing server
        domain of the bank admin.
        """
        email_split = email.split("@")
        auth_username_split = auth_username.split("@")
        return f"{email_split[0]}@{auth_username_split[-1]}"

    def send_organization_account_creation_notification(self, organization, password):
        """
        Sends the Organization Login credentials to the organization's mail
        """
        message = f"Hello {organization.organization_name}\n \nYOUR {organization.bank.name.upper()} ORGANIZATION ACCOUNT WAS CREATED ON OMNI CHANNEL \nusername: \n \t{organization.user.username}\n \npassword:\n \t{password}\n \nYou can now login on the portal on {settings.BANK_ADMIN_PORTAL_BASE_URL}"

        email_args = {
            "email_from": organization.bank.email,
            "to": organization.user.email,
            "subject": f"{organization.bank.name.upper()} ORGANIZATION ACCOUNT CREATION NOTIFICATION",
            "message": message,
        }

        send_email(email_args, "INTERNAL_NOTIFICATION")


class ResendOrganizationLoginCredentials(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    def get(self, request, id):
        try:
            """
            we try to get the Organization based on the Organization Id, we then verifiy that
            the Organization and the request user belong to the same bank before resetting
            the organization password and send the organiztion it's updated login details.
            """
            user = get_user_model().objects.get(id=id)
            if not hasattr(user, "bankadmin"):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Organization Does Not Exist",
                    detail="Organization Does Not Exist",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if user.bankadmin.bank != request.auth.user.bankadmin.bank:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Organization Does Not Exist",
                    detail="Organization Does Not Exist",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            password = generate_rand_password()
            user.set_password(password)
            user.save()

            Thread(
                target=self.send_organization_account_login_credentials,
                args=(user.bankadmin, password),
            ).start()

            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Marketer Organization Created Successfully",
                "response_detail": "Marketer Organization Created Successfully",
            }
            return Response(data, status=status.HTTP_200_OK)

        except get_user_model().DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Organization Does Not Exist",
                detail="Organization Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to send Organization Login Credentials {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def send_organization_account_login_credentials(organization, password):
        """
        Sends the Organization Login credentials to the organization's mail
        """
        message = f"Hello {organization.organization_name}\n \nYOUR {organization.bank.name.upper()} ORGANIZATION ACCOUNT WAS UPDATED ON OMNI CHANNEL \nusername: \n \t{organization.user.username}\n \npassword:\n \t{password}\n \nYou can now login on the portal on {settings.BANK_ADMIN_PORTAL_BASE_URL}"

        email_args = {
            "email_from": organization.bank.email,
            "to": organization.user.email,
            "subject": f"{organization.bank.name.upper()} ORGANIZATION ACCOUNT UPDATE NOTIFICATION",
            "message": message,
        }

        send_email(email_args, "INTERNAL_NOTIFICATION")


class UpdateOrganization(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = CreateOrganisationSerializer

    def put(self, request, id):
        try:
            """
            Retrieves merchant details and updates the merchant and if there is a change in the
            merchant email, we basically update the user details and send the user new login details
            """

            serializer = CreateOrganisationSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            user = get_user_model().objects.get(id=id)
            email = serializer.validated_data["email"]
            organization_name = serializer.validated_data["organization_name"]

            if user.email == email:
                # today is a good day we just update the organization name and we are fine
                user.bankadmin.organization_name = organization_name
                user.bankadmin.save()
                first_name, last_name = self.get_name(organization_name)
                user.first_name = first_name
                user.last_name = last_name
                user.save()
            else:
                # OH FUCK!!! :(
                first_name, last_name = self.get_name(organization_name)
                username = self.get_username(email, request.auth.user.username)
                user.bankadmin.organization_name = organization_name
                user.bankadmin.save()
                user.first_name = first_name
                user.last_name = last_name
                user.username = username
                user.email = email
                password = generate_rand_password()
                user.set_password(password)
                user.save()

                Thread(
                    target=self.send_organization_account_login_credentials,
                    args=(user.bankadmin, password),
                ).start()
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "Marketer Organization Account Updated Successfully",
                "response_detail": "Marketer Organization Account Updated Successfully",
            }
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except get_user_model().DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Organization Does Not Exist",
                detail="Organization Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def get_name(self, organization_name: str) -> tuple:
        """
        we create a first name and last name combination from the organization name
        """

        name_list = organization_name.split(" ")
        return (name_list[0], "Organization")

    def get_username(self, email: str, auth_username: str):
        """
        we create a username from the combination of the marketers username and the mailing server
        domain of the bank admin.
        """
        email_split = email.split("@")
        auth_username_split = auth_username.split("@")
        return f"{email_split[0]}@{auth_username_split[-1]}"

    def send_organization_account_login_credentials(self, organization, password):
        """
        Sends the Organization Login credentials to the organization's mail
        """
        message = f"Hello {organization.organization_name}\n \nYOUR {organization.bank.name.upper()} ORGANIZATION ACCOUNT WAS UPDATED ON OMNI CHANNEL \nusername: \n \t{organization.user.username}\n \npassword:\n \t{password}\n \nYou can now login on the portal on {settings.BANK_ADMIN_PORTAL_BASE_URL}"

        email_args = {
            "email_from": organization.bank.email,
            "to": organization.user.email,
            "subject": f"{organization.bank.name.upper()} ORGANIZATION ACCOUNT UPDATE NOTIFICATION",
            "message": message,
        }

        send_email(email_args, "INTERNAL_NOTIFICATION")


class SuspendUnsuspendOrganization(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    def get(self, request, id):
        try:
            """
            use the id to get Organization and from the query params get the suspend param which is
            expected to be a boolean
            """
            suspend = request.query_params.get("suspend", "").lower()
            if suspend not in ["true", "false"]:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Kindly provide request param <suspend> with boolean in request url",
                    detail="Kindly provide request param <suspend> with boolean in request url",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            organization = get_user_model().objects.get(id=id)
            organization.is_active = False if suspend == "true" else True
            organization.save()
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description=f'Organization Successfully {"Suspended" if suspend == "true" else "Unsuspended"}',
                detail="Organization Status Updated",
            )
            return Response(data, status=status.HTTP_200_OK)
        except get_user_model().DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Organization Does Not Exist",
                detail="Organization Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Deleting Organization: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetOrganizations(viewsets.GenericViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = OrganizationSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = ("=email", "^first_name")

    def get_queryset(self):
        user = self.request.auth.user
        bank = user.bankadmin.bank

        organizations = (
            get_user_model()
            .objects.filter(bankadmin__bank=bank, bankadmin__role="marketer")
            .order_by("date_joined")
        )
        search = self.request.query_params.get("search", None)
        if search is not None:
            # force call the search field filter
            organizations = self.filter_queryset(organizations)
        return organizations

    def list(self, request):
        try:
            """
            Retrieves all the Organizations for a partner bank
            """
            queryset = self.get_queryset()
            queryset = self.paginate_queryset(queryset)
            serializer = OrganizationSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)

            if queryset:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Organizations List",
                    "response_detail": paginated_resp.data,
                }
            else:
                data = {
                    "response_code": settings.SUCCESS_RESPONSE,
                    "response_description": "Empty Organizations List",
                    "response_detail": paginated_resp.data,
                }
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = {
                "reponse_code": settings.FAILED_RESPONSE,
                "response_description": "Error occurred while getting Organizations List",
                "response_detail": str(e),
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, id):
        try:
            """
            We Retrieve a single Organization along side relevant data that is not provided with the
            general list view api
            """

            organization = get_user_model().objects.get(id=id)
            serializer = OrganizationSerializer(organization)

            organization_data = self.get_organization_data(organization.bankadmin)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Organization Details Retrieved Successfully",
                detail={**serializer.data, **organization_data},
            )
            return Response(data, status=status.HTTP_200_OK)
        except get_user_model().DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Organization Does Not Exist",
                detail="Organization Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Retrieving Organization : Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def get_organization_data(self, organization):
        """
        Retrieves relevant organisation data such as no or agents, accounts opened and merchants onboarded in the
        organization.
        """
        agents = Agent.objects.filter(created_by=organization)
        no_of_agents = agents.count()
        accounts_opened = agents.aggregate(value=Sum("accounts_opened")).get("value")
        merchants_onboarded = agents.aggregate(value=Sum("merchants_onboarded")).get(
            "value"
        )

        return {
            "no_of_agents": no_of_agents,
            "accounts_opened": accounts_opened,
            "merchants_onboarded": merchants_onboarded,
        }


class SummaryData(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    start_date = openapi.Parameter(
        "start_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    end_date = openapi.Parameter(
        "end_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    organisation_id = openapi.Parameter(
        "organisation_id",
        openapi.IN_QUERY,
        description="retrieve organisation summary data by bank admin (parse organisation id)",
        type=openapi.TYPE_INTEGER,
        required=False,
    )

    @swagger_auto_schema(manual_parameters=[start_date, end_date, organisation_id])
    def get(self, request):
        try:
            """
            Returns summary of DFA accounts, merchnats and organizations
            """
            start_date = request.query_params.get("start_date", None)
            end_date = request.query_params.get("end_date", None)
            organisation_id = request.query_params.get("organisation_id", None)

            if organisation_id:
                bankadmin = BankAdmin.objects.get(
                    user__id=organisation_id, role="marketer"
                )
            else:
                bankadmin = request.auth.user.bankadmin
            bank = bankadmin.bank

            is_organization_admin = True if bankadmin.role == "marketer" else False

            if is_organization_admin:
                agents = Agent.objects.filter(bank=bank, created_by=bankadmin)
                accounts = AgentOnboardedAccount.objects.filter(
                    agent__bank=bank, agent__created_by=bankadmin
                )
                merchants = Merchant.objects.filter(
                    agent__bank=bank, agent__created_by=bankadmin
                )
            else:
                agents = Agent.objects.filter(bank=bank)
                organizations = BankAdmin.objects.filter(bank=bank, role="marketer")
                accounts = AgentOnboardedAccount.objects.filter(agent__bank=bank)
                merchants = Merchant.objects.filter(agent__bank=bank)

            accounts_data = get_data(accounts)
            merchants_data = get_data(merchants)

            if start_date and end_date:
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
                end_date = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)

                accounts = AgentOnboardedAccount.objects.filter(
                    agent__bank=bank,
                    agent__created_by=bankadmin,
                    date_created__range=[start_date, end_date],
                )
                merchants = Merchant.objects.filter(
                    agent__bank=bank,
                    agent__created_by=bankadmin,
                    date_created__range=[start_date, end_date],
                )

                accounts_range_data = get_data(accounts)
                merchants_range_data = get_data(merchants)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Summary Data Retrieved Successfully",
                detail={
                    "total_agents": agents.count(),
                    "total_organizations": organizations.count()
                    if not is_organization_admin
                    else 1,
                    "accounts_data": accounts_data,
                    "merchants_data": merchants_data,
                    "transaction_range": {
                        "accounts_data": accounts_range_data
                        if start_date and end_date
                        else {},
                        "merchants_data": merchants_range_data
                        if start_date and end_date
                        else {},
                        "loan_analysis": {
                            "volume": 0,
                            "value": 0,
                            "repaid": 0,
                            "pending": 0,
                        },
                        "customer_top_up": {"volume": 0, "value": 0},
                    },
                },
            )
            return Response(data, status=status.HTTP_200_OK)
        except BankAdmin.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Organisation Does Not Exist",
                detail="Organisation Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Error Retrieving Summary Data: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
      

class AgentsCollectionMapping(generics.GenericAPIView):
    
    # permission_classes = [IsBankAdmin]
    required_scopes = ["bank"]

    def post(self, request):
        
        try:
            user = request.auth.user
            bank = user.bankadmin.bank
            bank_admin = user.bankadmin

            app_code = request.auth.user.app_code 
            if app_code == None or app_code == "0000":
                app_code = bank.alias

            agent = request.data.get("agent")
            account_number = request.data.get("account_number")
            
            try:
                agent = User.objects.get(id=agent)
                if not hasattr(agent, "agent"):
                    raise Exception("User has no Agent Profile")
            except ObjectDoesNotExist:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Agent not found",
                    detail="Agent does not exist for this Bank."
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                agent_account = AgentAccount.objects.get(agent__user=agent, bank=bank)
            except AgentAccount.DoesNotExist:
                raise Exception("Agent account not found or does not have an agent account profile.")
            
            institution_code = Bank.objects.filter(Q(nip_code=bank.nip_code) | Q(code=bank.code))
            institution_code = institution_code[0]
            customer_account = get_name_enquiry(
                account_number=account_number, 
                institution_code=institution_code.code,
                sender_account_number=account_number,
                sender_institution_code=institution_code.code,
                type="INTRA",
                app_code=app_code
            )
            
            if customer_account.get("response_code") == settings.SUCCESS_RESPONSE:
                name = customer_account.get("response_description")["account_name"]
                email = customer_account.get("response_description")["email"]

            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Unable to verify account number.",
                    detail=customer_account.get("response_description"),
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            
            try:
                # to ensure an agent is not mapped to another agent
                AgentAccount.objects.get(account_number=account_number)
                data = set_response_data(
                        code=settings.FAILED_RESPONSE,
                        description="Cannot map to another Agent Account.",
                        detail="Cannot map to another Agent Account."
                    )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            except AgentAccount.DoesNotExist:
                pass
                
            map_collections = AgentMappingTable.objects.create(
                customer_account_number=account_number,
                customer_email=email,
                customer_account_name=name,
                bank=bank,
                agent=agent_account,
                bank_admin=bank_admin
            )
            
            response_data = {
                "customer_account": account_number,
                "customer_email": str(map_collections.customer_email),
                "customer_account_name": str(map_collections.customer_account_name),
                "mapped_agent": str(agent_account.agent.user.get_full_name()),
                "bank_admin": str(bank_admin.user.get_full_name()),
                "created": map_collections.created
            }
            
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Customer Account Successfully Mapped to Agent",
                detail=response_data,
            )
            return Response(data, status=status.HTTP_200_OK)
        
        except IntegrityError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Customer already mapped to this Agent",
                detail="Customer already mapped to this Agent"
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Failed to map agent",
                detail=f"{e}"
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        

class GetCustomersMappedToAgent(viewsets.GenericViewSet):
    
    permission_classes = [DFAAccessPermission | IsBankAdmin]
    serializer_class = GetCustomerMappedToAgentSerializer
    pagination_class = CustomPageNumberPagination

    customer_account = openapi.Parameter(
        "customer_account",
        openapi.IN_QUERY,
        description="Customer Account Number",
        type=openapi.TYPE_STRING,
        required=False,
    )
    
    agent_id = openapi.Parameter(
        "agent_id",
        openapi.IN_QUERY,
        description="Agent ID Number",
        type=openapi.TYPE_STRING,
        required=False,
    )
    
    def get_queryset(self):
        
        sender = self.request.auth.user
        
        if hasattr(sender, "bankadmin"):
            bank = sender.bankadmin.bank
            agent_id = self.request.query_params.get('agent_id', None)
            customer_account = self.request.query_params.get("customer_account", None)

            if agent_id is not None:  
                try:
                    agent = Agent.objects.get(user__id=agent_id, bank=bank)
                    print(f"AGENT >> {agent}")
                except AgentAccount.DoesNotExist:
                    raise Exception("Agent account does not exist")
                customers = AgentMappingTable.objects.filter(
                    agent__agent=agent, bank=bank
                ).order_by("-created")
                
                if len(customers) == 0:
                    raise Exception("No Customer is mapped to the Agent Account supplied.")
                
            if customer_account is not None:
                customers = AgentMappingTable.objects.filter(
                    customer_account_number=customer_account, bank=bank
                ).order_by("-created")

                if len(customers) == 0:
                    raise Exception("No mapping or customer account for this customer.")
           
            else:
                customers = AgentMappingTable.objects.filter(bank=bank).order_by("-created")
            
        if hasattr(sender, "agent"):
            customers = AgentMappingTable.objects.filter(
                agent__agent__user=sender
            ).order_by("-created")
            
        return customers
        
    @swagger_auto_schema(manual_parameters=[customer_account, agent_id])
    def list(self, request):
        
        try:
            """ 
            Based on the logged in user, from the token passed, we supply the customers mapped to an agent.
            If the Bank Admin token, all details of the mapping is supplied for the Bank Admin's bank, 
            on the other hand if Agent token, we supply all the customers mapped to the Agent but with a
            bit more limited details for each of the mapping. Also Bank Admins can filter their search to 
            get all customers mapped to a particular Agent Account, rather than all from the bank, by passing the
            Agent Account number in the query parameter.
            """
            
            sender = self.request.auth.user

            queryset = self.get_queryset()
            queryset = self.paginate_queryset(queryset)
            serializer = self.get_serializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)
            
            if queryset:
                    data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description='GET MAPPINGS SUCCESSFUL',
                        detail=paginated_resp.data
                )
            else:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='MAPPINGS LIST EMPTY',
                    detail="No Customer mapped to this Agent" if hasattr(sender, "agent") else "No Mappings created for your Bank"
                )
            return Response(data, status=status.HTTP_200_OK)
        
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='MAPPINGS REQUEST FAILED',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST) 
        

    def retrieve(self, request, id):
        
        try:
            queryset = self.get_queryset()
            instance = queryset.get(pk=id)
            serializer = self.get_serializer(instance)

            data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='GET MAPPING SUCCESSFUL',
                    detail=serializer.data
                )
            return Response(data, status=status.HTTP_200_OK) 
        
        except ObjectDoesNotExist:
            data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description='MAPPING DOES NOT EXIST',
                    detail="Mapping not found"
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='MAPPINGS REQUEST FAILED',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST) 

    
    def get_serializer(self, *args, **kwargs):
        
        sender = self.request.auth.user
        if hasattr(sender, "agent"):
            sender = "AGENT"
        else:
            sender = "BANKADMIN"
        kwargs['context'] = {'sender': sender}
        return GetCustomerMappedToAgentSerializer(*args, **kwargs)


class DeleteMappings(generics.GenericAPIView):
    
     permission_classes = [IsBankAdmin]
     
     def delete(self, request, id):
        
        try:
            bank_admin = request.auth.user.bankadmin
            bank = bank_admin.bank
            mapping = AgentMappingTable.objects.get(id=id, bank=bank)
            mapping.delete()
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Mapping Deleted Successfully',
                detail='Mapping deleted successfully'
            )
            return Response(data, status=status.HTTP_200_OK)
        except AgentMappingTable.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Mapping Does Not Exist',
                detail='Mapping does not exist'
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Failed to delete Mapping.',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    
class GetCollectionHistory(viewsets.GenericViewSet):
    
    permission_classes = [DFAAccessPermission | IsBankAdmin]
    serializer_class = CollectionHistorySerializer
    pagination_class = CustomPageNumberPagination
    
    start_date = openapi.Parameter(
        "start_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    end_date = openapi.Parameter(
        "end_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    
    customer_account = openapi.Parameter(
        "customer_account",
        openapi.IN_QUERY,
        description="Customer Account Number",
        type=openapi.TYPE_STRING,
        required=False,
    )
    
    agent_id = openapi.Parameter(
        "agent_id",
        openapi.IN_QUERY,
        description="Agent ID Number",
        type=openapi.TYPE_STRING,
        required=False,
    )
    
    def get_queryset(self):
        
        sender = self.request.auth.user

        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)
        customer_account = self.request.query_params.get('customer_account', None)
        agent_id = self.request.query_params.get('agent_id', None)
        
        if start_date and end_date:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
 
        
        if hasattr(sender, "bankadmin"):
            bank = sender.bankadmin.bank
            if agent_id:
                try:
                    Agent.objects.get(user__id=agent_id, bank=bank)
                except Agent.DoesNotExist:
                    raise Exception("Agent Profile Does Not exist")
                
            if customer_account:
                try: 
                    CustomerAccount.objects.get(bank=bank, account_number=customer_account)
                except CustomerAccount.DoesNotExist:
                    raise Exception("Customer Account Does Not exist")
                
            if start_date and end_date and agent_id:
                transactions = Collection.objects.filter(
                    agent__user__id=agent_id, bank=bank,
                    date_created__range=[start_date, end_date]).order_by("-date_created"
                )
                if len(transactions) == 0:
                    raise Exception("No transaction history found for the date range or Agent.")
                
            elif start_date and end_date and customer_account:
                transactions = Collection.objects.filter(
                    customer_account_number=customer_account, bank=bank, 
                    date_created__range=[start_date, end_date]).order_by("-date_created"
                )
                if len(transactions) == 0:
                    raise Exception("No transaction history found for the date range or Customer Account.")
                
            elif start_date and end_date:
                transactions = Collection.objects.filter(
                    bank=bank, date_created__range=[start_date, end_date]).order_by("-date_created"
                )
                if len(transactions) == 0:
                    raise Exception("No transaction history found for the date range.")
                
            elif agent_id:
                transactions = Collection.objects.filter(
                    agent__user__id=agent_id, bank=bank
                ).order_by("-date_created")
                if len(transactions) == 0:
                    raise Exception("No transaction history found for the Agent.")
                
            elif customer_account:
                transactions = Collection.objects.filter(
                    customer_account_number=customer_account, bank=bank, 
                ).order_by("-date_created")
                if len(transactions) == 0:
                    raise Exception("No transaction history found for the Customer Account.")
            else:
                transactions = Collection.objects.filter(bank=bank).order_by("-date_created")
                
            return transactions
        
        if hasattr(sender, "agent"):
            if start_date and end_date and customer_account:
                transactions = Collection.objects.filter(
                    agent__user=sender, customer_account_number=customer_account,
                    date_created__range=[start_date, end_date]).order_by("-date_created")
                if len(transactions) == 0:
                    raise Exception("No transaction history found for the date range or Customer Account.")
                
            elif start_date and end_date:
                transactions = Collection.objects.filter(
                    agent__user=sender, date_created__range=[start_date, end_date]).order_by("-date_created")
                if len(transactions) == 0:
                    raise Exception("No transaction history found for the date range")
                
            elif customer_account:
                transactions = Collection.objects.filter(
                    agent__user=sender, customer_account_number=customer_account).order_by("-date_created")
                if len(transactions) == 0:
                    raise Exception("No transaction history found for Customer Account")
                
            else:
                transactions = Collection.objects.filter(agent__user=sender).order_by("-date_created")
                
            return transactions
        
        
    @swagger_auto_schema(manual_parameters=[start_date, end_date, customer_account, agent_id])
    def list(self, request):
       
        try:
            """ 
            Returns history of an Agent or Bank for bank admin, based on parameters specified in the query param
            or all returns history for Agent or Bank for bank admin, if none of the parameters are specified. 
            """
            
            sender = self.request.auth.user

            queryset = self.get_queryset()
            queryset = self.paginate_queryset(queryset)
            serializer = CollectionHistorySerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)
            
            if queryset:
                    data = set_response_data(
                        code=settings.SUCCESS_RESPONSE,
                        description='GET COLLECTION HISTORY SUCCESSFUL',
                        detail=paginated_resp.data
                )
            else:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='COLLECTION HISTORY LIST EMPTY',
                    detail="No history found"
                )
            return Response(data, status=status.HTTP_200_OK)
            
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='COLLECTION HISTORY REQUEST FAILED',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST) 
        
    def retrieve(self, request, user_ref):
        
        try:
            """ 
            Retrieves a particular collection transaction for a user_ref passed.
            Query result is based on the filter result returned from the get_queryset method.
            """
            queryset = self.get_queryset()
            instance = queryset.get(user_ref=user_ref)
            serializer = CollectionHistorySerializer(instance)
            
            data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='RETRIEVE COLLECTION HISTORY SUCCESSFUL',
                    detail=serializer.data
            )
            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description='HISTORY DOES NOT EXIST',
                    detail="History not found"
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description='FAILED TO RETRIEVE HISTORY',
                    detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


# USE AGENT ID INSTEAD AS QUERY PARAM TO QUERY GET MAPPINGS
# ALSO QUERY WITH CUSTOMERS
# ADD RETRIEVE ENDPOINT TO GET MAPPINGS
# TEST AND ENSURE GET MAPPING RETURNS AGENT FULL NAME NOT USERNAME
# ALSO TEST ND ENSURE THIS FOR WHEN MAPPING IS CREATED
