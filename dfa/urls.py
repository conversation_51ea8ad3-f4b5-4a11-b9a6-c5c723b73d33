from common.views import NameEnquiry
from customer.views import SetPin
from django.urls import path

from verdant.views import VerifyPhoneNumberOTP

from .views import (
    ApproveAccountOpening,
    BulkCreateAgent,
    BulkCreateMerchant,
    CreateAccount,
    CreateAccountV2,
    CreateAgent,
    CreateOrganization,
    ForgotDetails,
    GenerateBulkCreateAgentTemplate,
    GenerateBulkCreateMerchantTemplate,
    GetAccountDetails,
    GetAgentAccountBalance,
    AgentOnboardedAccounts,
    AgentOnboardedMerchants,
    GetAgentProfile,
    GetAgents,
    GetOrganizations,
    RejectAccountOpening,
    ResendOrganizationLoginCredentials,
    RetryCreateAccount,
    SuspendAndUnSuspendAgent,
    SuspendUnsuspendOrganization,
    UpdateAgent,
    SendPhoneNumberOTP,
    SendEmailOTP,
    CreateMerchant,
    UpdateKYCstatus,
    AddOrUpdateKYCImages,
    UpdateOrganization,
    SummaryData,
    AgentsCollectionMapping,
    GetCustomersMappedToAgent,
    DeleteMappings,
    GetCollectionHistory,
)
from bank.views import ResetPassword as UpdatePassword

urlpatterns = [
    path("create_agent/", CreateAgent.as_view(), name="create_agent"),
    path("get_agents/", GetAgents.as_view({"get": "list"}), name="get_agents"),
    path(
        "get_agent/<int:id>",
        GetAgents.as_view({"get": "retrieve"}),
        name="get_agent_by_id",
    ),
    path("update_agent/<int:id>/", UpdateAgent.as_view(), name="update_agent"),
    path(
        "get_agent_onboarded_accounts/<int:id>/",
        AgentOnboardedAccounts.as_view({"get": "retrieve"}),
        name="get_agent_onboarded_accounts_by_agent_id",
    ),
    path(
        "get_agent_onboarded_accounts/",
        AgentOnboardedAccounts.as_view({"get": "list"}),
        name="get_agent_onboarded_accounts",
    ),
    path(
        "delete_agent_onboarded_account/<int:id>/",
        AgentOnboardedAccounts.as_view({"delete": "destroy"}),
        name="delete_agent_onboarded_account",
    ),
    path(
        "get_agent_onboarded_merchants/<int:id>/",
        AgentOnboardedMerchants.as_view({"get": "retrieve"}),
        name="get_agent_onboarded_merchants_by_agent_id",
    ),
    path(
        "get_agent_onboarded_merchants/",
        AgentOnboardedMerchants.as_view({"get": "list"}),
        name="get_agent_onboarded_merchants",
    ),
    path(
        "get_account_details/<int:id>/",
        GetAccountDetails.as_view(),
        name="get_account_details",
    ),
    path("get_agent_profile/", GetAgentProfile.as_view(), name="get_agent_profile"),
    path(
        "get_agent_account_balance/<str:account_number>/",
        GetAgentAccountBalance.as_view(),
        name="get_agent_account_balance",
    ),
    path("create_account/", CreateAccount.as_view(), name="create_account"),
    path("create_account_v2/", CreateAccountV2.as_view(), name="create_account_v2"),
    path(
        "suspend_and_unsuspend_agent/<int:id>/",
        SuspendAndUnSuspendAgent.as_view(),
        name="suspend_agent",
    ),
    path(
        "send_user_sms_otp/<str:phone_number>/",
        SendPhoneNumberOTP.as_view(),
        name="dfa_send_user_otp",
    ),
    path(
        "send_user_email_otp/", SendEmailOTP.as_view(), name="dfa_send_user_email_otp"
    ),
    path("create_merchant/", CreateMerchant.as_view(), name="create_merchant"),
    path("update_kyc_status/", UpdateKYCstatus.as_view(), name="update_kyc_status"),
    path(
        "approve_account_opening/<int:id>/",
        ApproveAccountOpening.as_view(),
        name="approve_account_opening",
    ),
    path("update_password/", UpdatePassword.as_view(), name="dfa_update_password"),
    path("forgot_details/", ForgotDetails.as_view(), name="forgot_details"),
    path(
        "create_organization/", CreateOrganization.as_view(), name="create_organization"
    ),
    path(
        "resend_organization_login_details/<int:id>/",
        ResendOrganizationLoginCredentials.as_view(),
        name="resend_organization_login_details",
    ),
    path(
        "update_organization/<int:id>/",
        UpdateOrganization.as_view(),
        name="update_organization",
    ),
    path(
        "suspend_unsuspend_organization/<int:id>/",
        SuspendUnsuspendOrganization.as_view(),
        name="suspend_unsuspened_organization",
    ),
    path(
        "get_organizations/",
        GetOrganizations.as_view({"get": "list"}),
        name="get_organizations",
    ),
    path(
        "get_organization/<int:id>/",
        GetOrganizations.as_view({"get": "retrieve"}),
        name="get_organization_by_id",
    ),
    path("summary_data/", SummaryData.as_view(), name="dfa_summary_data"),
    path("set_pin/", SetPin.as_view(), name="dfa_set_pin"),
    path("name_enquiry/", NameEnquiry.as_view(), name="dfa_name_enquiry"),
    path(
        "validate_otp/",
        VerifyPhoneNumberOTP.as_view(),
        name="dfa_validate_otp",
    ),
    path("bulk_create_agent/", BulkCreateAgent.as_view(), name="dfa_bulk_create_agent"),
    path(
        "retry_create_account/<int:id>/",
        RetryCreateAccount.as_view(),
        name="dfa_retry_create_account",
    ),
    path(
        "bulk_create_merchant/",
        BulkCreateMerchant.as_view(),
        name="dfa_bulk_create_merchant",
    ),
    path(
        "generate_create_bulk_agent_template/",
        GenerateBulkCreateAgentTemplate.as_view(),
        name="dfa_generate_create_bulk_agent_template",
    ),
    path(
        "generate_create_bulk_merchant_template/",
        GenerateBulkCreateMerchantTemplate.as_view(),
        name="dfa_generate_create_bulk_merchant_template",
    ),
    path(
        "reject_account_opening/",
        RejectAccountOpening.as_view(),
        name="dfa_reject_account_opening",
    ),
    path(
        "add_update_kyc_images/<int:id>",
         AddOrUpdateKYCImages.as_view(),
         name="add_or_update_kyc_images"
    ),
    path(
        "map_agents_customer_accounts/",
        AgentsCollectionMapping.as_view(),
        name="mapping_agents_for_collection"
    ),
    path(
        "get_mapped_customers/",
        GetCustomersMappedToAgent.as_view({"get": "list"}),
        name="get_mapped_customers"
    ),
    path(
        "retrieve_mapped_customers/<int:id>/",
        GetCustomersMappedToAgent.as_view({"get": "retrieve"}),
        name="retrieve_mapped_customers"
    ),
    path(
        "delete_mappings/<int:id>",
        DeleteMappings.as_view(),
        name="delete_mappings"
        ),
    path(
        "get_collection_history/",
        GetCollectionHistory.as_view({"get": "list"}),
        name="get_collection_history"
    ),
    path(
        "retrieve_collection_history/<str:user_ref>",
        GetCollectionHistory.as_view({"get": "retrieve"}),
        name="retrieve_collection_history"
    )
]
