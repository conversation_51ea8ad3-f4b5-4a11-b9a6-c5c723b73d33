import json
import uuid
from user_profile.models import BankAdmin
from common.validators import validate_phone_number

from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _

from bank.models import Bank, BankAccountType


# Create your models here.
class Agent(models.Model):
    class Meta:
        db_table = "omni_bank_agent"

    ACTIVE = "A"
    INACTIVE = "I"

    STATUS_CHOICES = (
        (ACTIVE, "Active"),
        (INACTIVE, "Inactive"),
    )

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True
    )
    phone_number = models.CharField(
        max_length=16, validators=[validate_phone_number], blank=True, null=True
    )
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, null=True)
    reset_password = models.BooleanField(default=False)
    accounts_opened = models.PositiveSmallIntegerField(default=0)
    merchants_onboarded = models.PositiveSmallIntegerField(default=0)
    pin = models.CharField(max_length=250, default="", blank=True, null=True)
    status = models.CharField(
        max_length=1,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    created_by = models.ForeignKey(BankAdmin, on_delete=models.CASCADE, null=True)
    
    def get_full_name(self):
        """
        Return the first_name plus the last_name, with a space in between.
        """
        full_name = f"{self.user.first_name} {self.user.last_name}"
        return full_name

    def __str__(self) -> str:
        return f"{self.user.last_name} {self.user.first_name} {self.user.username}"


class AgentAccount(models.Model):
    class Meta:
        verbose_name = "Agent Account"
        verbose_name_plural = "Agent Accounts"

    ACTIVE = "A"
    INACTIVE = "I"
    STATUS_CHOICES = (
        (ACTIVE, "Active"),
        (INACTIVE, "Inactive"),
    )

    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    account_number = models.CharField(max_length=15, unique=True, db_index=True)
    account_type = models.ForeignKey(
        BankAccountType, on_delete=models.CASCADE, to_field="code"
    )
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, null=True)
    daily_limit = models.DecimalField(max_digits=10, decimal_places=2, default="0.00")
    daily_cummulative = models.DecimalField(
        max_digits=10, decimal_places=2, default="0.00"
    )
    status = models.CharField(
        max_length=1,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    date_created = models.DateTimeField("date created", auto_now_add=True)

    def __str__(self) -> str:
        return self.account_number


class AgentOnboardedAccount(models.Model):

    INCOMPLETE = "incomplete"
    PENDING = "pending"
    COMPLETE = "complete"
    MALE = "MALE"
    FEMALE = "FEMALE"
    INTERNATIONAL_PASSPORT = "international_passport"
    DRIVERS_LICENSE = "drivers_license"
    NATIONAL_ID = "national_id"
    VOTERS_CARD = "voters_card"

    KYC_VERIFICATION_STATUS_CHOICES = (
        (INCOMPLETE, "Incomplete"),
        (PENDING, "Pending"),
        (COMPLETE, "Complete"),
    )
    GENDER_CHOICES = (
        (FEMALE, "Female"),
        (MALE, "Male"),
    )
    MEANS_OF_ID_CHOICES = (
        (INTERNATIONAL_PASSPORT, "International Passport"),
        (DRIVERS_LICENSE, "Drivers License"),
        (NATIONAL_ID, "National ID"),
        (VOTERS_CARD, "Voters Card"),
    )

    class Meta:
        verbose_name = "Agent Onboarded Account"
        verbose_name_plural = "Agent Onboarded Accounts"

    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    bvn = models.CharField(max_length=11, db_index=True)
    first_name = models.CharField(_("first name"), max_length=150)
    last_name = models.CharField(_("last name"), max_length=150)
    middle_name = models.CharField(_("middle name"), max_length=150, blank=True)
    account_number = models.CharField(max_length=15, db_index=True, blank=True, null=True)
    customer_number = models.CharField(max_length=30, blank=True, null=True)
    account_type = models.ForeignKey(
        BankAccountType, on_delete=models.CASCADE, to_field="code"
    )
    email = models.EmailField(_("email address"))
    phone_number = models.CharField(max_length=16, validators=[validate_phone_number])
    address = models.TextField(blank=False)
    city = models.CharField(max_length=30)
    state = models.CharField(max_length=30)
    gender = models.CharField(max_length=6, choices=GENDER_CHOICES, blank=True)
    dob = models.CharField(max_length=25)

    kyc_verification_status = models.CharField(
        max_length=10,
        choices=KYC_VERIFICATION_STATUS_CHOICES,
        default=INCOMPLETE,
    )
    reason = models.CharField(
        max_length=400, default="KYC DOCUMENTS PENDING VERIFICATION"
    )
    means_of_id = models.CharField(max_length=25, choices=MEANS_OF_ID_CHOICES)
    id_base_64 = models.TextField(blank=True, null=True)
    id_issue_date = models.DateField(null=True, blank=True)
    id_expiry_date = models.DateField(null=True, blank=True)
    id_number = models.CharField(max_length=50)
    signature_base_64 = models.TextField(blank=True, null=True)
    profile_picture_base_64 = models.TextField(blank=True, null=True)

    is_approved = models.BooleanField(default=False)
    date_created = models.DateTimeField(auto_now_add=True, null=True)

    extra_fields = models.TextField(blank=True, null=True)  # Add this field

    def set_extra_fields(self, data):
        """Save extra fields as a JSON string."""
        self.extra_fields = json.dumps(data)

    def get_extra_fields(self):
        """Retrieve extra fields as a dictionary."""
        if self.extra_fields:
            return json.loads(self.extra_fields)
        return {}


class Merchant(models.Model):

    agent = models.ForeignKey(Agent, on_delete=models.CASCADE)
    bvn = models.CharField(max_length=11, db_index=True)
    phone_number = models.CharField(max_length=16, validators=[validate_phone_number])
    dob = models.CharField(verbose_name="Date of birth", max_length=25)
    account_number = models.CharField(max_length=15, db_index=True)
    account_name = models.CharField(max_length=50, blank=True, null=True)
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE)
    merchant_number = models.CharField(
        max_length=20, unique=True, blank=True, null=True
    )
    merchant_qr_data = models.TextField(blank=True, default="")
    merchant_sub_number = models.CharField(
        max_length=20, unique=True, blank=True, null=True
    )
    first_name = models.CharField(_("first name"), max_length=150)
    last_name = models.CharField(_("last name"), max_length=150)
    email = models.EmailField(_("email address"))
    address = models.CharField(max_length=500, blank=True, null=True)
    long = models.CharField(max_length=15, blank=True, null=True)
    lat = models.CharField(max_length=15, blank=True, null=True)
    is_approved = models.BooleanField(default=True, editable=False)
    date_created = models.DateTimeField(auto_now_add=True, null=True)

    # class Meta:
    #     constraints = [
    #         models.UniqueConstraint(
    #             fields=["account_number", "bank"], name="unique_merchant_account"
    #         )
    #     ]
    

class AgentMappingTable(models.Model):
    customer_account_number = models.CharField(max_length=15)
    customer_email = models.EmailField(blank=True)
    customer_account_name = models.CharField(max_length=200)  
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE) 
    bank_admin = models.ForeignKey(BankAdmin, on_delete=models.CASCADE, null=True)
    agent = models.ForeignKey(AgentAccount, on_delete=models.CASCADE) 
    created = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.customer_email} -> {self.agent.agent.user}"
    
    class Meta:
        verbose_name = "Agent Mapping"
        verbose_name_plural = "Agent Mappings"
        
        constraints = [
            models.UniqueConstraint(
                fields=["customer_account_number", "agent"], name="unique_customer_account_agent"
            )
        ]
