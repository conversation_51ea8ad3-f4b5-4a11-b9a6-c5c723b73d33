from decimal import Decimal
from bank.models import Bank, BankAccountType
from customer.models import CustomerAccount

from common.validators import validate_name, validate_phone_number
from rest_framework import serializers

from django.contrib.auth import get_user_model

from .models import Agent, AgentAccount, AgentOnboardedAccount, Merchant

from .util import (get_base64_image_from_s3)


User = get_user_model()


class CreateAgentSerializer(serializers.Serializer):

    last_name = serializers.CharField(max_length=20, validators=[validate_name])
    first_name = serializers.Char<PERSON>ield(max_length=20, validators=[validate_name])
    phone_number = serializers.Char<PERSON>ield(
        max_length=16, validators=[validate_phone_number]
    )
    email = serializers.EmailField(required=True)
    account_number = serializers.CharField(
        max_length=15, required=False, allow_blank=True
    )
    transaction_limit = serializers.DecimalField(
        max_digits=15, decimal_places=2, required=False
    )

    class Meta:
        fields = [
            "first_name",
            "last_name",
            "email",
            "phone_number",
            "account_number",
            "transaction_limit",
        ]

    def validate(self, attrs):
        validated_data = super().validate(attrs)

        account_number = validated_data.get("account_number", None)
        transaction_limit = validated_data.get("transaction_limit", None)
        # if either is not None then both of them must not be None
        if account_number or transaction_limit:
            if not account_number:
                raise serializers.ValidationError(
                    {
                        "account_number": "account number must exist for transaction limit"
                    }
                )
            if not transaction_limit:
                raise serializers.ValidationError(
                    {
                        "transaction_limit": "transaction limit must exist for account number"
                    }
                )
        return validated_data


class BulkCreateAgentSerializer(serializers.Serializer):
    agents = CreateAgentSerializer(many=True)

    class Meta:
        fields = ["agents"]

    def create(self, validated_data):
        return Agent.objects.bulk_create(validated_data)


class AgentSerializer(serializers.ModelSerializer):
    phone_number = serializers.SerializerMethodField("get_phone_number")
    accounts_opened = serializers.SerializerMethodField("get_accounts_opened")
    merchants_onboarded = serializers.SerializerMethodField("get_merchants_onboarded")
    account_number = serializers.SerializerMethodField("get_account_number")
    daily_limit = serializers.SerializerMethodField("get_daily_limit")
    status = serializers.SerializerMethodField("get_status")

    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "first_name",
            "last_name",
            "email",
            "is_active",
            "last_login",
            "phone_number",
            "date_joined",
            "accounts_opened",
            "merchants_onboarded",
            "account_number",
            "daily_limit",
            "status",
        ]

    def get_phone_number(self, obj):
        return obj.agent.phone_number

    def get_accounts_opened(self, obj):
        return obj.agent.accounts_opened

    def get_merchants_onboarded(self, obj):
        return obj.agent.merchants_onboarded

    def get_account_number(self, obj):
        try:
            account = AgentAccount.objects.get(agent=obj.agent)
            return account.account_number
        except Exception:
            return ""

    def get_daily_limit(self, obj):
        try:
            account = AgentAccount.objects.get(agent=obj.agent)
            return account.daily_limit
        except Exception:
            return ""

    def get_status(self, obj):
        return obj.agent.status


class AccountDetailSerializer(serializers.ModelSerializer):

    profile = serializers.SerializerMethodField("get_profile")
    next_of_kin = serializers.SerializerMethodField("get_next_of_kin")
    kyc_details = serializers.SerializerMethodField("get_kyc_details")

    class Meta:
        model = CustomerAccount
        fields = ["profile", "next_of_kin", "kyc_details"]

    def get_profile(self, obj):
        return {
            "full_name": f"{obj.profile.customer.last_name} {obj.profile.customer.first_name}",
            "dob": obj.profile.dob,
            "phone_number": obj.profile.phone_number,
            "email": obj.profile.customer.email,
            "gender": obj.profile.gender,
        }

    def get_next_of_kin(self, obj):
        if hasattr(obj.profile, "nextofkin"):
            next_of_kin = obj.profile.nextofkin
            return {
                "full_name": f"{next_of_kin.last_name} {next_of_kin.first_name}",
                "dob": next_of_kin.dob,
                "phone_number": next_of_kin.phone_number,
                "email": next_of_kin.email,
                "gender": next_of_kin.gender,
            }
        return {}

    def get_kyc_details(self, obj):
        if hasattr(obj.profile, "kycdocs"):
            kyc_docs = obj.profile.kycdocs
            return {
                "means_of_id": kyc_docs.means_of_id,
                "id_base_64": kyc_docs.id_base_64,
                "signature_base_64": kyc_docs.signature_base_64,
                "profile_picture_base_64": kyc_docs.profile_picture_base_64,
            }
        return {}


class AgentAccountSerializer(serializers.ModelSerializer):

    account_name = serializers.SerializerMethodField("get_account_name")
    account_type_code = serializers.SerializerMethodField("get_account_type_code")

    class Meta:
        model = AgentAccount
        fields = [
            "id",
            "account_number",
            "account_name",
            "status",
            "date_created",
            "account_type_code",
        ]

    def get_account_name(self, obj):
        return f"{obj.agent.user.last_name} {obj.agent.user.first_name}"

    def get_account_type_code(self, obj):
        return (obj.account_type.code,)


class CreateAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = AgentOnboardedAccount
        exclude = [
            "agent",
            "kyc_verification_status",
            "is_approved",
            "account_number",
            "customer_number",
            "reason",
            "id_base_64",
            "signature_base_64",
            "profile_picture_base_64",
        ]


class CreateAccountSerializerV2(serializers.ModelSerializer):

    SAVINGS = "savings"
    CURRENT = "current"
    SINGLE = "single"
    MARRIED = "married"
    MARRIED_CHOICES = (
        (SINGLE, "Single"),
        (MARRIED, "Married")
    )
    ACCOUNT_TYPE = (
        (SAVINGS, "Savings"),
        (CURRENT, "Current")
    )

    account_type = serializers.ChoiceField(choices=ACCOUNT_TYPE, required=True)
    marital_status = serializers.ChoiceField(choices=MARRIED_CHOICES, required=True)
    tin = serializers.CharField(required=True) # CHECK IF REQUIRED 
    nin = serializers.CharField(required=True)
    sector = serializers.CharField(required=True) # CHECK IF REQUIRED
    place_of_birth = serializers.CharField(required=True)

    next_kin_address = serializers.CharField(required=False)
    next_kin_name = serializers.CharField(required=False)
    next_kin_relationship = serializers.CharField(required=False)
    next_kin_phone = serializers.CharField(required=False)
    next_kin_email = serializers.CharField(required=False)

    class Meta:
        model = AgentOnboardedAccount
        fields = [
            "bvn",
            "first_name",
            "last_name",
            "middle_name",
            "account_type",
            "email",
            "phone_number",
            "address",
            "city",
            "state",
            "gender",
            "dob",
            "means_of_id",
            "id_issue_date",
            "id_expiry_date",
            "id_number",
            "marital_status",
            "tin",
            "nin",
            "sector",
            "next_kin_address",
            "next_kin_name",
            "next_kin_relationship",
            "next_kin_phone",
            "next_kin_email",
            "place_of_birth",
        ]
        # exclude = [
        #     "agent",
        #     "kyc_verification_status",
        #     "is_approved",
        #     "account_number",
        #     "customer_number",
        #     "reason",
        #     "id_base_64",
        #     "signature_base_64",
        #     "profile_picture_base_64",
        #     "extra_fields"
        # ]

    
class EmailOTPSerializer(serializers.Serializer):
    email = serializers.EmailField()
    first_name = serializers.CharField(max_length=30)

    class Meta:
        fields = ["email", "first_name"]


class CreateMerchantSerializer(serializers.ModelSerializer):
    class Meta:
        model = Merchant
        exclude = [
            "agent",
            "dob",
            "account_name",
            "merchant_number",
            "merchant_qr_data",
            "merchant_sub_number",
            "date_created",
        ]


class BulkMerchantSerializer(serializers.Serializer):

    agent_username = serializers.CharField(max_length=25, required=True)
    phone_number = serializers.CharField(
        max_length=16, validators=[validate_phone_number]
    )
    account_number = serializers.CharField(max_length=15, required=True)
    first_name = serializers.CharField(max_length=150, required=True)
    last_name = serializers.CharField(max_length=150, required=True)
    email = serializers.EmailField(required=True)
    address = serializers.CharField(max_length=500, required=False, allow_blank=True)
    long = serializers.CharField(max_length=10, required=False, allow_blank=True)
    lat = serializers.CharField(max_length=10, required=False, allow_blank=True)
    bank_id = serializers.IntegerField(required=True)

    class Meta:
        fields = [
            "agent_username",
            "phone_number",
            "account_number",
            "first_name",
            "last_name",
            "email",
            "address",
            "long",
            "lat",
            "bank_id",
        ]

    def validate(self, attrs):
        validated_data = super().validate(attrs)

        long = validated_data.get("long")
        lat = validated_data.get("lat")

        if long:
            # try to parse long into a decimal, it fails raise a validation error
            try:
                Decimal(long)
            except Exception:
                raise serializers.ValidationError({"long": "Invalid longitude data"})
        if lat:
            # try to parse lat into decimal, if it fails raise a validation error
            try:
                Decimal(lat)
            except Exception:
                raise serializers.ValidationError({"lat": "Invalid Latitude data"})
        return validated_data


class BulkCreateMerchantSerializer(serializers.Serializer):

    merchants = BulkMerchantSerializer(many=True)

    class Meta:
        fields = ["merchants"]


class AgentOnboardedAccountSerializer(serializers.ModelSerializer):

    details = serializers.SerializerMethodField("get_details")
    status = serializers.SerializerMethodField("get_status")
    kyc_data = serializers.SerializerMethodField("get_kyc_data")

    class Meta:
        model = AgentOnboardedAccount
        fields = [
            "details",
            "status",
            "kyc_data",
        ]

    def get_details(self, obj):
        return {
            "id": obj.id,
            "bvn": obj.bvn,
            "first_name": obj.first_name,
            "last_name": obj.last_name,
            "middle_name": obj.middle_name,
            "account_number": obj.account_number,
            "account_type": obj.account_type.name,
            "email": obj.email,
            "phone_number": obj.phone_number,
            "address": obj.address,
            "city": obj.city,
            "state": obj.state,
            "gender": obj.gender,
            "dob": obj.dob,
        }

    def get_status(self, obj):
        return {
            "kyc_verification_status": obj.kyc_verification_status,
            "is_approved": obj.is_approved,
            "reason": obj.reason,
        }

    def get_kyc_data(self, obj):
        
        return {
            "means_of_id": obj.means_of_id,
            "id_base_64": get_base64_image_from_s3(obj.id_base_64),
            "id_issue_date": obj.id_issue_date,
            "id_expiry_date": obj.id_expiry_date,
            "id_number": obj.id_number,
            "signature_base_64": get_base64_image_from_s3(obj.signature_base_64),
            "profile_picture_base_64": get_base64_image_from_s3(obj.profile_picture_base_64),
        }


class AgentOnboardedAccountListSerializer(serializers.ModelSerializer):

    details = serializers.SerializerMethodField("get_details")
    status = serializers.SerializerMethodField("get_status")

    class Meta:
        model = AgentOnboardedAccount
        fields = [
            "details",
            "status",
        ]

    def get_details(self, obj):
        return {
            "id": obj.id,
            "bvn": obj.bvn,
            "first_name": obj.first_name,
            "last_name": obj.last_name,
            "middle_name": obj.middle_name,
            "account_number": obj.account_number,
            "account_type": obj.account_type.name,
            "email": obj.email,
            "phone_number": obj.phone_number,
            "address": obj.address,
            "city": obj.city,
            "state": obj.state,
            "gender": obj.gender,
            "dob": obj.dob,
        }

    def get_status(self, obj):
        return {
            "kyc_verification_status": obj.kyc_verification_status,
            "is_approved": obj.is_approved,
            "reason": obj.reason,
        }


class AgentOnboardedMerchantSerializer(serializers.ModelSerializer):

    details = serializers.SerializerMethodField("get_details")

    class Meta:
        model = Merchant
        fields = ["details"]

    def get_details(self, obj):
        return {
            "bvn": obj.bvn,
            "phone_number": obj.phone_number,
            "dob": obj.dob,
            "account_number": obj.account_number,
            "bank": obj.bank.name,
            "first_name": obj.first_name,
            "last_name": obj.last_name,
            "email": obj.email,
            "is_approved": obj.is_approved,
        }


class UpdateKYCstatusSerializer(serializers.ModelSerializer):
    reason = serializers.CharField(max_length=400, required=False)

    class Meta:
        model = AgentOnboardedAccount
        fields = ["id", "kyc_verification_status", "reason"]

    def validate(self, attrs):
        validated_data = super().validate(attrs)
        kyc_status = validated_data.get("kyc_verification_status")
        reason = validated_data.get("reason")
        if kyc_status == "incomplete" and not reason:
            raise serializers.ValidationError(
                {"reason": "A reason must exist for setting kyc status to incomplete"}
            )
        if kyc_status == "complete":
            validated_data["reason"] = "CUSTOMER KYC DATA VERIFICATION COMPLETE"

        return validated_data


class ForgotDetailsSerializer(serializers.Serializer):

    email = serializers.EmailField()
    app_code = serializers.CharField(min_length=1)

    class Meta:
        fields = ["email", "app_code"]

    # def validate(self, attrs):
    #     validated_data = super().validate(attrs)

    #     app_code: str = validated_data.get("app_code")
    #     try:
    #         Bank.objects.get(alias=app_code.lower())
    #     except Bank.DoesNotExist:
    #         raise serializers.ValidationError({"app_code": f"{app_code} is not a valid bank alias"})

    #     return validated_data


class CreateOrganisationSerializer(serializers.Serializer):

    email = serializers.EmailField()
    organization_name = serializers.CharField(min_length=1)

    class Meta:
        fields = ["email", "organization_name"]


class OrganizationSerializer(serializers.ModelSerializer):
    organization_name = serializers.SerializerMethodField("get_organization_name")

    class Meta:
        model = User
        fields = [
            "id",
            "organization_name",
            "date_joined",
            "email",
            "is_active",
            "last_login",
        ]

    def get_organization_name(self, obj):
        return obj.bankadmin.organization_name


class RejectAccountOpeningSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=True)
    reason = serializers.CharField(min_length=2)

    class Meta:
        fields = ["id", "reason"]
        
        
class GetCustomerMappedToAgentSerializer(serializers.Serializer):
    
    id = serializers.SerializerMethodField("get_id")
    customer_account_name = serializers.SerializerMethodField("get_customer_account_name")
    customer_account_number = serializers.SerializerMethodField("get_customer_account_number")
    customer_email = serializers.SerializerMethodField("get_customer_email")
    bank = serializers.SerializerMethodField("get_bank")
    agent = serializers.SerializerMethodField("get_agent")
    bank_admin = serializers.SerializerMethodField("get_bank_admin")
    created = serializers.SerializerMethodField("get_created")

    class Meta:
        fields = [
            "customer_account_name", "customer_email", "bank", "id"
            "customer_account_number", "agent", "bank_admin", "created"
        ]

    def get_id(self, obj):
        return obj.id
        
    def get_customer_account_name(self, obj):
        return obj.customer_account_name
        
    def get_customer_account_number(self, obj):
        return obj.customer_account_number
    
    def get_customer_email(self, obj):
        return obj.customer_email
    
    def get_bank(self, obj):
        return obj.bank.name

    def get_agent(self, obj):
        return str(obj.agent.agent.user.get_full_name())
            
    def get_bank_admin(self, obj):
        return str(obj.bank_admin.user.get_full_name())
    
    def get_created(self, obj):
        return obj.created
    
    def to_representation(self, instance):
        """ 
        At this point based on the token received we limit the 
        data sent if Agent and send full data if Bank Admin.
        """
        
        data = super().to_representation(instance)
        
        if self.is_bank_admin_detail() == True:
            return data
        elif self.is_bank_admin_detail() == False:
            data.pop("bank_admin", None)
            data.pop("created", None)
            # data.pop("id", None)
            return data
    
    def is_bank_admin_detail(self):
        
        """ 
        Extracts the context or kwargs passed to the serializer from the view level, and 
        use to ascertain what type of data representation to pass.
        """
        
        sender = self.context.get("sender")
        if sender == "BANKADMIN":
            return True
        return False
    
    
class CollectionHistorySerializer(serializers.Serializer):
    
    user_ref = serializers.SerializerMethodField("get_user_ref")
    bank = serializers.SerializerMethodField("get_bank")
    agent = serializers.SerializerMethodField("get_agent")
    amount = serializers.SerializerMethodField("get_amount")
    transaction_type = serializers.SerializerMethodField("get_transaction_type")
    agent_account = serializers.SerializerMethodField("get_agent_account")
    customer_account_number = serializers.SerializerMethodField("get_customer_account_number")
    customer_account_name = serializers.SerializerMethodField("get_customer_account_name")
    response_description = serializers.SerializerMethodField("get_response_description")
    remark = serializers.SerializerMethodField("get_remark")
    status = serializers.SerializerMethodField("get_status")
    date_created = serializers.SerializerMethodField("get_date_created")

    class Meta:
        fields = [
            "user_ref", "bank", "agent", "amount", "transaction_type",
            "agent_account", "customer_account_number", "customer_account_name", 
            "response_description", "remark", "status", "date_created"
        ]

    def get_user_ref(self, obj):
        return obj.user_ref
        
    def get_bank(self, obj):
        return obj.bank.name
    
    def get_agent(self, obj):
        return obj.agent.user.get_full_name()
    
    def get_amount(self, obj):
        return obj.amount
            
    def get_transaction_type(self, obj):
        return obj.transaction_type.name
    
    def get_agent_account(self, obj):
        return obj.agent_account.account_number
    
    def get_customer_account_number(self, obj):
        return obj.customer_account_number
    
    def get_customer_account_name(self, obj):
        return obj.customer_account_name
    
    def get_response_description(self, obj):
        return obj.response_description
    
    def get_remark(self, obj):
        return obj.remark
    
    def get_status(self, obj):
        if obj.status == "S":
            return "Successful"
        elif obj.status == "P":
            return "Pending"
        elif obj.status == "F":
            return "Failed"
        elif obj.status == "R":
            return "Reversed"
    
    def get_date_created(self, obj):
        return obj.date_created
    
    