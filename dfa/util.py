from django.db.models.query import QuerySet
from common.models import BVN
from .models import Agent, AgentAccount, AgentOnboardedAccount
from common.functions import create_bvn_record, get_agent_username, get_reference
from common.services import send_email, send_sms_test, send_sms, validate_bvn
from common.validators import validate_phone_number
from django.contrib.auth import get_user_model
from bank.models import BankAccountType
# from logic_omnichannel import settings
from django.conf import settings
import logging
import boto3
from botocore.exceptions import ClientError
import os
import base64


def create_agent(bankadmin, validated_data, password) -> Agent:
    """
    Creates a bank agent by first creating the agent as a user then making the user an agent
    """

    agent_data = {**validated_data}
    bank = bankadmin.bank

    phone_number = agent_data.pop("phone_number")
    account_number = agent_data.pop("account_number", None)
    transaction_limit = agent_data.pop("transaction_limit", None)
    agent_data["username"] = get_agent_username(bank.alias, phone_number)
    user = get_user_model().objects.filter(
        email=agent_data["email"], username__istartswith=bank.alias
    )
    if user:
        raise Exception("User Already Exists")
    user = get_user_model().objects.create(**agent_data)
    user.set_password(password)
    user.save()

    agent = Agent.objects.create(
        user=user, phone_number=phone_number, bank=bank, created_by=bankadmin
    )

    if account_number:
        try:
            # Try to create an agent account for agent
            account_type = BankAccountType.objects.get(code="savings")
            AgentAccount.objects.create(
                agent=agent,
                account_number=account_number,
                daily_limit=transaction_limit,
                account_type=account_type,
                bank=bank,
            )
        except Exception:
            user.delete()
            raise Exception("Unable to Create Agent Account")

    return agent


def send_agent_creation_notification(agent: Agent, password: str):
    """
    Notifies Bank Agent via mail and text message of the account creation details for login
    """

    message = f"Hello {agent.user.first_name}\n \nYOUR {agent.bank.name.upper()} AGENT ACCOUNT WAS CREATED ON OMNI CHANNEL \nusername: \n \t{agent.user.username}\n \npassword:\n \t{password}\n \nYou can now login on the Agent DFA app"
    email_args = {
        "email_from": agent.bank.email,
        "to": agent.user.email,
        "subject": f"{agent.bank.name.upper()} AGENT ACCOUNT CREATION NOTIFICATION",
        "message": message,
    }

    message = f"Hello {agent.user.first_name}\nYour agent login details \nusername:{agent.user.username}\npassword:{password}"

    send_email(email_args, "INTERNAL_NOTIFICATION")

    if settings.APP_ENV == "development":
        send_sms_test(agent.phone_number, message)
        return

    if agent.bank.message_sender_name == "OmoluabiMB":
        send_sms_test(agent.phone_number, message)
    else:
        request_id = get_reference()
        send_sms(
            agent.phone_number,
            message,
            institution_code=agent.bank.code,
            message_sender_name=agent.bank.message_sender_name,
            request_id=request_id,
        )


def send_customer_initiate_account_creation_notification(
    account: AgentOnboardedAccount,
):
    """
    We send the customer a notification email that his account creation process has started
    """
    message = f"A {account.account_type.name.upper()} account creation process has been initated for you by one of our agents, {account.agent.user.last_name} {account.agent.user.first_name}. As soon as your application has been approved you'd recieve mail containing your Account details."

    email_args = {
        "email_from": account.agent.bank.email,
        "to": account.email,
        "subject": f"{account.account_type.name.upper()} ACCOUNT OPENING APPLICATION RECIEVED.",
        "account_number": account.account_number,
        "message": message,
        "customer_first_name": account.first_name,
        "img_url": f"{account.agent.bank.email_img_url}",
        "foreground": account.agent.bank.email_foreground_color,
        "background": account.agent.bank.email_background_color,
    }

    send_email(email_args, "ACCOUNT_OPENING_NOTIFICATION")


def retrieve_bvn(bvn: str) -> BVN:
    """
    tries to retrieve bvn from local database if exist else retrieve from bvn service
    else raise BVN.DoesNotExist error
    """
    try:
        bvn = BVN.objects.get(bvn=bvn)
    except BVN.DoesNotExist:
        bvn_enq = validate_bvn(bvn)
        if bvn_enq["response_code"] == settings.SUCCESS_RESPONSE:
            bvn = bvn_enq["response_description"]["bvn"]
            phone_number = bvn_enq["response_description"]["mobile"]
            first_name = bvn_enq["response_description"]["first_name"]
            last_name = bvn_enq["response_description"]["last_name"]
            dob = bvn_enq["response_description"]["dob"]
            formatted_dob = bvn_enq["response_description"]["formatted_dob"]
            gender = bvn_enq.get("response_description", {}).get("gender", "")
            residential_address = bvn_enq.get("response_description", {}).get(
                "residential_address", ""
            )
            bvn = create_bvn_record(
                bvn,
                phone_number,
                first_name,
                last_name,
                dob,
                formatted_dob,
                gender,
                residential_address,
            )
        else:
            raise BVN.DoesNotExist
    except Exception:
        raise BVN.DoesNotExist

    return bvn


def get_data(queryset: QuerySet):
    """
    returns useful data from a given queryset
    """
    total_count = queryset.count()
    total_approved = queryset.filter(is_approved=True).count()
    total_pending_approval = total_count - total_approved
    return {
        "count": total_count,
        "approved": total_approved,
        "pending": total_pending_approval,
    }

def save_base64_image_to_s3(base64_string, filename, bucket_name = None):
    # Decode the base64 string to binary image data
    binary_data = base64.b64decode(base64_string)

    # Create an instance of the S3 client
    s3 = boto3.client('s3')

    # Upload the image data to S3
    if bucket_name is None:
        bucket_name = "octozeep-bucket"

    file_key = f'account_opening_images/{filename}'

    try:

        s3.put_object(Bucket=bucket_name, Key=file_key, Body=binary_data)

        # Generate the public URL for the image
        return file_key
    except Exception:         
        return None
    
def get_base64_image_from_s3(object_key, bucket_name = None):

    # Upload the image data to S3
    if bucket_name is None:
        bucket_name = "octozeep-bucket"

    try:
        s3 = boto3.client('s3')

        # Get the object
        response = s3.get_object(Bucket=bucket_name, Key=object_key)
        #print('here')

        # Print the content of the object
        binary_data = response['Body'].read()
        #print("binary > " + str(binary_data))
        base64_data = base64.b64encode(binary_data)
        base64_string = base64_data.decode('utf-8')
        #print("dec >>" + base64_string)

        # Generate the public URL for the image
        return base64_string
    except Exception:         
        return None
    
def generate_s3_half_key(base64_type, agent, phone_number):
    if base64_type == "id":
        key_type = ':id'
    elif base64_type == "signature":
        key_type = ':signature'
    elif base64_type == "profile_picture":
        key_type = ':profile_picture'
        
    number = validate_phone_number(phone_number)
    half_key = number + ':' + str(agent)
    full_key = half_key + key_type
    
    return full_key


def generate_s3_full_key(db_key, s3_half_key, base64_string):
    if db_key == 'N/A' or db_key == None:
        # Adding `or db_key == None` to the line above is a great fix for an unexpected event I saw in production.
        # Though fix temporary because I don't know the cause of the unexpected, but the updated would work 
        key = s3_half_key
    else:
        key = db_key.split("/")[1]
        
    full_key = save_base64_image_to_s3(
        base64_string=base64_string,
        filename=key
    )
    if full_key == None:
        raise Exception("Could not upload the image. Please try again, ensuring it's a correct string")
    return full_key
