from django.db import models
from dfa.models import Agent, Agent<PERSON><PERSON>unt, AgentOnboardedAccount, Merchant, AgentMappingTable
from django.contrib import admin

# Register your models here.


class AgentAccountInline(admin.TabularInline):
    model = AgentAccount


class AgentOnboardedAccountInline(admin.TabularInline):
    model = AgentOnboardedAccount
    exclude = ["id_base_64", "signature_base_64", "profile_picture_base_64", "bvn"]


@admin.register(Agent)
class AgentAdmin(admin.ModelAdmin):
    list_display = [
        "user_name",
        "first_name",
        "last_name",
        "email",
        "bank",
        "accounts_opened",
        "merchants_onboarded",
        "status",
    ]
    list_filter = ["status", "bank"]
    search_fields = (
        "user__username",
        "user__first_name",
        "user__last_name",
        "user__email",
    )
    exclude = ["reset_password", "pin"]

    inlines = [
        AgentAccountInline,
        AgentOnboardedAccountInline,
    ]

    def user_name(self, obj):
        return obj.user.username

    def first_name(self, obj):
        return obj.user.first_name

    def last_name(sef, obj):
        return obj.user.last_name

    def email(self, obj):
        return obj.user.email

    def has_change_permission(self, request, obj=None) -> bool:
        return False

    def has_add_permission(self, request, obj=None) -> bool:
        return False

    user_name.short_description = "username"
    first_name.short_description = "first name"
    last_name.short_description = "last name"


@admin.register(AgentAccount)
class AgentAccountAdmin(admin.ModelAdmin):
    list_display = [
        "user_name",
        "first_name",
        "last_name",
        "email",
        "account_number",
        "status",
        "date_created",
    ]

    list_filter = [
        "status",
    ]
    search_fields = (
        "agent__user__username",
        "agent__user__first_name",
        "agent__user__last_name",
        "agent__user__email",
    )

    def user_name(self, obj):
        return obj.agent.user.username

    def first_name(self, obj):
        return obj.agent.user.first_name

    def last_name(sef, obj):
        return obj.agent.user.last_name

    def email(self, obj):
        return obj.agent.user.email


@admin.register(Merchant)
class MerchantAdmin(admin.ModelAdmin):
    list_display = ["first_name", "last_name", "email", "bank"]
    search_fields = (
        "first_name",
        "last_name",
        "email",
    )


@admin.register(AgentOnboardedAccount)
class AgentOnboardedAccountAdmin(admin.ModelAdmin):
    list_display = ["first_name", "last_name", "email", "agent"]
    search_fields = (
        "first_name",
        "last_name",
        "email",
    )
    

@admin.register(AgentMappingTable)
class AgentMappingTableAdmin(admin.ModelAdmin):
    list_display = ["customer_account_number", "customer_email", "customer_account_name", "bank", "agent", "created"]
    search_fields = (
        "customer_account_number",
        "customer_email",
        "customer_account_name",
        "bank", 
        "agent",
        "created"
    )