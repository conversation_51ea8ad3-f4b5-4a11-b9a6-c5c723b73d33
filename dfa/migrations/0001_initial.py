# Generated by Django 3.2.3 on 2024-04-10 22:51

import common.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bank', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Agent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone_number', models.CharField(blank=True, max_length=16, null=True, validators=[common.validators.validate_phone_number])),
                ('reset_password', models.BooleanField(default=False)),
                ('accounts_opened', models.PositiveSmallIntegerField(default=0)),
                ('merchants_onboarded', models.PositiveSmallIntegerField(default=0)),
                ('pin', models.CharField(blank=True, default='', max_length=250, null=True)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=1)),
            ],
            options={
                'db_table': 'omni_bank_agent',
            },
        ),
        migrations.CreateModel(
            name='AgentAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_number', models.CharField(db_index=True, max_length=15, unique=True)),
                ('daily_limit', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('daily_cummulative', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=1)),
                ('date_created', models.DateTimeField(auto_now_add=True, verbose_name='date created')),
            ],
            options={
                'verbose_name': 'Agent Account',
                'verbose_name_plural': 'Agent Accounts',
            },
        ),
        migrations.CreateModel(
            name='Merchant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bvn', models.CharField(db_index=True, max_length=11)),
                ('phone_number', models.CharField(max_length=16, validators=[common.validators.validate_phone_number])),
                ('dob', models.CharField(max_length=25, verbose_name='Date of birth')),
                ('account_number', models.CharField(db_index=True, max_length=15)),
                ('account_name', models.CharField(blank=True, max_length=50, null=True)),
                ('merchant_number', models.CharField(blank=True, max_length=20, null=True, unique=True)),
                ('merchant_qr_data', models.TextField(blank=True, default='')),
                ('merchant_sub_number', models.CharField(blank=True, max_length=20, null=True, unique=True)),
                ('first_name', models.CharField(max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(max_length=150, verbose_name='last name')),
                ('email', models.EmailField(max_length=254, verbose_name='email address')),
                ('address', models.CharField(blank=True, max_length=500, null=True)),
                ('long', models.CharField(blank=True, max_length=15, null=True)),
                ('lat', models.CharField(blank=True, max_length=15, null=True)),
                ('is_approved', models.BooleanField(default=True, editable=False)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dfa.agent')),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bank')),
            ],
        ),
        migrations.CreateModel(
            name='AgentOnboardedAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bvn', models.CharField(db_index=True, max_length=11)),
                ('first_name', models.CharField(max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(max_length=150, verbose_name='last name')),
                ('middle_name', models.CharField(blank=True, max_length=150, verbose_name='middle name')),
                ('account_number', models.CharField(blank=True, db_index=True, max_length=15)),
                ('customer_number', models.CharField(blank=True, max_length=30)),
                ('email', models.EmailField(max_length=254, verbose_name='email address')),
                ('phone_number', models.CharField(max_length=16, validators=[common.validators.validate_phone_number])),
                ('address', models.TextField()),
                ('city', models.CharField(max_length=30)),
                ('state', models.CharField(max_length=30)),
                ('gender', models.CharField(blank=True, choices=[('FEMALE', 'Female'), ('MALE', 'Male')], max_length=6)),
                ('dob', models.CharField(max_length=25)),
                ('kyc_verification_status', models.CharField(choices=[('incomplete', 'Incomplete'), ('pending', 'Pending'), ('complete', 'Complete')], default='incomplete', max_length=10)),
                ('reason', models.CharField(default='KYC DOCUMENTS PENDING VERIFICATION', max_length=400)),
                ('means_of_id', models.CharField(choices=[('international_passport', 'International Passport'), ('drivers_license', 'Drivers License'), ('national_id', 'National ID'), ('voters_card', 'Voters Card')], max_length=25)),
                ('id_base_64', models.TextField(blank=True, null=True)),
                ('id_issue_date', models.DateField(blank=True, null=True)),
                ('id_expiry_date', models.DateField(blank=True, null=True)),
                ('id_number', models.CharField(max_length=50)),
                ('signature_base_64', models.TextField(blank=True, null=True)),
                ('profile_picture_base_64', models.TextField(blank=True, null=True)),
                ('is_approved', models.BooleanField(default=False)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True)),
                ('account_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bankaccounttype', to_field='code')),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dfa.agent')),
            ],
            options={
                'verbose_name': 'Agent Onboarded Account',
                'verbose_name_plural': 'Agent Onboarded Accounts',
            },
        ),
        migrations.CreateModel(
            name='AgentMappingTable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_account_number', models.CharField(max_length=15)),
                ('customer_email', models.EmailField(blank=True, max_length=254)),
                ('customer_account_name', models.CharField(max_length=200)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dfa.agentaccount')),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bank')),
            ],
            options={
                'verbose_name': 'Agent Mapping',
                'verbose_name_plural': 'Agent Mappings',
            },
        ),
    ]
