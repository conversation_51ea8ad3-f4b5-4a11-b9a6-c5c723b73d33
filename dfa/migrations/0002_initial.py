# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('user_profile', '0001_initial'),
        ('dfa', '0001_initial'),
        ('bank', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='agentmappingtable',
            name='bank_admin',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_profile.bankadmin'),
        ),
        migrations.AddField(
            model_name='agentaccount',
            name='account_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bank.bankaccounttype', to_field='code'),
        ),
        migrations.AddField(
            model_name='agentaccount',
            name='agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dfa.agent'),
        ),
        migrations.AddField(
            model_name='agentaccount',
            name='bank',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='bank.bank'),
        ),
        migrations.AddField(
            model_name='agent',
            name='bank',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='bank.bank'),
        ),
        migrations.AddField(
            model_name='agent',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='user_profile.bankadmin'),
        ),
        migrations.AddField(
            model_name='agent',
            name='user',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddConstraint(
            model_name='agentmappingtable',
            constraint=models.UniqueConstraint(fields=('customer_account_number', 'agent'), name='unique_customer_account_agent'),
        ),
    ]
