from bank.models import Bank
from django.db import models

from django.contrib.auth import get_user_model

User = get_user_model()
# Create your models here.


class WalletType(models.Model):
    name = models.CharField(max_length=50)
    short_name = models.CharField(max_length=7, unique=True)
    description = models.CharField(max_length=100)
    wallet_id = models.CharField(max_length=10, unique=True)
    product_type = models.CharField(max_length=15)
    fineract_product_id = models.CharField(max_length=7)
    date_created = models.DateTimeField(auto_now_add=True)
    daily_limit = models.DecimalField(max_digits=10, decimal_places=2, default="0.00")

    def __str__(self) -> str:
        return self.name


class SavingsType(models.Model):

    name = models.CharField(max_length=25, unique=True)
    description = models.CharField(max_length=100)
    code = models.Char<PERSON>ield(max_length=5, unique=True, db_index=True)
    date_created = models.DateTimeField(auto_now_add=True)


class PaymentOptions(models.Model):

    BANK = "bank"
    CARD = "card"

    OPTION_CHOICES = (
        (BANK, "Bank"),
        (CARD, "Card"),
    )

    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    option = models.CharField(max_length=4, choices=OPTION_CHOICES)
    account_number = models.CharField(max_length=10, blank=True)
    bank = models.ForeignKey(Bank, blank=True, on_delete=models.CASCADE)
    card_pan = models.CharField(max_length=30, blank=True)


class SavingsConfiguration(models.Model):

    DAILY = "D"
    WEEKLY = "W"
    MONTHLY = "M"

    FREQUENCY_CHOICES = ((DAILY, "Daily"), (WEEKLY, "Weekly"), (MONTHLY, "Monthly"))

    customer = models.ForeignKey(User, on_delete=models.CASCADE)
    savings_type = models.ForeignKey(SavingsType, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    frequency = models.CharField(max_length=1, choices=FREQUENCY_CHOICES, blank=True)
    title = models.CharField(max_length=20, blank=True)
    duration = models.PositiveSmallIntegerField(blank=True)
    duration_frequency = models.CharField(
        max_length=1, choices=FREQUENCY_CHOICES, blank=True
    )
    start_date = models.DateField(blank=True)
    withdrawal_date = models.DateField(blank=True)
    payment_option = models.ForeignKey(PaymentOptions, on_delete=models.CASCADE)
    wallet_type = models.ForeignKey(WalletType, on_delete=models.CASCADE)
    wallet_no = models.CharField(max_length=10)
    nuban = models.CharField(max_length=10)
