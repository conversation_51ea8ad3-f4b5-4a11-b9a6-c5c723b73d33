from rest_framework import serializers

from django.utils.translation import gettext_lazy as _

from .models import PaymentOptions, SavingsConfiguration, SavingsType


class SavingsTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = SavingsType
        fields = "__all__"


class PaymentOptionSerializer(serializers.ModelSerializer):

    id = serializers.IntegerField(read_only=True)

    class Meta:
        model = PaymentOptions
        fields = ["id", "option", "account_number", "bank", "card_pan"]

    def validate(self, attrs):
        validated_data = super().validate(attrs)
        option = validated_data.get("option")
        if option == "bank":
            # for bank payment option a bank and an account number must exist
            if not validated_data.get("bank"):
                raise serializers.ValidationError(
                    {"bank": _(f"A Valid Bank Must Exist for Bank Payment Option")}
                )
            if not validated_data.get("account_number"):
                raise serializers.ValidationError(
                    {
                        "account_number": _(
                            f"A Valid Account Number Must Exist for Bank Payment Option"
                        )
                    }
                )
            validated_data.pop("card_pan", None)
        elif option == "card":
            if not validated_data.get("card_pan"):
                raise serializers.ValidationError(
                    {
                        "card_pan": _(
                            f"A Valid Card Pan Must Exist for Card Payment Option"
                        )
                    }
                )
            validated_data.pop("bank", None)
            validated_data.pop("account_number", None)
        return validated_data


class SavingsConfigurationSerializer(serializers.ModelSerializer):
    class Meta:
        model = SavingsConfiguration
        fields = [
            "savings_type",
            "amount",
            "frequency",
            "title",
            "duration",
            "duration_frequency",
            "start_date",
            "withdrawal_date",
            "payment_option",
        ]

    def validate(self, attrs):
        validated_data = super().validate(attrs)

        if validated_data.get("savings_type") == SavingsType.objects.get(code="auto"):
            # this configuration is for Auto Savings
            if not validated_data.get("frequency"):
                raise serializers.ValidationError(
                    {"frequency": _(f"Frequency must exist for Auto Savings")}
                )
            validated_data.pop("title", None)
            validated_data.pop("duration", None)
            validated_data.pop("duration_frequency", None)
            validated_data.pop("start_date", None)
            validated_data.pop("withdrawal_date", None)
        elif validated_data.get("savings_type") == SavingsType.objects.get(code="safe"):
            # this configuration is for Safe Lock Savings
            if not validated_data.get("title"):
                raise serializers.ValidationError(
                    {"title": _(f"Title must exist for Safe Lock Savings")}
                )
            if not validated_data.get("duration"):
                raise serializers.ValidationError(
                    {"duration": _(f"Duration must exist for Safe Lock Savings")}
                )
            if not validated_data.get("duration_frequency"):
                raise serializers.ValidationError(
                    {
                        "duration_frequency": _(
                            f"Duration Frequency must exist for Safe Lock Savings"
                        )
                    }
                )
            if not validated_data.get("withdrawal_date"):
                raise serializers.ValidationError(
                    {
                        "withdrawal_date": _(
                            f"Withdrawal Date must exist for Safe Lock Savings"
                        )
                    }
                )
            validated_data.pop("frequency", None)
            validated_data.pop("start_date", None)
        elif validated_data.get("savings_type") == SavingsType.objects.get(
            code="targt"
        ):
            # this configuration is for Target Savings
            if not validated_data.get("title"):
                raise serializers.ValidationError(
                    {"title": _(f"Title must exist for Safe Lock Savings")}
                )
            if not validated_data.get("duration"):
                raise serializers.ValidationError(
                    {"duration": _(f"Duration must exist for Safe Lock Savings")}
                )
            if not validated_data.get("duration_frequency"):
                raise serializers.ValidationError(
                    {
                        "duration_frequency": _(
                            f"Duration Frequency must exist for Safe Lock Savings"
                        )
                    }
                )
            if not validated_data.get("start_date"):
                raise serializers.ValidationError(
                    {"start_date": _(f"Start Date must exist for Safe Lock Savings")}
                )
            if not validated_data.get("withdrawal_date"):
                raise serializers.ValidationError(
                    {
                        "withdrawal_date": _(
                            f"Withdrawal Date must exist for Safe Lock Savings"
                        )
                    }
                )
            validated_data.pop("frequency", None)

        return validated_data
