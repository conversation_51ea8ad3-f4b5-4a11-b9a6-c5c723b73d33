from verdant.models import PaymentOptions, SavingsConfiguration, SavingsType, WalletType
from verdant.serializers import (
    PaymentOptionSerializer,
    SavingsConfigurationSerializer,
    SavingsTypeSerializer,
)
from oauth2_provider.contrib.rest_framework import permissions
from user_profile.serializers import VerifyUserPhoneNumberSerializer
from rest_framework.views import APIView
from common.services import (
    create_customer_wallet,
    is_payment_option_valid,
    send_sms,
    send_sms_test,
)
import pyotp
from common.functions import (
    get_reference,
    get_serializer_key_error,
    otp_rand,
    set_response_data,
)
from bank.models import Bank
from rest_framework.response import Response
from rest_framework import status, generics, viewsets

from common.permissions import DFAAccessPermission, VerdantAccessPermission
from rest_framework.permissions import AllowAny
from rest_framework.exceptions import ValidationError
# from logic_omnichannel import settings
from django.conf import settings

# Create your views here.


class GetSavingsTypes(generics.ListAPIView):

    permission_classes = [VerdantAccessPermission]
    serializer_class = SavingsTypeSerializer

    def get_queryset(self):
        return SavingsType.objects.all().order_by("name")

    def list(self, request):
        try:
            """
            returns the list of available savings types
            """
            queryset = self.get_queryset()
            serializer = SavingsTypeSerializer(queryset, many=True)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Get Savings Types List Successful",
                detail={"savings_types": serializer.data},
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Retrieve Savings Types List",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SendPhoneNumberOTP(APIView):

    permission_classes = [VerdantAccessPermission]

    def get(self, request, phone_number):
        try:
            """
            send sms otp to onboarding customers on verdant digital
            """
            bank = Bank.objects.get(alias="chmfb")
            message_sender_name = bank.message_sender_name

            # Generate counter based OTP
            hotp = pyotp.HOTP("base32secret3232")
            validate_otp_at = otp_rand()
            verify_phone_number_otp = hotp.at(validate_otp_at)

            sms_text = f"Welcome to Verdant Digital Bank. \nThis is your phone number verfication OTP: \n{verify_phone_number_otp}"

            if message_sender_name != "OmoluabiMB":
                # run our test sms service because this is a test case
                # resp = send_sms_test(phone_number, sms_text)

                resp = send_sms(
                    phone_number,
                    sms_text,
                    institution_code=bank.code,
                    message_sender_name=message_sender_name,
                    request_id=get_reference(),
                )

                if resp["response_code"] == settings.SUCCESS_RESPONSE:
                    data = {
                        "response_code": settings.SUCCESS_RESPONSE,
                        "response_description": "OTP SMS Sent Successfully",
                        "response_detail": {
                            "validate_otp_at": validate_otp_at,
                            "otp": verify_phone_number_otp,
                        },
                    }
                    return Response(data, status=status.HTTP_200_OK)

            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Sending OTP SMS",
                detail="Error Verifying User Phone Number",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Verifying User Phone Number",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class VerifyPhoneNumberOTP(generics.GenericAPIView):

    permission_classes = [AllowAny]
    serializer_class = VerifyUserPhoneNumberSerializer

    def post(self, request):
        try:
            """
            Retrieve the OTP parameters from the payload and verify the phone number
            """
            serializer = VerifyUserPhoneNumberSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            validate_otp_at = serializer.validated_data["validate_otp_at"]
            sms_otp = serializer.validated_data["sms_otp"]

            hotp = pyotp.HOTP("base32secret3232")
            if not hotp.verify(sms_otp, int(validate_otp_at)):
                data = {
                    "response_code": settings.FAILED_RESPONSE,
                    "response_detail": "Invalid OTP",
                    "response_description": "Invalid OTP, Confirm the OTP",
                }
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            data = {
                "response_code": settings.SUCCESS_RESPONSE,
                "response_description": "User Phone Number Verified",
                "response_detail": f"User phone number has successfully verified",
            }
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class PaymentOption(viewsets.GenericViewSet):
    permission_classes = [VerdantAccessPermission]
    serializer_class = PaymentOptionSerializer

    def get_queryset(self, customer):
        return PaymentOptions.objects.filter(customer=customer).order_by("option")

    def create(self, request):
        try:
            """
            Adds Payment options to be used for customer savings contribution
            """
            serializer = PaymentOptionSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            if not is_payment_option_valid(serializer.validated_data):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Payment Option Not Valid to Customer",
                    detail="Payment Option Not Valid to Customer",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            serializer.save(customer=request.auth.user)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Payment Option Successfully Added",
                detail="Payment Option Successfully Added",
            )
            return Response(data, status=status.HTTP_200_OK)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def list(self, request):
        try:
            """
            Return the list of payment options for a particular customer
            """
            queryset = self.get_queryset(request.auth.user)
            serializer = PaymentOptionSerializer(queryset, many=True)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Customer Payment Option Retrieved",
                detail={"payment_options": serializer.data},
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Retrieve CUstomer Payment Options",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, id):
        try:
            """
            deletes payment option set by customer
            """
            payment_option = PaymentOptions.objects.get(
                id=id, customer=request.auth.user
            )
            saving_configs = SavingsConfiguration.objects.filter(
                payment_option=payment_option
            )
            if saving_configs:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Bound To Savings Configuration",
                    detail="Unable To Delete Payment Option as Option is Tied to Other Savings, Update Savings Configuration before you can be allowed to Delete this Option",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            payment_option.delete()
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Payment Option Deleted Successfully",
                detail="Payment Option Deleted Successfully",
            )
            return Response(data, status=status.HTTP_200_OK)
        except PaymentOptions.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Payment Option Does Not Exist",
                detail="Payment Option Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class Configuration(viewsets.GenericViewSet):

    permission_classes = [VerdantAccessPermission]
    serializer_class = SavingsConfigurationSerializer

    def create(self, request):
        try:
            """
            validate the request payload and for the type of savings and then save then persist the
            configuration with the customer
            """
            serializer = SavingsConfigurationSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            savings_type = serializer.validated_data["savings_type"]
            wallet_type = WalletType.objects.get(short_name=savings_type)
            customer = request.auth.user

            resp = create_customer_wallet(
                customer.customerprofile.mifos_customer_id, wallet_type.wallet_id
            )
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                wallet_no = resp.get("response_detail", {}).get("account_number")
                nuban = resp.get("response_detail", {}).get("nuban")
                serializer.save(
                    customer=customer,
                    wallet_type=wallet_type,
                    wallet_no=wallet_no,
                    nuban=nuban,
                )
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Savings Configuration Successfully added",
                    detail="Savings Configuration Successfully added",
                )
                return Response(data, status=status.HTTP_200_OK)
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Create Wallet for Savings Configuration",
                detail="Unable to Create Wallet for Savings Configuration",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except WalletType.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Wallet Type Does Not Exist for this Savings Configuration",
                detail="Wallet Type Does Not Exist for this Savings Configuration",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable To Create Savings Configuration: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, id):
        try:
            """
            retrieve the list of savings configuration based on the the savings
            type Id
            """
            savings_type = SavingsType.objects.get(id=id)
            queryset = SavingsConfiguration.objects.filter(
                customer=request.auth.user, savings_type=savings_type
            )
            serializer = SavingsConfigurationSerializer(queryset, many=True)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Savings Configurations Retrieved",
                detail={"configuration": serializer.data},
            )
            return Response(data, status=status.HTTP_200_OK)
        except SavingsType.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Savings Type Does Not Exist",
                detail="Savings Type Does Not Exist",
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable to Retrieve Savings Configurations",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
