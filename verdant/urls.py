from django.urls import path
from .views import (
    Configuration,
    GetSavingsTypes,
    SendPhoneNumberOTP,
    VerifyPhoneNumberOTP,
    PaymentOption,
)

urlpatterns = [
    path(
        "send_user_sms_otp/<str:phone_number>/",
        SendPhoneNumberOTP.as_view(),
        name="verdant_send_user_otp",
    ),
    path(
        "verify_phone_number_otp/",
        VerifyPhoneNumberOTP.as_view(),
        name="verdant_verify_phone_number_otp",
    ),
    path("get_savings_types/", GetSavingsTypes.as_view(), name="get_savings_types"),
    path(
        "payment_option/",
        PaymentOption.as_view({"post": "create"}),
        name="add_payment_option",
    ),
    path(
        "payment_options/",
        PaymentOption.as_view({"get": "list"}),
        name="get_payment_options",
    ),
    path(
        "payment_option/<int:id>/",
        PaymentOption.as_view({"delete": "destroy"}),
        name="delete_payment_option",
    ),
    path(
        "create_savings_configuration/",
        Configuration.as_view({"post": "create"}),
        name="create_savings_configuration",
    ),
    path(
        "retrieve_savings_configurations/<int:savings_type_id>/",
        Configuration.as_view({"get": "retrieve"}),
        name="retrieve_savings_configurations",
    ),
]
