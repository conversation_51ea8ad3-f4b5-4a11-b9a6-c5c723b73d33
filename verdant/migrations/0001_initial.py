# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('bank', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentOptions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('option', models.CharField(choices=[('bank', 'Bank'), ('card', 'Card')], max_length=4)),
                ('account_number', models.CharField(blank=True, max_length=10)),
                ('card_pan', models.CharField(blank=True, max_length=30)),
                ('bank', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.CASCADE, to='bank.bank')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='SavingsType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=25, unique=True)),
                ('description', models.CharField(max_length=100)),
                ('code', models.CharField(db_index=True, max_length=5, unique=True)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='WalletType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('short_name', models.CharField(max_length=7, unique=True)),
                ('description', models.CharField(max_length=100)),
                ('wallet_id', models.CharField(max_length=10, unique=True)),
                ('product_type', models.CharField(max_length=15)),
                ('fineract_product_id', models.CharField(max_length=7)),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('daily_limit', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
            ],
        ),
        migrations.CreateModel(
            name='SavingsConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('frequency', models.CharField(blank=True, choices=[('D', 'Daily'), ('W', 'Weekly'), ('M', 'Monthly')], max_length=1)),
                ('title', models.CharField(blank=True, max_length=20)),
                ('duration', models.PositiveSmallIntegerField(blank=True)),
                ('duration_frequency', models.CharField(blank=True, choices=[('D', 'Daily'), ('W', 'Weekly'), ('M', 'Monthly')], max_length=1)),
                ('start_date', models.DateField(blank=True)),
                ('withdrawal_date', models.DateField(blank=True)),
                ('wallet_no', models.CharField(max_length=10)),
                ('nuban', models.CharField(max_length=10)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('payment_option', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='verdant.paymentoptions')),
                ('savings_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='verdant.savingstype')),
                ('wallet_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='verdant.wallettype')),
            ],
        ),
    ]
