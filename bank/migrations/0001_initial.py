# Generated by Django 3.2.3 on 2024-04-10 22:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Bank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('code', models.CharField(blank=True, max_length=20, null=True, unique=True)),
                ('nip_code', models.CharField(blank=True, max_length=20, null=True, unique=True)),
                ('institution_number', models.Char<PERSON>ield(blank=True, max_length=20, null=True, unique=True)),
                ('alias', models.CharField(blank=True, db_index=True, max_length=10, null=True, unique=True)),
                ('ussd_code', models.Char<PERSON>ield(blank=True, db_index=True, max_length=10, null=True, unique=True)),
                ('utility_account', models.<PERSON>r<PERSON><PERSON>(blank=True, db_index=True, max_length=15, null=True, unique=True)),
                ('address', models.<PERSON>r<PERSON>ield(blank=True, max_length=250, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=200, null=True)),
                ('website', models.CharField(blank=True, max_length=250, null=True)),
                ('logo_img', models.ImageField(blank=True, null=True, upload_to='bank_logo')),
                ('belong_to_nambuit_switch', models.BooleanField(default=False)),
                ('belong_to_nibss_switch', models.BooleanField(default=False)),
                ('belong_to_mifos_wallet_service', models.BooleanField(default=False)),
                ('limit_for_soft_token', models.DecimalField(decimal_places=2, default='20000.00', max_digits=10)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
                ('is_partner_bank', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('message_sender_name', models.CharField(blank=True, max_length=20, null=True)),
                ('email_img_url', models.URLField(blank=True, null=True)),
                ('email_foreground_color', models.CharField(blank=True, max_length=10, null=True)),
                ('email_background_color', models.CharField(blank=True, max_length=10, null=True)),
                ('savings_account_limit', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('current_account_limit', models.DecimalField(decimal_places=2, default='0.00', max_digits=10)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
            ],
            options={
                'verbose_name': 'Bank',
                'verbose_name_plural': 'Banks',
                'db_table': 'omni_bank',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BankAccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=15, unique=True)),
                ('status', models.CharField(choices=[('A', 'Active'), ('I', 'Inactive')], default='A', max_length=2)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
            ],
            options={
                'verbose_name': 'Bank Account Type',
                'verbose_name_plural': 'Bank Account Type',
                'db_table': 'omni_bank_account_type',
            },
        ),
        migrations.CreateModel(
            name='Beneficiary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_name', models.CharField(max_length=200)),
                ('account_number', models.CharField(max_length=15)),
                ('status', models.BooleanField(default=True)),
                ('transactions_count', models.PositiveIntegerField(default=0)),
                ('date_created', models.DateTimeField(auto_now_add=True, null=True, verbose_name='date published')),
                ('bank', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='bank.bank')),
            ],
            options={
                'verbose_name': 'Beneficiary',
                'verbose_name_plural': 'Beneficiaries',
                'db_table': 'omni_beneficiary',
            },
        ),
    ]
