from decimal import Decimal
from common.functions import get_channel_object
from django.contrib.auth import get_user_model
from customer.models import CustomerAccount, CustomerProfile
from rest_framework import serializers

from bank.models import Bank, BankAccountType, Beneficiary


class BankSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bank
        fields = '__all__'


class BankLightSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bank
        fields = ('id', 'name', 'code', 'nip_code', 'belong_to_nambuit_switch', 'belong_to_nibss_switch', 'ussd_code', 'status')


class BankAccountTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = BankAccountType
        fields = '__all__'


class BeneficiarySerializer(serializers.ModelSerializer):
    bank = BankLightSerializer()

    class Meta:
        model = Beneficiary
        fields = '__all__'


class GetAllBankCustomerAccountSerializer(serializers.ModelSerializer):

    email = serializers.SerializerMethodField("get_email")
    first_name = serializers.SerializerMethodField("get_first_name")
    last_name = serializers.SerializerMethodField("get_last_name")
    account_type = serializers.SerializerMethodField("get_account_type")
    phone_number = serializers.SerializerMethodField("get_phone_number")
    access_channels = serializers.SerializerMethodField("get_access_channels")

    class Meta:
        model = CustomerAccount
        fields = [
            "id",
            "email",
            "first_name",
            "last_name",
            "account_number",
            "account_type",
            "phone_number",
            "status",
            "daily_limit",
            "date_created",
            "access_channels",
        ]

    def get_email(self, obj):
        return obj.profile.customer.email

    def get_first_name(self, obj):
        return obj.profile.customer.first_name

    def get_last_name(self, obj):
        return obj.profile.customer.last_name

    def get_account_type(self, obj):
        return obj.account_type.code

    def get_phone_number(self, obj):
        return obj.profile.phone_number

    def get_access_channels(self, obj):
        channel_list = []

        channel_list.append(
            get_channel_object("MOBILE_CHANNEL_ACCESS", obj.mobile_channel_access)
        )
        channel_list.append(
            get_channel_object("WEB_CHANNEL_ACCESS", obj.web_channel_access)
        )
        channel_list.append(
            get_channel_object("USSD_CHANNEL_ACCESS", obj.ussd_channel_access)
        )

        return channel_list


class GetAllBankCustomerProfileSerializer(serializers.ModelSerializer):

    email = serializers.SerializerMethodField('get_email')
    first_name = serializers.SerializerMethodField('get_first_name')
    last_name = serializers.SerializerMethodField('get_last_name')
    date_joined = serializers.SerializerMethodField('get_date_joined')
    access_channels = serializers.SerializerMethodField('get_access_channels')
    customer_account = serializers.SerializerMethodField('get_customer_account')

    class Meta:
        model = CustomerProfile
        fields = ['id', 'first_name', 'last_name', 'email', 'bvn', 'phone_number', 'dob', 'is_phone_number_verified', 
        'is_email_verified', 'no_of_customer_account', 'status', 'access_channels', 'date_joined', 'customer_account']

    def get_email(self, obj):
        return obj.customer.email
    
    def get_first_name(self, obj):
        return obj.customer.first_name
    
    def get_last_name(self, obj):
        return obj.customer.last_name
    
    def get_date_joined(self, obj):
        return obj.customer.date_joined

    def get_access_channels(self, obj):

        try:
            channel_list = []
            channel_list.append(get_channel_object('MOBILE_CHANNEL_ACCESS', obj.mobile_channel_access))
            channel_list.append(get_channel_object('WEB_CHANNEL_ACCESS', obj.web_channel_access))
            channel_list.append(get_channel_object('USSD_CHANNEL_ACCESS', obj.ussd_channel_access))

            return channel_list
        except Exception:
            return []
    
    def get_customer_account(self, obj):
        customer_account = []
        if obj.no_of_customer_account > 0:
            accounts = CustomerAccount.objects.filter(profile=obj)
            for account in accounts:
                serializer = GetAllBankCustomerAccountSerializer(instance=account)
                customer_account.append(serializer.data)
        return customer_account


class ActivateDeactivateBankCustomerAccountSerializer(serializers.Serializer):
    customer_account_id = serializers.IntegerField(required=True)
    is_active = serializers.BooleanField(default=True)

    class Meta:
        fields = ['customer_account_id', 'is_active']


class ActivateDeactivateBankCustomerProfileSerializer(serializers.Serializer):
    customer_profile_id = serializers.IntegerField(required=True)
    is_active = serializers.BooleanField(default=True)

    class Meta:
        fields = ['customer_profile_id', 'is_active']


class UpdateCustomerAccountSerializer(serializers.Serializer):
    mobile_channel_access = serializers.NullBooleanField(default=None, required=False)
    web_channel_access = serializers.NullBooleanField(default=None, required=False)
    ussd_channel_access = serializers.NullBooleanField(default=None, required=False)
    daily_limit = serializers.DecimalField(
        max_digits=10, decimal_places=2, required=False
    )

    class Meta:
        fields = [
            "mobile_channel_access",
            "web_channel_access",
            "ussd_channel_access",
            "daily_limit",
        ]

    def update(self, instance, validated_data):
        mobile_channel_access = validated_data.get("mobile_channel_access")
        web_channel_access = validated_data.get("web_channel_access")
        ussd_channel_access = validated_data.get("ussd_channel_access")
        daily_limit = validated_data.get("daily_limit", Decimal("0.00"))

        if mobile_channel_access is not None:
            instance.mobile_channel_access = mobile_channel_access
        if web_channel_access is not None:
            instance.web_channel_access = web_channel_access
        if ussd_channel_access is not None:
            instance.ussd_channel_access = ussd_channel_access
        if daily_limit:
            instance.daily_limit = daily_limit
        instance.save()

        return instance
        

class ActivateDeactivatePartnerBankSerializer(serializers.Serializer):

    app_code = serializers.CharField(max_length=15)
    is_active = serializers.BooleanField()

    class Meta:
        fields = ['app_code', 'is_active']


class SendResetPasswordEmailSerializer(serializers.Serializer):

    email = serializers.EmailField(required=True)

    class Meta:
        fields = ["email"]


class ResetPasswordSerializer(serializers.Serializer):

    password = serializers.CharField(min_length=5)
    old_password = serializers.CharField(min_length=5, required=False)

    class Meta:
        fields = ["password", "old_password"]
