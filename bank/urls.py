from django.urls import path, include
from rest_framework import routers

from . import views
from .views import (
    AccountStatement,
    ActivateDeactivatePartnerBank,
    ActivateDeactiveBankCustomerProfile,
    CustomerAccountBalance,
    CustomerAccountTransactions,
    GetAllBankCustomerAccount,
    GetAllBankCustomerProfile,
    GetBank,
    GetBankAccountType,
    CreateUpdateBeneficiary,
    GetBeneficiary,
    GetBankWithNIBSS,
    GetCustomerTransactions,
    GetPartnerBanks,
    ActivateDeactiveBankCustomerAccount,
    ResetPassword,
    SendResetPasswordEmail,
    SummaryData,
    UpdateCustomerAccount,
    UpdateCustomerProfile,
)

router = routers.DefaultRouter()
router.register(r"banks", views.BankViewSet)

urlpatterns = (
    path("", include(router.urls)),
    path("get_bank/", GetBank.as_view(), name="get_bank"),
    path(
        "get_bank_central_switch/",
        GetBankWithNIBSS.as_view(),
        name="get_bank_central_switch",
    ),
    path(
        "get_bank_account_type/",
        GetBankAccountType.as_view(),
        name="get_bank_account_type",
    ),
    path(
        "create_update_beneficiary/",
        CreateUpdateBeneficiary.as_view(),
        name="create_update_beneficiary",
    ),
    path(
        "get_beneficiary/<int:user_id>/",
        GetBeneficiary.as_view(),
        name="get_beneficiary",
    ),
    path("get_partner_banks/", GetPartnerBanks.as_view(), name="get_partner_banks"),
    path(
        "get_all_customer_account/",
        GetAllBankCustomerAccount.as_view({"get": "list"}),
        name="get_all_customer_account",
    ),
    path(
        "get_customer_account/<str:account_number>",
        GetAllBankCustomerAccount.as_view({"get": "retrieve"}),
        name="get_customer_account",
    ),
    path(
        "get_all_customer_profile/",
        GetAllBankCustomerProfile.as_view({"get": "list"}),
        name="get_all_customer_profile",
    ),
    path(
        "get_customer_profile/<int:id>",
        GetAllBankCustomerProfile.as_view({"get": "retrieve"}),
        name="get_customer_profile",
    ),
    path(
        "activate_deactivate_customer_account/",
        ActivateDeactiveBankCustomerAccount.as_view(),
        name="activate_deactivate_customer_account",
    ),
    path(
        "activate_deactivate_customer_profile/",
        ActivateDeactiveBankCustomerProfile.as_view(),
        name="activate_deactivate_customer_profile",
    ),
    path(
        "activate_deactivate_partner_bank/",
        ActivateDeactivatePartnerBank.as_view(),
        name="activate_deactivate_partner_bank",
    ),
    path(
        "update_customer_account/<int:pk>/",
        UpdateCustomerAccount.as_view(),
        name="update_customer_account",
    ),
    path(
        "update_customer_profile/<int:pk>/",
        UpdateCustomerProfile.as_view(),
        name="update_customer_profile",
    ),
    path(
        "get_transactions/",
        GetCustomerTransactions.as_view(),
        name="get_customer_transactions",
    ),
    path("summary_data/", SummaryData.as_view(), name="summary_data"),
    path(
        "send_reset_password_email/",
        SendResetPasswordEmail.as_view(),
        name="send_reset_password_email",
    ),
    path("reset_password/", ResetPassword.as_view(), name="reset_password"),
    path(
        "get_customer_account_balance/<int:customer_account_id>/",
        CustomerAccountBalance.as_view(),
        name="get_customer_account_balance",
    ),
    path(
        "get_customer_account_transactions/<int:customer_account_id>/",
        CustomerAccountTransactions.as_view(),
        name="get_customer_account_transactions",
    ),
    path(
        "account_statement/<int:customer_account_id>/",
        AccountStatement.as_view(),
        name="bank_account_statement",
    ),
)
