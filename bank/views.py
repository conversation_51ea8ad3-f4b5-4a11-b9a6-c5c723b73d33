import logging

import requests
from common.models import TransactionType
from datetime import date, timedelta, datetime

from rest_framework.permissions import AllowAny
from customer.functions import process_account_statement
from customer.utils import retrieve_update_core_banking_transactions

from transactions.models import (
    AirtimeData,
    BillPayment,
    CoreBankingTransactions,
    SendMoney,
    Transaction,
)
from customer.serializers import (
    GetAccountTransactionSerializer,
    GetTransactionsSerializer,
)
from common.permissions import BankPortalAccessPermission
from common.functions import get_serializer_key_error
from common.services import get_balance_enquiry, get_wallet_balance, send_email
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.contrib.auth import get_user_model
from django.contrib.auth.models import update_last_login
from django.db.models import Q


# Create your views here
from oauth2_provider.contrib.rest_framework import permissions, TokenHasScope
from rest_framework import generics, viewsets, status
from rest_framework import pagination
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.filters import <PERSON>Filter
from rest_framework.permissions import AllowAny
from rest_framework.reverse import reverse

from bank.models import Bank, BankAccountType, Beneficiary
from bank.serializers import (
    ActivateDeactivateBankCustomerAccountSerializer,
    ActivateDeactivateBankCustomerProfileSerializer,
    ActivateDeactivatePartnerBankSerializer,
    BankSerializer,
    BankAccountTypeSerializer,
    BeneficiarySerializer,
    BankLightSerializer,
    GetAllBankCustomerAccountSerializer,
    GetAllBankCustomerProfileSerializer,
    ResetPasswordSerializer,
    SendResetPasswordEmailSerializer,
    UpdateCustomerAccountSerializer,
)
# from logic_omnichannel import settings
from django.conf import settings
from customer.models import CustomerAccount, CustomerProfile
from common.functions import (
    CustomPageNumberPagination,
    get_client_credentials,
    get_filtered_queryset_by_date,
    get_platform,
    get_transactions_data,
    set_response_data,
    get_user_token,
)
from user_profile.views import set_app_code
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from drf_yasg import openapi


class BankViewSet(viewsets.ModelViewSet):
    permission_classes = [permissions.TokenHasReadWriteScope]
    """
    API endpoint that allows users profile to be viewed or edited.
    """
    queryset = Bank.objects.all()
    serializer_class = BankSerializer


class GetPartnerBanks(generics.GenericAPIView):

    # permission_classes = [permissions.TokenHasReadWriteScope]
    permission_classes = (AllowAny,)

    def get(self, request):
        try:
            """
            Try to get all partner banks with the Omni channel that current have their status
            as active
            """
            all_banks = Bank.objects.filter(is_partner_bank=True).filter(status="A")
            bank_list = []
            if all_banks:
                for bank in all_banks:
                    _bank = {
                        "id": bank.id,
                        "code": bank.code,
                        "name": bank.name,
                        "alias": bank.alias,
                        "belong_to_nambuit_switch": bank.belong_to_nambuit_switch,
                        "belong_to_nibss_switch": bank.belong_to_nibss_switch,
                        "status": bank.status,
                        "logo_img": bank.logo_img.url,
                        # "logo_base_64": bank.get_logo_base64(),
                    }
                    bank_list.append(_bank)
                detail = {"bank_list": bank_list}
                data = set_response_data(
                    settings.SUCCESS_RESPONSE, "PARTNERED Bank List", detail
                )
                return Response(data, status=status.HTTP_200_OK)
            detail = {"bank_list": bank_list}
            data = set_response_data(
                settings.SUCCESS_RESPONSE, "Empty PARTNER BANK LIST", detail
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                "Error occurred while getting list of PARTNER BANKS",
                str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetAllBankCustomerAccount(viewsets.GenericViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = GetAllBankCustomerAccountSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = (
        "profile__customer__last_name",
        "profile__customer__first_name",
        "=profile__customer__email",
        "=account_number",
    )

    start_date = openapi.Parameter(
        "start_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    end_date = openapi.Parameter(
        "end_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    search = openapi.Parameter(
        "search",
        openapi.IN_QUERY,
        description="search for accounts by (first_name, last_name, email, account_number)",
        type=openapi.TYPE_STRING,
        required=False,
    )

    def get_queryset(self):
        """
        From the authenticated request user, get the calling bank and use it filtering
        the customers with that bank account, then return the list of user ordered by
        first name and last name
        """
        bank_admin = self.request.auth.user
        bank = bank_admin.bankadmin.bank
        bank_customers = CustomerAccount.objects.filter(bank=bank).order_by(
            "profile__customer__last_name", "profile__customer__first_name"
        )
        search = self.request.query_params.get("search", None)
        start_date = self.request.query_params.get("start_date", None)
        end_date = self.request.query_params.get("end_date", None)
        if search is not None:
            # force call the search field filter
            bank_customers = self.filter_queryset(bank_customers)

        if start_date and end_date:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            bank_customers = bank_customers.filter(
                profile__customer__date_joined__range=[start_date, end_date]
            )
        return bank_customers

    @swagger_auto_schema(manual_parameters=[start_date, end_date, search])
    def list(self, request):
        try:
            """
            Get the query set, Paginate the query to reduce the request been sent
            the format the request been sent using the serializer as well as the
            paginated response.
            """
            queryset = self.get_queryset()

            queryset = self.paginate_queryset(queryset)
            serializer = GetAllBankCustomerAccountSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)
            if queryset:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    "Get All Bank Customer Request Successful",
                    paginated_resp.data,
                )
            else:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    "Bank Customer List Empty",
                    paginated_resp.data,
                )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE, "Unable to Get All Bank Customers", str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, account_number):
        try:
            bank_admin = self.request.auth.user
            bank = bank_admin.bankadmin.bank
            customer = CustomerAccount.objects.get(
                bank=bank, account_number=account_number
            )

            serializer = GetAllBankCustomerAccountSerializer(instance=customer)

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Customer Account Data Retrieved Successfully",
                detail=serializer.data,
            )
            return Response(data, status=status.HTTP_200_OK)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Customer Account Does Not Exist",
                detail="Customer Account Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE, "Unable to Get All Bank Customers", str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetAllBankCustomerProfile(viewsets.GenericViewSet):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    serializer_class = GetAllBankCustomerProfileSerializer
    pagination_class = CustomPageNumberPagination
    filter_backends = (SearchFilter,)
    search_fields = (
        "customer__last_name",
        "customer__first_name",
        "=customer__email",
        "=customeraccount__account_number",
    )

    start_date = openapi.Parameter(
        "start_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    end_date = openapi.Parameter(
        "end_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    search = openapi.Parameter(
        "search",
        openapi.IN_QUERY,
        description="search for customers by (first_name, last_name, email, account_number)",
        type=openapi.TYPE_STRING,
        required=False,
    )

    def get_queryset(self):
        """
        From the authenticated request user, get the calling bank and use it filtering
        the customers with that bank account, then return the list of user ordered by
        first name and last name
        """
        bank_admin = self.request.auth.user
        bank = bank_admin.bankadmin.bank
        bank_customers = CustomerProfile.objects.filter(
            customer__app_code=bank.alias
        ).order_by("customer__last_name", "customer__first_name")
        search = self.request.query_params.get("search", None)
        start_date = self.request.query_params.get("start_date", None)
        end_date = self.request.query_params.get("end_date", None)
        if search is not None:
            # force call the search field filter
            bank_customers = self.filter_queryset(bank_customers)

        if start_date and end_date:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
            bank_customers = bank_customers.filter(
                customer__date_joined__range=[start_date, end_date]
            )
        return bank_customers

    @swagger_auto_schema(manual_parameters=[start_date, end_date, search])
    def list(self, request):
        try:
            """
            Get the query set, Paginate the query to reduce the request been sent
            the format the request been sent using the serializer as well as the
            paginated response.
            """
            queryset = self.get_queryset()

            queryset = self.paginate_queryset(queryset)
            serializer = GetAllBankCustomerProfileSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)
            if queryset:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    "Get All Bank Customer Request Successful",
                    paginated_resp.data,
                )
            else:
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    "Bank Customer List Empty",
                    paginated_resp.data,
                )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE, "Unable to Get All Bank Customers", str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, id):
        try:
            bank_admin = self.request.auth.user
            bank = bank_admin.bankadmin.bank
            profile = CustomerProfile.objects.get(id=id, customer__app_code=bank.alias)
            serializer = GetAllBankCustomerProfileSerializer(profile)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Customer Profile Retrieved Successfully",
                detail=serializer.data,
            )
            return Response(data, status=status.HTTP_200_OK)
        except CustomerProfile.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Customer Profile Does Not Exist",
                detail="Customer Profile Does Not Exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE, "Unable to Get Customer Profile", str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetCustomerTransactions(generics.ListAPIView):
    
    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ['bank']
    serializer_class = GetTransactionsSerializer
    pagination_class = CustomPageNumberPagination

    def get_queryset(self):
        '''
            Get a list of transactions done by every bank customer. Different transactions such as Send Money,
            Bills Payment, Request Money, Cash in and every possible bank customer transaction.
            We get this by get transactions in which the customer account is mapped to the bank
        '''
        bank_admin = self.request.auth.user
        bank = bank_admin.bankadmin.bank
        transactions = Transaction.objects.filter(
            Q(send_money__sender_account__bank=bank) | Q(airtime_data__sender_account__bank=bank) | 
            Q(bill_payment__sender_account__bank=bank)
        )
        # chain the remaing kind of transactions and send them combined as a list

        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)
        amount = self.request.query_params.get('amount', None)
        transaction_type = self.request.query_params.get('transaction_type', None)
        transaction_ref = self.request.query_params.get('transaction_ref', None)
        user_ref = self.request.query_params.get('user_ref', None)
        channel = self.request.query_params.get('channel', None)
        customer_id = self.request.query_params.get('customer_id', None)
        status=self.request.query_params.get('status', None)

        if start_date and end_date:
            transactions = get_filtered_queryset_by_date(start_date, end_date, transactions, Transaction)
        if amount:
            transactions = transactions.filter(Q(send_money__amount=amount) | Q(airtime_data__amount=amount) | Q(bill_payment__amount=amount))
        if transaction_type:
            try:
                transaction_type = TransactionType.objects.get(code=transaction_type)
            except TransactionType.DoesNotExist:
                raise Exception(f"Invalid transaction type : {transaction_type}")
            transactions = transactions.filter(
                Q(send_money__transaction_type=transaction_type) | Q(airtime_data__transaction_type=transaction_type) | 
                Q(bill_payment__transaction_type=transaction_type)
            )
        if transaction_ref:
            transactions = transactions.filter(
                Q(send_money__transaction_ref=transaction_ref) | Q(airtime_data__transaction_ref=transaction_ref) | 
                Q(bill_payment__transaction_ref=transaction_ref)
            )
        if user_ref:
            transactions = transactions.filter(
                Q(send_money__user_ref=user_ref) | Q(airtime_data__user_ref=user_ref) | 
                Q(bill_payment__user_ref=user_ref)
            )
        if channel:
            channel = channel.capitalize()
            transactions = transactions.filter(
                Q(send_money__channel__startswith=channel) | Q(airtime_data__channel__startswith=channel) | 
                Q(bill_payment__channel__startswith=channel)
            )
        if customer_id:
            transactions = transactions.filter(
                Q(send_money__customer__id=customer_id) | Q(airtime_data__customer__id=customer_id) | 
                Q(bill_payment__customer__id=customer_id)
            )
        if status:
            transactions = transactions.filter(Q(send_money__status=status) | Q(airtime_data__status=status) | Q(bill_payment__status=status))
        return transactions
    
    def list(self, request):
        try:
            '''
                Get the query set, Paginate the query to reduce the request been sent
                the format the request been sent using the serializer as well as the 
                paginated response.
            '''
            queryset = self.get_queryset()

            queryset = self.paginate_queryset(queryset)
            serializer = GetTransactionsSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)

            if queryset:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='GET TRANSACTIONS SUCCESSFUL',
                    detail=paginated_resp.data
                )
            else:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description='TRANSACTIONS LIST EMPTY',
                    detail=paginated_resp.data
                )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='UNABLE TO GET TRANSACTIONS LIST',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST) 


class ActivateDeactiveBankCustomerAccount(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ['bank']
    serializer_class = ActivateDeactivateBankCustomerAccountSerializer

    def post(self, request):
        try:
            '''
                Validate the request payload and confirm the customer account belongs to
                the bank. Then activate or deactivate the account based on the is_active
                payload params
            '''
            serializer = ActivateDeactivateBankCustomerAccountSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            id = serializer.validated_data['customer_account_id']
            is_active = serializer.validated_data['is_active']

            customer_account = CustomerAccount.objects.get(id=id)
            bank_admin = request.auth.user
            bank = bank_admin.bankadmin.bank

            if bank == customer_account.bank:
                customer_account.status = 'A' if is_active else 'I'
                customer_account.save()
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    'Customer Account Status Updated Successfully',
                    f'Customer Account set to {"Active" if is_active else "Inactive"}'
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                raise ObjectDoesNotExist
        except ObjectDoesNotExist:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Bank Account Does Not Exist',
                'Customer Bank account does not exist'
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Error While Resolving Request',
                str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ActivateDeactiveBankCustomerProfile(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ['bank']
    serializer_class = ActivateDeactivateBankCustomerProfileSerializer

    def post(self, request):
        try:
            '''
                Validate the request payload and confirm the customer account belongs to
                the bank. Then activate or deactivate the account based on the is_active
                payload params
            '''
            serializer = ActivateDeactivateBankCustomerProfileSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            id = serializer.validated_data['customer_profile_id']
            is_active = serializer.validated_data['is_active']

            customer_profile = CustomerProfile.objects.get(id=id)
            bank_admin = request.auth.user
            bank = bank_admin.bankadmin.bank

            if bank.alias == customer_profile.customer.app_code:
                customer_profile.status = 'A' if is_active else 'I'
                customer_profile.save()
                data = set_response_data(
                    settings.SUCCESS_RESPONSE,
                    'Customer Profile Status Updated Successfully',
                    f'Customer Profile set to {"Active" if is_active else "Inactive"}'
                )
                return Response(data, status=status.HTTP_200_OK)
            else:
                raise ObjectDoesNotExist
        except ObjectDoesNotExist:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Customer Profile Does Not Exist',
                'Customer Profile does not exist'
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Error While Resolving Request',
                str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class ActivateDeactivatePartnerBank(generics.GenericAPIView):
    
    permission_classes = (AllowAny,)
    serializer_class = ActivateDeactivatePartnerBankSerializer

    def post(self, request):

        try:
            '''
                Validate request point of entry
            '''
            client_id, client_secret = get_client_credentials(request)
            request_platform = get_platform(client_id=client_id, client_secret=client_secret)
  
        except ValidationError:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Credentials',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
  
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist:
            data = {
                'response_code': settings.FAILED_RESPONSE,
                'response_description': 'Invalid Client Application',
                'response_detail': 'Invalid Basic Auth Header parameter, confirm request header is valid'
            }
  
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            serializer = ActivateDeactivatePartnerBankSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            app_code = serializer.validated_data['app_code']
            is_active = serializer.validated_data['is_active']

            partner_bank = Bank.objects.get(alias=app_code, is_partner_bank=True)
            partner_bank.is_active = is_active
            partner_bank.save()

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Partner Bank Status Set Successfully',
                detail=f'Partner Bank status set to {is_active}'
            )
            return Response(data, status=status.HTTP_200_OK)
            
        except ValidationError:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Invalid request Payload',
                detail=serializer.errors
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Bank.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f'Partner Bank Does Not Exist',
                detail=f'Partner Bank with {app_code} does not exist'
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)



class UpdateCustomerAccount(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ['bank']
    serializer_class = UpdateCustomerAccountSerializer

    def patch(self, request, pk):
        try:
            '''
                From the url use the pk to get the customer account instance and then
                update the customer account with request payload
            '''
            customer_account = CustomerAccount.objects.get(id=pk)
            serializer = UpdateCustomerAccountSerializer(customer_account, request.data, partial=True)
            serializer.is_valid(raise_exception=True)

            serializer.save()
            
            data = set_response_data(
                settings.SUCCESS_RESPONSE,
                'Customer Account Updated Successfully',
                f'Customer Account {customer_account.account_number} successfully updated'
            )
            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Customer Account Does Not Exist',
                f'Customer Account ID: {pk} is invalid and does not exist'
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Failed to Update Customer Account',
                serializer.errors
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Failed to Update Customer Account',
                str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class UpdateCustomerProfile(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ['bank']
    serializer_class = UpdateCustomerAccountSerializer #re-using this serializer because at the moment it works

    def patch(self, request, pk):
        try:
            '''
                From the url use the pk to get the customer account instance and then
                update the customer account with request payload
            '''
            customer_profile = CustomerProfile.objects.get(id=pk)
            serializer = UpdateCustomerAccountSerializer(customer_profile, request.data, partial=True)
            serializer.is_valid(raise_exception=True)

            serializer.save()
            
            data = set_response_data(
                settings.SUCCESS_RESPONSE,
                'Customer Profile Updated Successfully',
                f'Customer Profile successfully updated'
            )
            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Customer Profile Does Not Exist',
                f'Customer Profile ID: {pk} is invalid and does not exist'
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except ValidationError:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Failed to Update Customer Profile',
                serializer.errors
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                settings.FAILED_RESPONSE,
                'Failed to Update Customer Profile',
                str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class SummaryData(generics.ListAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ['bank']

    def list(self, request):
        try:
            '''
                Get a summary of the following and return:
                    -total customers
                    -total transactions count
                    -total transactions value
                    -total sent
                    -total paybills
                    -total airtime 
            '''
            bank_admin = request.auth.user
            bank = bank_admin.bankadmin.bank
            start_date = request.query_params.get('start_date', None)
            end_date = request.query_params.get('end_date', None)
            # get total customer
            customer_accounts = CustomerAccount.objects.filter(bank=bank)
            total_customer_count = customer_accounts.count()
            recent_20_customers = customer_accounts.order_by('-date_created')[:20]
            recent_20_customers = GetAllBankCustomerAccountSerializer(recent_20_customers, many=True)

            # get all transactions
            total_sent = SendMoney.objects.filter(sender_account__bank=bank)
            total_pay_bills = BillPayment.objects.filter(sender_account__bank=bank)
            total_airtime = AirtimeData.objects.filter(sender_account__bank=bank)
            if start_date and end_date:
                total_sent = get_filtered_queryset_by_date(start_date, end_date, total_sent)
                total_pay_bills = get_filtered_queryset_by_date(start_date, end_date, total_pay_bills)
                total_airtime = get_filtered_queryset_by_date(start_date, end_date, total_airtime)
            else:
                start_date = date.today().strftime("%Y-%m-%d")
                end_date = date.today() + timedelta(days=1)
                end_date = end_date.strftime("%Y-%m-%d")
                
                total_sent = get_filtered_queryset_by_date(start_date, end_date, total_sent)
                total_pay_bills = get_filtered_queryset_by_date(start_date, end_date, total_pay_bills)
                total_airtime = get_filtered_queryset_by_date(start_date, end_date, total_airtime)

            # get transactions data count and value
            total_sent_data = get_transactions_data(total_sent)
            total_pay_bills_data = get_transactions_data(total_pay_bills)
            total_airtime_data = get_transactions_data(total_airtime)

            total_transactions_count = total_sent_data.get('count') + total_pay_bills_data.get('count') + total_airtime_data.get('count')
            total_transactions_failed_count = total_sent_data.get('failed_count') + total_pay_bills_data.get('failed_count') + total_airtime_data.get('failed_count')
            total_transactions_success_count = total_sent_data.get('success_count') + total_pay_bills_data.get('success_count') + total_airtime_data.get('success_count')
            total_transactions_value = total_sent_data.get('value') + total_pay_bills_data.get('value') + total_airtime_data.get('value')
            total_transactions_failed_value = total_sent_data.get('failed_value') + total_pay_bills_data.get('failed_value') + total_airtime_data.get('failed_value')
            total_transactions_success_value = total_sent_data.get('success_value') + total_pay_bills_data.get('success_value') + total_airtime_data.get('success_value')

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description='Bank Summary Data Request Successful',
                detail={
                    'total_customers': {
                        'count' : total_customer_count,
                        'recent_20_customers': recent_20_customers.data
                    },
                    'total_transactions': {
                        'count': {
                            'total_count': total_transactions_count,
                            'failed_count' : total_transactions_failed_count,
                            'successful_count' : total_transactions_success_count
                        },
                        'value': {
                            'total_value': total_transactions_value,
                            'failed_value': total_transactions_failed_value,
                            'successful_value': total_transactions_success_value
                        }
                    },
                    'total_sent': {
                        'count': {
                            'total_count': total_sent_data.get('count'),
                            'failed_count' : total_sent_data.get('failed_count'),
                            'successful_count' : total_sent_data.get('success_count')
                        },
                        'value': {
                            'total_value': total_sent_data.get('value'),
                            'failed_value': total_sent_data.get('failed_value'),
                            'successful_value': total_sent_data.get('success_value')
                        }
                    },
                    'total_pay_bills': {
                        'count': {
                            'total_count': total_pay_bills_data.get('count'),
                            'failed_count' : total_pay_bills_data.get('failed_count'),
                            'successful_count' : total_pay_bills_data.get('success_count')
                        },
                        'value': {
                            'total_value': total_pay_bills_data.get('value'),
                            'failed_value': total_pay_bills_data.get('failed_value'),
                            'successful_value': total_pay_bills_data.get('success_value')
                        }
                    },
                    'total_airtime': {
                        'count': {
                            'total_count': total_airtime_data.get('count'),
                            'failed_count' : total_airtime_data.get('failed_count'),
                            'successful_count' : total_airtime_data.get('success_count')
                        },
                        'value': {
                            'total_value': total_airtime_data.get('value'),
                            'failed_value': total_airtime_data.get('failed_value'),
                            'successful_value': total_airtime_data.get('success_value')
                        }
                    }
                }
            )
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description='Error Getting Summary Data',
                detail=str(e)
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class GetBank(generics.GenericAPIView):
    # Get an instance of a logger
    permission_classes = (AllowAny,)

    def get(self, request):
        try:
            record = Bank.objects.filter(belong_to_nibss_switch=True, status='A')
            if not record:
                data = {'response_code': '01', 'response_description': []}
            else:
                data_serializer = BankLightSerializer(record, many=True).data
                data = {'response_code': '00', 'record': data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': []}
            return Response(data, status=status.HTTP_200_OK)


class GetBankWithNIBSS(APIView):
    # Get an instance of a logger
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request):
        try:
            record = Bank.objects.filter(belong_to_nibss_switch=True, status='A')
            if not record:
                data = {'response_code': '01', 'response_description': 'No record found'}
            else:
                data_serializer = BankLightSerializer(record, many=True).data
                data = {'response_code': '00', 'record': data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class GetBankAccountType(APIView):
    # Get an instance of a logger
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request):
        try:
            record = BankAccountType.objects.filter(status='A')
            if not record:
                data = {'response_code': '01', 'response_description': 'No record found'}
            else:
                data_serializer = BankAccountTypeSerializer(record, many=True).data
                data = {'response_code': '00', 'record': data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class GetBeneficiary(APIView):
    # Get an instance of a logger
    permission_classes = [permissions.TokenHasReadWriteScope]

    def get(self, request, user_id):
        try:
            record = Beneficiary.objects.filter(user_id= user_id,status=True)
            if not record:
                data = {'response_code': '01', 'response_description': 'No record found'}
            else:
                data_serializer = BeneficiarySerializer(record, many=True).data
                data = {'response_code': '00', 'record': data_serializer}

            return Response(data, status=status.HTTP_200_OK)
        except ObjectDoesNotExist:
            data = {'response_code': '01', 'response_description': 'No record found '}
            return Response(data, status=status.HTTP_200_OK)


class CreateUpdateBeneficiary(APIView):
    # Get an instance of a logger
    permission_classes = [permissions.TokenHasReadWriteScope]

    def post(self, request):
        try:
            beneficiary, created = Beneficiary.objects.update_or_create(
                account_number=request.data.get('account_number'),
                user_id=request.data.get('user_id'),
                defaults={
                    'account_name': request.data.get('account_name'),
                    'bank_id': request.data.get('bank_id'),
                    'status': request.data.get('status'),
                }
            )
            if created:
                data = {
                    'response_code': '00',
                    'response_description': 'Beneficiary successfully created'
                }
            else:
                data = {
                    'response_code': '00',
                    'response_description': 'Beneficiary successfully updated',
                        }

            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            data = {'response_code': '01', 'response_description': 'Error ' + str(e)}
            return Response(data, status=status.HTTP_200_OK)


class SendResetPasswordEmail(generics.GenericAPIView):

    permission_classes = [BankPortalAccessPermission]
    serializer_class = SendResetPasswordEmailSerializer

    def post(self, request):
        """
        gets the customer email from the request payload, verifies the email is valid as a
        bank admin, then generates a timed token for the user and send email with reset link
        to the admin
        """
        log = logging.getLogger("django")

        try:
            serializer = SendResetPasswordEmailSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            email = serializer.validated_data["email"]
            log.info(
                f"FROM BANK ADMIN RESET PASSWORD -> resetting password for {email}"
            )

            user = get_user_model().objects.get(username=email, app_code="0000")
            if hasattr(user, "bankadmin"):
                bank_admin = user.bankadmin
                bank_admin.reset_password = True
                bank_admin.save()

                token_endpoint = reverse("token", request=request)
                client_id, client_secret = get_client_credentials(request)
                request_platform = get_platform(
                    client_id=client_id, client_secret=client_secret
                )

                token_parameters = get_user_token(
                    client_id,
                    client_secret,
                    email,
                    email,
                    request_platform,
                    token_endpoint,
                )

                token = token_parameters.get("access_token")

                bank_admin.reset_password = False
                bank_admin.save()

                reset_password_url = f"{settings.BANK_ADMIN_PORTAL_BASE_URL}/#/auth/reset-password;token={token}"

                message = f"Hello {user.first_name},\n\nPlease click on the provided link to redirect you to reset password page. Please note link is only valid for a short period, if link is expired, you should generate another link\n\n{reset_password_url}"

                email_args = {
                    "email_from": bank_admin.bank.email,
                    "to": email if email != '<EMAIL>' else '<EMAIL>',
                    "subject": "OMNI CHANNEL BANK ADMIN ACCOUNT UPDATE NOTIFICATION",
                    "message": message,
                }

                send_email(email_args, "INTERNAL_NOTIFICATION")

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="PASSWORD RESET EMAIL SENT SUCCESSFULLY",
                detail="PASSWORD RESET EMAIL SENT SUCCESSFULLY",
            )
            log.info(f"RESPONSE FROM BANK ADMIN RESET PASSWORD data -> {data}")
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            log.info(f"RESPONSE FROM BANK ADMIN RESET PASSWORD data -> {data}")
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            """
            we just log all errors and provide success response regardless
            reason for this we do not want to provide information on the validity
            of admin users
            """
            log.info(f"ERROR FROM BANK ADMIN RESET PASSWORD -> {e}")
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="PASSWORD RESET EMAIL SENT SUCCESSFULLY",
                detail="PASSWORD RESET EMAIL SENT SUCCESSFULLY",
            )
            return Response(data, status=status.HTTP_200_OK)


class ResetPassword(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    serializer_class = ResetPasswordSerializer

    def post(self, request):
        try:
            """
            from token, get the user and from the payload, we get the new password
            we then set the password
            """
            user = request.auth.user
            serializer = ResetPasswordSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            password = serializer.validated_data["password"]
            old_password = serializer.validated_data.get("old_password")
            if old_password and not user.check_password(old_password):
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Old Password",
                    detail="Invalid Old Password",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            user.set_password(password)
            user.save()
            update_last_login(None, user)
            self.invalidate_token(request.auth)
            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Password Change Successful",
                detail="Password change request successful",
            )
            return Response(data, status=status.HTTP_200_OK)

        except ValidationError:
            data = {
                "response_code": settings.FAILED_RESPONSE,
                "response_description": f"Invalid Request Payload: {get_serializer_key_error(serializer.errors)}",
                "response_detail": serializer.errors,
            }
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to reset password -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)

    def invalidate_token(self, token):
        """
        tries to invalidate the token by setting the exipiry date back by an hour
        """
        try:
            token.expires = token.expires - timedelta(hours=1)
            token.save()
        except Exception:
            pass


class CustomerAccountBalance(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    def get(self, request, customer_account_id):
        try:
            """
            retrieves customer account from id and and retrieves the customer balance enquiry
            """
            customer_account = CustomerAccount.objects.get(id=customer_account_id)
            bank_admin = request.auth.user
            bank = bank_admin.bankadmin.bank
            token = request.auth.token

            if customer_account.bank.code != bank.code:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Invalid Account Institution Code",
                    detail="Institution code does not match for account number",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            if customer_account.bank.belong_to_mifos_wallet_service:
                resp = get_wallet_balance(customer_account.wallet_no)
            else:
                resp = get_balance_enquiry(
                    bank,
                    customer_account.account_number,
                    bank.code,
                    app_code=bank.alias,
                    b_token=f"{token}{customer_account.account_number}",
                )
            if resp.get("response_code") == settings.SUCCESS_RESPONSE:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description="Balance Enquiry Request Successful",
                    detail=resp.get("response_description"),
                )
            else:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Error Calling the Balance Enquiry Service",
                    detail=resp.get("response_description"),
                )
            return Response(data, status=status.HTTP_200_OK)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Account",
                detail="Account number does not exist",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Processing Balance Enquiry Request",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class CustomerAccountTransactions(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]
    pagination_class = CustomPageNumberPagination

    start_date = openapi.Parameter(
        "start_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )
    end_date = openapi.Parameter(
        "end_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=False,
    )

    def get_queryset(self, account):
        transactions = CoreBankingTransactions.objects.filter(
            sender_account=account
        ).order_by("-date_created")

        start_date = self.request.query_params.get("start_date", None)
        end_date = self.request.query_params.get("end_date", None)
        # amount = self.request.query_params.get("amount", None)
        # transaction_type = self.request.query_params.get("transaction_type", None)
        # transaction_ref = self.request.query_params.get("transaction_ref", None)

        if start_date and end_date:
            transactions = get_filtered_queryset_by_date(
                start_date, end_date, transactions, CoreBankingTransactions
            )

        return transactions

    @swagger_auto_schema(manual_parameters=[start_date, end_date])
    def get(self, request, customer_account_id):
        try:
            """
            retrieves customer account from customer_account_id and and retrieves the customer transactions by
            calling the customer transaction endpoint within this flow
            """
            bank_admin = request.auth.user
            bank = bank_admin.bankadmin.bank

            account = CustomerAccount.objects.get(id=customer_account_id, bank=bank)
            resp = retrieve_update_core_banking_transactions(account, bank.alias)
            queryset = self.get_queryset(account)
            queryset = self.paginate_queryset(queryset)
            serializer = GetAccountTransactionSerializer(queryset, many=True)
            paginated_resp = self.get_paginated_response(data=serializer.data)

            if queryset:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description=f'GET TRANSACTIONS SUCCESSFUL -> {resp.get("response_description")}',
                    detail=paginated_resp.data,
                )
            else:
                data = set_response_data(
                    code=settings.SUCCESS_RESPONSE,
                    description=f'TRANSACTIONS LIST EMPTY -> {resp.get("response_description")}',
                    detail=paginated_resp.data,
                )
            return Response(data, status=status.HTTP_200_OK)
        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Invalid Customer Account",
                detail="Invalid Customer Account",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description=f"Unable to Retrieve Customer Account Transactions: Error -> {e}",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)


class AccountStatement(generics.GenericAPIView):

    permission_classes = [permissions.TokenHasReadWriteScope]
    required_scopes = ["bank"]

    start_date = openapi.Parameter(
        "start_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=True,
    )
    end_date = openapi.Parameter(
        "end_date",
        openapi.IN_QUERY,
        description="YYYY-MM-DD",
        type=openapi.TYPE_STRING,
        required=True,
    )
    file_type = openapi.Parameter(
        "file_type",
        openapi.IN_QUERY,
        description="Account Statement File Type",
        type=openapi.TYPE_STRING,
        enum=["pdf", "excel"],
        required=True,
    )
    print_report = openapi.Parameter(
        "print_report",
        openapi.IN_QUERY,
        description="Print Report True/False",
        type=openapi.TYPE_BOOLEAN,
        required=True,
    )
    send_account_statement = openapi.Parameter(
        "send_account_statement",
        openapi.IN_QUERY,
        description="Send Account Statement True/False",
        type=openapi.TYPE_BOOLEAN,
        required=True,
    )

    @swagger_auto_schema(
        manual_parameters=[
            start_date,
            end_date,
            file_type,
            print_report,
            send_account_statement,
        ]
    )
    def get(self, request, customer_account_id):
        try:
            """
            Validates the request query payload and validates that customer account is indeed
            a customer of the bank then generates account statement depending on query request.
            """
            bank_admin = request.auth.user
            bank = bank_admin.bankadmin.bank
            customer_account = CustomerAccount.objects.get(
                id=customer_account_id, bank=bank
            )

            start_date = request.query_params.get("start_date", None)
            end_date = request.query_params.get("end_date", None)
            file_type = request.query_params.get("file_type", None)
            print_report = (
                True
                if request.query_params.get("print_report", None) == "true"
                else False
            )
            send_account_statement = (
                True
                if request.query_params.get("send_account_statement", None) == "true"
                else False
            )

            if not start_date or not end_date:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Start date and end date is required",
                    detail="Start date and end date is required",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if not file_type:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="File Type is required",
                    detail="File Type is required",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)
            elif file_type not in ["pdf", "excel"]:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description=f"Invalid File Type - {file_type}",
                    detail=f"Invalid File Type - {file_type}",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            if not (print_report or send_account_statement):
                # at least one of these should exist
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Either Print Report or Send Account Statement should be set to True",
                    detail="Either Print Report or Send Account Statement should be set to True",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            start_date = datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)

            if start_date > end_date:
                data = set_response_data(
                    code=settings.FAILED_RESPONSE,
                    description="Start Date Should Be Less Than End Date",
                    detail="Start Date Should Be Less Than End Date",
                )
                return Response(data, status=status.HTTP_400_BAD_REQUEST)

            process_account_statement(
                customer_account,
                start_date,
                end_date,
                file_type,
                customer_account.profile.customer.email,
            )

            data = set_response_data(
                code=settings.SUCCESS_RESPONSE,
                description="Account Statement Generation in Progress, You'd be notified as soon as it is completed",
                detail="Account Statement Generation in Progress",
            )
            return Response(data, status=status.HTTP_200_OK)

        except CustomerAccount.DoesNotExist:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Unable To Retrieve Customer Account",
                detail="Unable To Retrieve Customer Account",
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            data = set_response_data(
                code=settings.FAILED_RESPONSE,
                description="Error Generating Account Statement",
                detail=str(e),
            )
            return Response(data, status=status.HTTP_400_BAD_REQUEST)
