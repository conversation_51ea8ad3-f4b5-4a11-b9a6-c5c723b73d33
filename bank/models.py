import base64
from django.conf import settings

from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


class Bank(models.Model):
    class Meta:
        db_table = 'omni_bank'
        verbose_name = "Bank"
        verbose_name_plural = "Banks"
        ordering = ['name']

    def __str__(self):
        return self.name

    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    name = models.CharField(max_length=200)
    code = models.CharField(max_length=20, unique=True, blank=True, null=True)
    nip_code = models.CharField(max_length=20, unique=True, blank=True, null=True)
    institution_number = models.CharField(
        max_length=20, unique=True, blank=True, null=True
    )
    alias = models.Char<PERSON>ield(max_length=10, unique=True, blank=True, null=True, db_index=True)
    ussd_code = models.Char<PERSON>ield(max_length=10, unique=True, blank=True, null=True, db_index=True)
    utility_account = models.CharField(max_length=15, unique=True, blank=True, null=True, db_index=True)
    address = models.CharField(max_length=250, blank=True, null=True)
    email = models.EmailField(max_length=254, blank=True, null=True)
    phone_number = models.CharField(max_length=200, blank=True, null=True)
    website = models.CharField(max_length=250, blank=True, null=True)
    logo_img = models.ImageField(upload_to='bank_logo', blank=True, null=True)
    belong_to_nambuit_switch = models.BooleanField(default=False)
    belong_to_nibss_switch = models.BooleanField(default=False)
    belong_to_mifos_wallet_service = models.BooleanField(default=False)
    limit_for_soft_token = models.DecimalField(max_digits=10, decimal_places=2, default='20000.00')
    belongs_to_nibss_core_banking = models.BooleanField(default=False)
    belongs_to_dsgs_core_banking = models.BooleanField(default=False)
    belongs_to_bank_one = models.BooleanField(default=False)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )
    is_partner_bank = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    message_sender_name = models.CharField(max_length=20, blank=True, null=True)
    email_img_url = models.URLField(blank=True, null=True)
    email_foreground_color = models.CharField(max_length=10, blank=True, null=True)
    email_background_color = models.CharField(max_length=10, blank=True, null=True)
    savings_account_limit = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    current_account_limit = models.DecimalField(max_digits=10, decimal_places=2, default='0.00')
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    def get_logo_base64(self):
        '''
            Retieves the Image logo file and converts it to base 64 and returns it
        '''
        if self.logo_img is not None:
            with open(self.logo_img.path, 'rb') as img_file:
                img_encode = base64.b64encode(img_file.read())
                img_encode = str(img_encode, 'utf-8')
            return img_encode
        return None
    
    def clean_fields(self, exclude=None) -> None:
        super().clean_fields(exclude=exclude)
        # if bool(self.code) != bool(self.belong_to_nambuit_switch):
        #     # XOR to make sure both exist together or not at all
        #     raise ValidationError({
        #     'code': _('If code exists, it must belong to nambuit switch'),
        #     'belong_to_nambuit_switch': _('If belong_to_nambuit_switch is set to True, it must have a valid code')
        # })
        # if bool(self.nip_code) != bool(self.belong_to_nibss_switch):
        #     # XOR to make sure both exist together or not at all
        #     raise ValidationError({
        #     'nip_code': _('If code exists, it must belong to nibss switch'),
        #     'belong_to_nibss_switch': _('If belong_to_nibss_switch is set to True, it must have an nip code')
        # })
        true_count = sum([
            self.belongs_to_nibss_core_banking, 
            self.belong_to_nambuit_switch,
            self.belongs_to_dsgs_core_banking,
            self.belongs_to_bank_one,
        ])

        # Ensure exactly one of the fields is True
        if true_count != 1:
            raise ValidationError(
                "One of 'belong_to_nambuit_switch', 'belongs_to_nibss_core_banking', or 'belngs_to_dsgs_core_banking' must be True."
            )
        
        if self.is_partner_bank:
            partner_details = {
                'alias': self.alias, 
                'address': self.address, 
                'email': self.email, 
                'phone_number': self.phone_number, 
                'website': self.website, 
                'logo': self.logo_img,
                'message_sender_name': self.message_sender_name,
                'savings_account_limit': self.savings_account_limit,
                'current_account_limit': self.current_account_limit,
                'ussd_code': self.ussd_code
            }
            for key in partner_details.keys():
                if not partner_details[key]:
                    raise ValidationError({
                    key : _(f'{key.capitalize()} must exist for partnered banks')
                })
        for field in [self.email_img_url, self.email_foreground_color, self.email_background_color]:
            if field and not self.is_partner_bank:
                raise ValidationError({
                    field: _(f'{field} can only exist for partnered banks')
                })
        for field in [self.email_foreground_color, self.email_background_color]:
            if self.email_img_url and not field:
                raise ValidationError({
                    field: _(f'{field} should exist with a valid email image url')
                })
        if self.savings_account_limit < 0:
            raise ValidationError({
                'savings_account_limit' : _('Account limit can not be less than 0.00')
            })
        if self.current_account_limit < 0:
            raise ValidationError({
                'current_account_limit' : _('Account limit can not be less than 0.00')
            })



class BankAccountType(models.Model):

    def __str__(self):
        return self.name


    ACTIVE = 'A'
    INACTIVE = 'I'
    STATUS_CHOICES = (
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
    )

    name = models.CharField(max_length=200)
    code = models.CharField(max_length=15, unique=True)
    status = models.CharField(
        max_length=2,
        choices=STATUS_CHOICES,
        default=ACTIVE,
    )

    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = 'omni_bank_account_type'
        verbose_name = "Bank Account Type"
        verbose_name_plural = "Bank Account Type"


class Beneficiary(models.Model):

    def __str__(self):
        return self.account_name

    account_name = models.CharField(max_length=200, blank=False)
    account_number = models.CharField(max_length=15, blank=False)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, blank=True, null=True
    )
    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, blank=True, null=True)
    status = models.BooleanField(default=True)
    transactions_count = models.PositiveIntegerField(default=0)
    date_created = models.DateTimeField('date published', auto_now_add=True, blank=True, null=True)

    class Meta:
        db_table = 'omni_beneficiary'
        unique_together = (('user', 'account_number'),)
        verbose_name = "Beneficiary"
        verbose_name_plural = "Beneficiaries"

