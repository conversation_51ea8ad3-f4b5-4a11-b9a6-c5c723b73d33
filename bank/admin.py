from django.contrib import admin

# Register your models here.
from bank.models import Bank, BankAccountType, Beneficiary


class BankAdmin(admin.ModelAdmin):
    search_fields = ['name', 'code']
    list_filter = ['date_created', 'is_partner_bank']
    list_display = ('name', 'code', 'nip_code', 'is_partner_bank', 'alias', 'status', 'ussd_code', 'date_created', "utility_account")

    fieldsets = (
        (
            "GENERAL BANK CONFIG",
            {
                "fields": (
                    "name",
                    "code",
                    "nip_code",
                    "alias",
                    "address",
                    "email",
                    "phone_number",
                    "website",
                    "logo_img",
                    "status",
                    
                )
            },
        ),
        (
            "PARTNER BANK CONFIG NOTE: (logo img should have already being uploaded)",
            {
                "fields": (
                    "belongs_to_nibss_core_banking",
                    "belongs_to_dsgs_core_banking",
                    "belong_to_nambuit_switch",
                    "belongs_to_bank_one",
                    "belong_to_nibss_switch",
                    "belong_to_mifos_wallet_service",
                    "institution_number",
                    "limit_for_soft_token",
                    "is_partner_bank",
                    "is_active",
                    "message_sender_name",
                    "email_img_url",
                    "email_foreground_color",
                    "email_background_color",
                    "savings_account_limit",
                    "current_account_limit",
                    "ussd_code",
                    "utility_account",
                )
            },
        ),
    )




class BankAccountTypeAdmin(admin.ModelAdmin):
    search_fields = ['name', 'code']
    list_filter = ['date_created']
    list_display = ('name', 'code', 'status', 'date_created')


class BeneficiaryAdmin(admin.ModelAdmin):
    search_fields = ['account_name', 'account_number', 'user__username']
    list_filter = ['account_name', 'account_number', 'date_created']
    list_display = ('user', 'account_name', 'account_number', 'status', 'date_created')


admin.site.register(Bank, BankAdmin)
admin.site.register(BankAccountType, BankAccountTypeAdmin)
admin.site.register(Beneficiary, BeneficiaryAdmin)


