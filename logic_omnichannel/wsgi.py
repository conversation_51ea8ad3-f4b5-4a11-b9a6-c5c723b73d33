"""
WSGI config for logic_omnichannel project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/howto/deployment/wsgi/
"""

import os

from logic_omnichannel.settings.base_settings import APP_ENV
from django.core.wsgi import get_wsgi_application


if APP_ENV == "production":
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'logic_omnichannel.settings.production')
elif APP_ENV == "UAT":
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'logic_omnichannel.settings.uat')
else:
     os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'logic_omnichannel.settings.local')


application = get_wsgi_application()
