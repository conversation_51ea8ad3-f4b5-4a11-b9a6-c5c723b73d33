"""logic_omnichannel URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.conf import settings
from django.urls import path, include
from django.conf.urls.static import static

from rest_framework import permissions
import oauth2_provider.views as oauth2_views
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

schema_view = get_schema_view(
   openapi.Info(
      title="LOGIC OMNI CHANNEL API",
      default_version='v1',
      description="API Documentation for Logic Omni Channel",
      terms_of_service="https://www.logicshift.com/policies/terms/",
      contact=openapi.Contact(email="<EMAIL>"),
      license=openapi.License(name="LogicShift License"),
   ),
   public=True,
   permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('user_profile/', include('user_profile.urls')),
    path('customer/', include('customer.urls')),
    path('common/', include('common.urls')),
    path('bill_payment/', include('bill_payment.urls')),
    path('billing/', include('billing.urls')),
    path('bank/', include('bank.urls')),
    path('transactions/', include('transactions.urls')),
    path('crm/', include('crm.urls')),
    path("mfs/", include("mfs.urls")),
    path("dfa/", include("dfa.urls")),
    path("nxpay/", include("nxpay.urls")),
    path("verdant/", include("verdant.urls")),
    path("channel_services/", include("channel_services.urls")),
    path("ussd_interface/", include("ussd_interface.urls")),
    path("reporting/", include("reporting.urls")),
    path("ledger_manager/", include("ledger_manager.urls")),
    path('token/', oauth2_views.TokenView.as_view(), name='token'),
    path('o/revoke_token/', oauth2_views.RevokeTokenView.as_view(), name='revoke_token'),
    path('o/', include('oauth2_provider.urls', namespace='oauth2_provider')),
    path('dev/documentation', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

admin.site.site_header = "LOGIC OMNI CHANNEL"
admin.site.site_title = "LOC"
admin.site.index_title = "WELCOME TO LOGIC SHIFT OMNI CHANNEL CONSOLE"
