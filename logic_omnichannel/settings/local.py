from .base_settings import *

INSTALLED_APPS += ["django_extensions"]

DEBUG = True

DATABASES["default"]["USER"] = env("USER")

ALLOWED_HOSTS = ["*"]

OAUTH2_PROVIDER["ACCESS_TOKEN_EXPIRE_SECONDS"] = 60000
OAUTH2_PROVIDER["REFRESH_TOKEN_EXPIRE_SECONDS"] = 90000
OAUTH2_PROVIDER["REFRESH_TOKEN_GRACE_PERIOD_SECONDS"] = 18000


USSD_AUTH_ID = "4qIPkppmeyGaQkzluF8BgppARggJBQsFAfN38ug5"

USSD_AUTH_SEC = "v2MoZMrSzmYkrosfNcvwDsTePbCb4ff1G2A3gD9qVwW0fgaeERdeP3uzol4YFbXgCr1PNJuEc29sgQU6Rpv8QCFNe2ovyq7cpXtomUYdONYkmDDKxp4MQp3WzBnG4KdF"
