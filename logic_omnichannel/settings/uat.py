from .base_settings import *

# import pymysql
# pymysql.install_as_MySQLdb()

DEBUG = True

DATABASES["default"]["USER"] = env("USER")

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://{env('REDIS_HOST')}:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}

ALLOWED_HOSTS = ["*"]


#--------------------- AWS S3 Config----------------------------------------
# USE THIS FOR LOCAL MACHINE SET UP AND RUN S3 NINJA CONTAINER

# AWS_ACCESS_KEY_ID = "AKIAIOSFODNN7EXAMPLE"
# AWS_SECRET_ACCESS_KEY = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
# AWS_STORAGE_BUCKET_NAME = "logicshift-uat-storage"
# AWS_S3_ENDPOINT_URL = "http://localhost:9444/"

# USE FOR LIVE AWS SETUP
AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "gZULM2eqqVYvt/MXbYdCfpVqeRwTdAyqV12Lwrib"
AWS_STORAGE_BUCKET_NAME = "logicshift-uat-storage"
AWS_S3_CUSTOM_DOMAIN = '%s.s3.amazonaws.com' % AWS_STORAGE_BUCKET_NAME

# ---------------------------------------------------------------------------

MEDIA_URL = f"{AWS_S3_CUSTOM_DOMAIN}media/"
DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"
AWS_S3_FILE_OVERWRITE = False