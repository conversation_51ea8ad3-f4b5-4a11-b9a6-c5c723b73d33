"""
Django settings for logic_omnichannel project.

Generated by 'django-admin startproject' using Django 3.1.7.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/3.1/ref/settings/
"""

from pathlib import Path
import environ

import pymysql
pymysql.install_as_MySQLdb()

# Initialise environment variables
env = environ.Env()
environ.Env.read_env()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent
LOG_DIR = BASE_DIR.parent / 'logs'



# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env('SECRET_KEY')

SERVICE_SK = env('SERVICE_SK')

FLEX_KEY = env('FLEX_KEY')

PB_KEYS = {
    'chmfb': env('chmfb'),
    'ancmfb': env('ancmfb'),
    'ammfb': env('ammfb'),
    'snbmfb': env('snbmfb'),
    'gabmfb': env('gabmfb'),
    'glmfb': env('glmfb'),
    'ekmfb': env('ekmfb'),
    'fhmfb': env('fhmfb'),
}

S_G_KEY = env('S_G_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False

# DataFlair #Logging Information
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': "[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s",
            'datefmt': "%d/%b/%Y %H:%M:%S"
        },
        'simple': {
        'format': '%(asctime)s OMNI_CHANNEL_PRODUCTION_SERVER OMNI_CHANNEL: %(message)s',
        'datefmt': '%Y-%m-%dT%H:%M:%S',
    },
    },
    'handlers': {
        'SysLog': {
        'level': 'DEBUG',
        'class': 'logging.handlers.SysLogHandler',
        'formatter': 'simple',
        'address': ('logs.papertrailapp.com', 31177)
        },
        'bvn_verification_logfile': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'bvn_logfile.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'create_user_logfile': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'create_user_logfile.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'login_logfile': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'login_logfile.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'send_money_logfile': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'send_money_logfile.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'airtime_data_logfile': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'airtime_data_logfile.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'bill_payment_logfile': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'bill_payment_logfile.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        'billing_logfile': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': LOG_DIR / 'billing_logfile.log',
            'maxBytes': 50000,
            'backupCount': 2,
            'formatter': 'standard',
        },
        "international_send_money_logfile": {
            "level": "DEBUG",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": LOG_DIR / "international_send_money_logfile.log",
            "maxBytes": 50000,
            "backupCount": 2,
            "formatter": "standard",
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'SysLog'],
            'level': 'INFO',
            'propagate': True,
        },
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
        'omni_bvn': {
            'handlers': ['console', 'bvn_verification_logfile', 'SysLog'],
            'level': 'DEBUG',
        },
        'omni_create_user': {
            'handlers': ['console', 'create_user_logfile', 'SysLog'],
            'level': 'DEBUG',
        },
        'omni_login': {
            'handlers': ['console', 'login_logfile', 'SysLog'],
            'level': 'DEBUG',
        },
        'omni_send_money': {
            'handlers': ['console', 'send_money_logfile', 'SysLog'],
            'level': 'DEBUG',
        },
        'omni_airtime_data': {
            'handlers': ['console', 'airtime_data_logfile', 'SysLog'],
            'level': 'DEBUG',
        },
        'omni_bill_payment': {
            'handlers': ['console', 'bill_payment_logfile', 'SysLog'],
            'level': 'DEBUG',
        },
        'omni_billing': {
            'handlers': ['console', 'billing_logfile', 'SysLog'],
            'level': 'DEBUG',
        },
        "omni_international_send_money": {
            "handlers": ["console", "international_send_money_logfile", "SysLog"],
            "level": "DEBUG",
        },
    }
}


ALLOWED_HOSTS = ['www.simpayswitch.com', 'simpayswitch.com', '*************']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # 3RD PARTY APPS
    'rest_framework',
    'oauth2_provider',
    'drf_yasg',
    'django_countries',
    'corsheaders',
    "django_celery_beat",
    "crispy_forms",
    "crispy_bootstrap4",
    # LOCAL APPS
    'user_profile.apps.UserProfileConfig',
    'common.apps.CommonConfig',
    'customer.apps.CustomerConfig',
    'bank.apps.BankConfig',
    'bill_payment.apps.BillPaymentConfig',
    'transactions.apps.TransactionsConfig',
    'billing.apps.BillingConfig',
    'crm.apps.CrmConfig',
    "reporting.apps.ReportingConfig",
    "mfs.apps.MfsConfig",
    "dfa.apps.DfaConfig",
    "verdant.apps.VerdantConfig",
    "nxpay.apps.NxpayConfig",
    "channel_services.apps.ChannelServicesConfig",
    "ussd_interface.apps.UssdInterfaceConfig",
    "ledger_manager.apps.LedgerManagerConfig",
]

#--------------------- AWS S3 Config----------------------------------------

AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "F6gkGVI4Ge1Vh1j3hPOXQDHqhlk0WdgcGn2rdIuV"
# ---------------------------------------------------------------------------

SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'Basic': {
            'type': 'basic'
        },
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        },
    }
}

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'logic_omnichannel.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'logic_omnichannel.wsgi.application'


# Database
# https://docs.djangoproject.com/en/3.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'OPTIONS': {
            'init_command': 'SET default_storage_engine=INNODB',
        },
        'NAME': env('NAME'),
        'USER': env("USER"),
        'PASSWORD': env('PASSWORD'),
        'HOST': env('HOST'),
        'PORT': env('PORT'),
    }
}

CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}


OAUTH2_PROVIDER = {
    'SCOPES_BACKEND_CLASS' : 'oauth2_provider.scopes.SettingsScopes',

    # this is the list of available scopes
    'SCOPES': {
        'read': 'Read scope', 
        'write': 'Write scope', 
        'groups': 'Access to your groups',
        'bank': 'Access to bank admin portal'
    },
    'DEFAULT_SCOPES' : ['read', 'write'],

    'ACCESS_TOKEN_EXPIRE_SECONDS': 600,
    'REFRESH_TOKEN_EXPIRE_SECONDS': 900,
    'REFRESH_TOKEN_GRACE_PERIOD_SECONDS': 180,
    
}


# Rest framework
REST_FRAMEWORK = {
    # Use Django's standard `django.contrib.auth` permissions,
    # or allow read-only access for unauthenticated users.
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'oauth2_provider.contrib.rest_framework.OAuth2Authentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20
}


# Password validation
# https://docs.djangoproject.com/en/3.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


CORS_ORIGIN_ALLOW_ALL = True

# Internationalization
# https://docs.djangoproject.com/en/3.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Africa/Lagos'

USE_I18N = True

USE_L10N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.1/howto/static-files/

STATIC_URL = '/static/'

STATIC_ROOT = BASE_DIR / 'static'

MEDIA_URL = '/media/'

MEDIA_ROOT = BASE_DIR / 'media_root'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CELERY CONFIGURATION

CELERY_BROKER_URL = "redis://localhost:6379"
CELERY_RESULT_BACKEND = "redis://localhost:6379"
CELERY_ACCEPT_CONTENT = ["application/json"]
CELERY_RESULT_SERIALIZER = "json"
CELERY_TASK_SERIALIZER = "json"
CELERY_TIMEZONE = "UTC"
DJANGO_CELERY_BEAT_TZ_AWARE = False

# OPTIONS

AUTH_USER_MODEL = 'user_profile.User'

AUTHENTICATION_BACKENDS = [
    'user_profile.backends.CustomUserBackend',
    'django.contrib.auth.backends.ModelBackend'
]

# VARIABLES

TOKEN_EXPIRY = 5

SUCCESS_RESPONSE = '00'

FAILED_RESPONSE = '01'

INVALID_PIN_RESPONSE = '02'

DAILY_LIMIT_RESPONSE = '03'

PENDING_RESPONSE = "04"

TRANSACTION_STATUS_CODE = {
    "S": ("00", "SUCCESS"),
    "F": ("01", "FAILED"),
    "P": ("04", "PENDING"),
    "R": ("05", "REVERSED"),
}

CORE_BANKING_RESPONSE_CODE = {"96": "EXCEPTIONAL ERROR, PLEASE TRY AGAIN LATER."}

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

DEFAULT_FROM_EMAIL = env('DEFAULT_FROM_EMAIL')

EMAIL_HOST = env('EMAIL_HOST')

EMAIL_HOST_USER = env('EMAIL_HOST_USER')

EMAIL_PORT = 587

EMAIL_USE_TLS = True

SMS_URL = env('SMS_URL')

SWITCH_BASE_URL = env('SWITCH_BASE_URL')

NAMBUIT_SERVICE_GATEWAY = env('NAMBUIT_SERVICE_GATEWAY')

BILL_PAYMENT_ITEMS_URL = env('BILL_PAYMENT_ITEMS_URL')

BILL_CUSTOMER_VALIDATION_URL = env('BILL_CUSTOMER_VALIDATION_URL')

BILL_ADVICE_URL = env('BILL_ADVICE_URL')

EMAIL_VERIFICATION_SERVICE_URL = env('EMAIL_VERIFICATION_SERVICE_URL')

BVN_SERVICE_BASE_URL = env('BVN_SERVICE_BASE_URL')

MONEY_FLEX_OPEN_API_BASE_URL = env('MONEY_FLEX_OPEN_API_BASE_URL')

NAMBUIT_API_BASE_URL = env('NAMBUIT_API_BASE_URL')

PAYSTACK_PAYMENT_VERIFY_URL = env("PAYSTACK_PAYMENT_VERIFY_URL")

SMS_SERVICE_URL = env('SMS_SERVICE_URL')

EMAIL_DEFAULT_IMAGE_URL = env('EMAIL_DEFAULT_IMAGE_URL')

EMAIL_DEFAULT_FOREGROUND = env('EMAIL_DEFAULT_FOREGROUND')

EMAIL_DEFAULT_BACKGROUND = env('EMAIL_DEFAULT_BACKGROUND')

BILL_PAYMENT_SERVICE_BASE_URL = env('BILL_PAYMENT_SERVICE_BASE_URL')

MFS_BASE_URL = env("MFS_BASE_URL")

BANK_ADMIN_PORTAL_BASE_URL = env("BANK_ADMIN_PORTAL_BASE_URL")

WALLET_SERVICE_BASE_URL = env("WALLET_SERVICE_BASE_URL")

NIP_OUTWARD_NAME_ENQUIRY_BASE_URL = env("NIP_OUTWARD_NAME_ENQUIRY_BASE_URL")

NQR_SERVICE_BASE_URL = env("NQR_SERVICE_BASE_URL")

APP_ENV = env("APP_ENV")

NXPAY_SERVICE_BASE_URL = env("NXPAY_SERVICE_BASE_URL")

UAT_BILL_PAYMENT_SERVICE_BASE_URL = env("UAT_BILL_PAYMENT_SERVICE_BASE_URL")

AGENCY_BANKING_BASE_URL = env("AGENCY_BANKING_BASE_URL")

CRISPY_TEMPLATE_PACK = "bootstrap4"

CRISPY_TEMPLATE_PACK = "bootstrap4"

USSD_AUTH_BASE_URL = 'https://simpayswitch.com/user_profile/login/'

TEMPLATE_CONTEXT_PROCESSORS = 'django.core.context_processors.request'

USSD_AIRTIME_BASE_URL = 'https://simpayswitch.com/transactions/airtime_data/'

USSD_AUTH_ID = 'WYMgzdOIDBRFG7lRiz3W4Qr2a7ru6xfsdQoA81b3'

USSD_AUTH_SEC = 'AjlOmXdcxCYob1rrHo5liCLM2mR4DtrJZfpf7ypVhXfZ5UojvIcwKKzROvGbpAENYXnaNwAVzeo0KCZGQYMjJr7MgR9ScykMGUpYHyH98XwuQLmvIX6oj1qAqYRhgbEI'

LM_AUTH_ID = 'Ida3n8KjEV7zQsEXWUcxB0ehId4DQ3n3tT5wUSDm'

LM_AUTH_SEC = 'jTZNt3H8E3s2bU9VOJzugcIqqA0biPz0VmQNe5IwHYerK29zwrAz8OQZxFdFujNBRntljGA8bBKFiMWxzXmKR9Sw3uBQgg63qACrpva5908quO5m4XztS50MUDbnEiDY'

# -------------------NIBSS-----------------------#

NIBSS_SECRET_KEY= env("NIBSS_SECRET_KEY")

BANK_ONE_API_URL = env("BANK_ONE_API_URL")

BANK_ONE_API_KEY = env("BANK_ONE_API_KEY")

NIBSS_BASE_URL = env("NIBSS_BASE_URL")

NIBSS_QA_BASE_URL = env("NIBSS_QA_BASE_URL")

DSGS_BASE_URL = env("DSGS_BASE_URL")
