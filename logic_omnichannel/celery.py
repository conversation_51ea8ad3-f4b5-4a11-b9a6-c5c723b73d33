from __future__ import absolute_import, unicode_literals

import os

from celery import Celery
from logic_omnichannel.settings.base_settings import APP_ENV

# set the default Django settings module for the 'celery' program.

if APP_ENV == "production":
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'logic_omnichannel.settings.production')
elif APP_ENV == "UAT":
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'logic_omnichannel.settings.uat')
else:
     os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'logic_omnichannel.settings.local')



app = Celery("logic_omnichannel")

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object("django.conf:settings", namespace="CELERY")

# Load task modules from all registered Django app configs.
app.autodiscover_tasks()


@app.task(bind=True)
def debug_task(self):
    print(f"Hello From debugger")


@app.task
def hello():
    print("Hello from Logic Omni Channel")


@app.task(bind=True)
def debug_task(self):
    print("Request: {0!r}".format(self.request))
