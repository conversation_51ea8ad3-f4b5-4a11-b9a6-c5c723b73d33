"""
ASGI config for logic_omnichannel project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/3.1/howto/deployment/asgi/
"""

import os
from logic_omnichannel.settings.base_settings import APP_ENV

from django.core.asgi import get_asgi_application

if APP_ENV == "production":
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'logic_omnichannel.settings.production')
elif APP_ENV == "UAT":
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'logic_omnichannel.settings.uat')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'logic_omnichannel.settings.local')


application = get_asgi_application()
