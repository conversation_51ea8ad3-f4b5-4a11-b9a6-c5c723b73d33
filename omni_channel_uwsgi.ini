[uwsgi]

# full path to Django project's root directory
chdir            = /home/<USER>/logic_omni_channel/logic_omni_channel_production
# Django's wsgi file
module           = logic_omnichannel.wsgi
# full path to python virtual env
home             = /home/<USER>/.local/share/virtualenvs/logic_omni_channel_production-dbk99D_j

# enable uwsgi master process
master          = true
# maximum number of worker processes
processes       = 5
# the socket (use the full path to be safe
socket          = /home/<USER>/logic_omni_channel/logic_omni_channel_production/logic_omnichannel.sock
# socket permissions
chmod-socket    = 666
# clear environment on exit
vacuum          = true
# daemonize uwsgi and write messages into given log
daemonize       = /home/<USER>/logic_omni_channel/uwsgi-emperor.log
